---
description: 单元测试规则
globs: *.test.js
alwaysApply: false
---
# ** 写单元测试 **

## 默认使用中文回复

## 规则如下：


1. 参考@priceservice的测试用例

2. 创建 testmock/mock.js，参考@priceservice下的 mock.js，把测试用例中所需要的所有静态数据和字段映射，都储存到这个文件中

3. 忽略以下方法调用，不用写测试用例
    - dataGetter下的方法
    - getDetailFields
    - getAllFields
    - getDetailFields
    - PPM.的方法
    - 目标项目中，方法名重复的写一个就可以
         
4. 核心方法优先覆盖

5. 当测试不通过时，不允许修改源文件，只能调整单元测试代码

6. 自动执行用例，每个文件的单元测试覆盖率不低于 80%

7. 整理测试用例中重复使用方法和静态数据放到 testmock/mock.js 中，减少代码量  
