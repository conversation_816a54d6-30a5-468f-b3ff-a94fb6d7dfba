/**
 * @desc: 多币种插件
 * @author: wang<PERSON><PERSON>
 * @date: 12/29/21
 */
// import PPM from 'plugin_public_methods'
import Base from 'plugin_base'

export default class Currency extends Base {

    options() {
        return {
            defMasterFields: {
                form_mc_currency: 'mc_currency', // 客户id
                // form_partner_id: 'partner_id',  // 合作伙伴id
                // form_price_book_id: 'price_book_id',  // 价目表id
            },
            defMdFields: {
                // product_price: 'price',                         // 价格
                // discount: 'discount',	                // 折扣
                // price_book_product_id: 'price_book_product_id',  // 开启价目表，替换价目表产品id
                // product_id: 'product_id',               //
                // attribute: 'attribute',
                // attribute_json: 'attribute_json',       //
                // attribute_price_book_id: "attribute_price_book_id",         // 属性价目表名称
            },
        }
    }

    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _beforeRender(plugin, param) {


    }
    // 处理计算参数，子件计算时，不计算预制字段
    _parseParamForBomFields(pp, param, mdList) {
        if (!param?.dataGetter) {
            console.warn('dataGetter is not available');
            return pp;
        }

        // 不计算 bom 子件的预制字段
        const bomFields = this.getPluginFields('bom');
        if(!bomFields) return pp;
        const {
            parent_prod_pkg_key,
            product_price,
            quantity,
            extra_discount,
            subtotal,
            price_book_discount,
            product_group_id,
            price_book_price,
            price_book_subtotal,
            sales_price
        } = bomFields;

        let fieldList = [
            product_price,
            quantity,
            extra_discount,
            subtotal,
            price_book_discount,
            product_group_id,
            price_book_price,
            price_book_subtotal,
            sales_price
        ];

        fieldList = fieldList.filter(f => f);
        
        // 初始化 excludedDetailCalculateFields
        pp.excludedDetailCalculateFields = pp.excludedDetailCalculateFields || {};              
        
        mdList.forEach(mdApiName => {
            const details = param.dataGetter.getDetail(mdApiName) || [];
            const filterChildren = details.reduce((acc, child, index) => {
                if(child?.[parent_prod_pkg_key]){
                    acc[index] = fieldList.map(f => ({
                        "fieldName": f,
                        "order": 1
                    }));
                }
                return acc;
            }, {});
            
            if(Object.keys(filterChildren).length){
                pp.excludedDetailCalculateFields[mdApiName] = {
                    ...filterChildren
                };
            }
        });
        
        return pp;
    }

    _dataChange_after(plugin, param) {
        return new Promise(async resolve => {
            let field = Object.keys(param.changeData)[0];
            let {
                form_mc_currency
            } = this.getAllFields();
            let isDef = param.autoFields?.mc_currency; // 设置默认值触发的不处理
            if (field === form_mc_currency && !isDef) {
                this._runBomCal = true;
                let oldData = param.oldData[form_mc_currency];
                let newData = param.changeData[form_mc_currency];
                if (oldData === newData) return resolve();
                let mdList = this.getAllMdApiName();
                let hasData = false;
                mdList.forEach(mdApiName => {
                    let details = param.dataGetter.getDetail(mdApiName);
                    if (details && details.length) hasData = true;
                })
                if (!hasData) return resolve();
                let priceServicePlugin = this.pluginService.api.getPlugins().find(item => item.pluginApiName === "price-service");
                let priceServiceApiNames = priceServicePlugin ? priceServicePlugin.params.details.map(item=> item.objectApiName) : [];
                // 每次迭代中等待异步操作完成后再进行下一次迭代
                for (const mdApiName of mdList) {
                    if (priceServiceApiNames.includes(mdApiName)) {
                        await param.run('price-service.getRealPriceAndCalculate', {
                            data: param.dataGetter.getDetail(mdApiName),
                            mdApiName,
                            actionFrom: '',
                            masterApiName: param.masterObjApiName,
                            masterData: param.dataGetter.getMasterData(),
                            showAlert: false,
                            param
                        })
                    }
                }

                resolve({
                    parseParam: (pp) => this._parseParamForBomFields(pp, param, mdList)
                })
            }else{
                resolve()
            }
        })
    }

    /**
     * @desc 从xx添加数据时，开了多币种要走取价
     * @param plugin
     * @param param
     * @returns {{isDoPriceService: boolean}}
     * @private
     */
    _batchAddAfter_before(plugin, param) {
        if (param.isFromProductOrPriceBook) {
            return {
                isDoPriceService: true
            }
        }
    }

    // _formUieventBefore(plugin, param) {
    //     console.log('formUieventBefore')
    // }

    /**
     * 币种变化之后需要触发 bom 计算；
     * 此方法适用于两个场景，1、映射，没有映射币种，底层会添加一个币种，再添加完之后需要走bom 计算；2、只有修改币种字段，才走 bom 计算； 
     */
    async _formDataChangeEnd(plugin, param) {
        if (!this._runBomCal) {
            return
        }
        this._runBomCal = false;
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;
        let mdList = this.getAllMdApiName();
        let hasData = false;

        mdList.forEach(mdApiName => {
            let details = param.dataGetter.getDetail(mdApiName);
            if (details && details.length) hasData = true;
        });
        if (!hasData) return;

        const bomPlugin = this.pluginService.api.getPlugins().find(item => item.pluginApiName === "bom");
        if (!bomPlugin?.params?.details) return;

        let {parent_prod_pkg_key} = this.getPluginFields('bom');

        const bomApiNames = bomPlugin.params.details.map(item => item.objectApiName);
        // 每次迭代中等待异步操作完成后再进行下一次迭代
        for (const mdApiName of mdList) {
            if (bomApiNames.includes(mdApiName)) {
                let data = param.dataGetter.getDetail(mdApiName);
                if (!data?.length) continue;
                let changeRow = data.filter(c => c[parent_prod_pkg_key]);
                await this.runPlugin('bom.queryBomAndCalculate', {
                    obj: {
                        data,
                        masterData,
                        masterApiName,
                        mdApiName,
                        recordType: data[0]?.recordType,
                        changeRow,
                    },
                    param
                })
            }
        }
    }

    getHook() {
        return [
            {
                event: 'form.dataChange.after',
                functional: this._dataChange_after.bind(this)
            }, {
                event: 'price-service.batchAddAfter.before',
                functional: this._batchAddAfter_before.bind(this)
            }, 
            // {
            //     event: 'form.uievent.before',
            //     functional: this._formUieventBefore.bind(this)
            // },
            {
                event: 'form.change.end',
                functional: this._formDataChangeEnd.bind(this)
            },

        ];
    }

}