{"name": "price_policy", "version": "960.0.4", "develop-ver": "960.0.4", "bugfix-ver": "950.0.13", "description": "价格政策", "main": "dist/index.js", "scripts": {"release_old": "babel src --out-dir dist && npm publish", "build": "webpack --mode production --config webpack.product.config.js", "release": "webpack --mode production --config webpack.product.config.js && npm publish --registry https://registry-npm.firstshare.cn", "test": "jest -coverage --all", "debugger": "node --inspect-brk ./node_modules/jest/bin/jest --runInBand --no-cache --no-watchman"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.21.0", "@types/jest": "^29.5.0", "babel-loader": "^9.1.2", "globals": "^16.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "plugin_public_methods": "^930.0.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "tslib": "^2.4.0", "typescript": "^4.9.5", "webpack": "^5.70.0", "webpack-cli": "^4.9.2"}, "dependencies": {"@babel/runtime": "^7.21.0", "plugin_base": "^900.0.1"}}