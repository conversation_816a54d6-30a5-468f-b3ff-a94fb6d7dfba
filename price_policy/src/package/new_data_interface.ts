
//match接口入参
export interface ReqMatchFace {
    batchNo: string;
    requestId: string;
    matchType: string;
    masterObjectApiName: string;
    accountId: string;
    masterData: {
        account_id: string;
        [props: string]: any;
    };
    detailDataMap: {
        [prod_pkg_key: string]: DetailDataFace;
    };
    modifiedDataIndexList: Array<string>;
    modifiedFieldApiNames: {
        [api: string]: Array<string>;
    };
    removeGroupKeySet: Array<string>;
    exactlyMatchModifiedData: boolean;
    requestSource: string;
    pricePolicyId?: string;
    scene?:string;
}


/************ START 主从对象里政策相关数据 ************/
export type GiftMap = {
    [ruleId: string]: GiftMapItemFace
}
export interface PolicyDataFace {
    price_policy_id: string | null;   //数据本身匹配到的政策id
    price_policy_rule_ids: Array<string>; //一个政策可能有多个规则，这是匹配到的规则id数组
    master_policy_id: string | null; //数据所属的主对象匹配到的政策id
    group_key: string;
    policy_dynamic_amount: number; //促销政策优惠的金额
    gift_map: GiftMap | null;
    [key: string]: any;
}
export interface GiftListItemFace {
    is_multiple_unit: boolean;
    is_this_product: boolean;
    operator: "ADD";
    price: number;
    product_id: string;
    product_id__s: string;
    required: boolean;
    unit: string;
    unit_name: string;
    max_value: number;
    min_value: number;
    isActive?: boolean;
    [props: string]: any;
}

export interface GiftMapItemFace {
    gift_condition: string;
    gift_condition_unit__s: string;
    gift_condition_unit__id: string;
    gift_kind_upper_limit: number;  //赠品种类上限
    gift_basis: string; //quantity-按数量，amount-按金额
    gift_list: Array<GiftListItemFace>;
    gift_total_num: number;   //赠品数量上限
    object_api_name: string;
    rule_type: string;   //"gift"
    type: string;   //"OPTIONAL","FIX"
    cycle_count: number;
    group_key: string;
    hold_chose?: string;  //"1"
    cacheInit?: boolean;
}
/************ END 主从对象里政策相关数据 ************/

export interface ExecuteFace {
	masterData: MasterDataFace;

	detailDataMap: {
        [key:string]:DetailDataFace
    };
	changeInfo: changeInfoFace;
	modifyInfo: modifyInfoFace;
	policyInfo: policyInfoFace|any;
	matchArgs: ReqMatchFace;
	matchRes: ResMatchFace;
	matchFrom: string;       //区分editInit和普通match等
	oriUiValue?:any;
	calArgs?:any;    
    promoteProcessLog?:PromoteProcessLog;
    allDetailsInfo?:object;
    scene?:string; //单据场景：新建、编辑、转化等
    triggerCal?:any;
	triggerCalAndUIEvent?:any;
}

export interface changeInfoFace {
	masterUpdate: any;
	mdUpdate: any;
	mdAdd: Array<any>;
	mdDel: Array<any>;
	[props: string]: any;
}
export interface modifyInfoFace {
	modifyFields: {
		[objApi: string]: Array<string>;
	};
	modifyIndex: Array<string>;
	triggerPolicy?: boolean;
}
//主对象数据结构
export interface MasterDataFace extends PolicyDataFace {
    account_id: string;
    [props: string]: any;
}

//从对象数据结构
export interface DetailDataFace extends PolicyDataFace {
    rowId: string;            //数据的唯一标识
    dataIndex: string;
    prod_pkg_key: string;    //数据的唯一标识
    [props: string]: any;
}
export interface policyInfoFace {
	amortizeInfoMap: {
		[key: string]: object
	};
	detailPricePolicyMap: {
		[key: string]: {
			[policyId: string]: Array<string>;
		};
	};
	groupMap: {
		[groupKey: string]: Array<string>;
	};
	masterPricePolicy: {
		[key: string]: Array<string>;
	};
	pricePolicies: Array<any>;
	limit?: Array<any>;
	initChangeInfo?:any;
}

export interface PromoteProcessLog{
	requestId: string;  // 单据的唯一标识
    processId: string;   // 处理流程的唯一标识
    startTime: string;   // 开始时间（推荐ISO格式字符串或明确格式）
    endTime: string;     // 结束时间
    durationMs: number;  // 总耗时（毫秒）
    serviceName: string; // 调用的服务名称
    details: Array<PromoteLogDetail>;
}
export interface PromoteLogDetail{
	action: string;                   // 动作名称
    request: string;                  // 调用的接口
    responseStatus: string;           // 接口返回状态
    durationMs: number;               // 动作耗时
    extraInfo: Record<string, any>;   // 其他信息，更灵活的数据结构
}
/************ START match接口返回数据 ************/
type RuleId = string;
type prodPkgKey = string;
export interface ResMatchFace {
    masterData: MasterDataFace | {},
    detailDataMap: {
        [prod_pkg_key: string]: DetailDataFace
    }
    masterPricePolicy: {
        [policyId: string]: Array<RuleId>;
    };
    detailPricePolicyMap: {
        [prod_pkg_key: string]: {
            [policyId: string]: Array<RuleId>;
        };
    };
    groupMap: {
        [groupKey: string]: Array<prodPkgKey>;
    } | null;
    pricePolicies: Array<PolicyFace>;
    amortizeInfoMap: {
        [policyId_ruleId_dataIndex: string]: {
            policy_dynamic_amount: number;
            price_policy_rule_ids?: any,    //dataIndex为“master”时
            price_policy_id?: any           //dataIndex为“master”时
        }
    };
    initChangeInfo?:any;  //业务逻辑补全信息
}

/************ START 政策数据 ************/
export interface RuleFace {
    id: string;
    ruleType: string; //"pricing","gift"
    name: string;
    amortizeType: string;  //"detail"
    executeResult: string;
}


type fieldName = string;
export interface PolicyFace {
    id: string;
    modifyType: string; //"master","detail"
    name: string;
    rules: Array<RuleFace>,
    //修改量中使用的字段
    executionFieldMap: {
        [objApiName: string]: Array<fieldName>
    }
}
/************ END 政策数据 ************/


/************ END match接口返回数据 ************/