/*
 * @Description: 政策中介者类(实现和业务交互的具体方法)
 * @Author: LingJ
 * @Date: 2022-05-10
 * @LastEditors: chaoxin
 * @LastEditTime: 2023-06-13 15:02:07
 */

import PPM from 'plugin_public_methods';
import PolicyExecute from './execute';
import PolicyGift from './gift';
import PolicyAfterHandle from './after_handle';
import PolicyLimit from './limit';
import {
    configFace,
    executePolicyFace,
    changeInfoFace,
    modifyInfoFace,
    modifyPolicyFace,
    masterFieldMapFace,
    FieldMapFace,
    DecimalMapFace
} from './data_interface';

import {
    ExecuteFace
} from "./new_data_interface";

import PolicyPreCheck from './pre_check';

export default class PricePolicyImp {
    public policyConfig: configFace | null;
    public policyExecute: any;
    public policyGift: any;
    public policyPreCheck: any;
    public policyAfterHandle: any;
    public policyLimit: any;
    public cacheMatchRules: Array<string>;
    public cachePolicyMap: {
        [key: string]: string;
    } = {};
    constructor(
        public requestId: string,
        public masterApiName: string,
        public detailApiName: string,
        public fromType: string,
        public fields: {
            [api: string]: object
        },
        public recordType: string,
        public request: any,
        public getRowBasicData: any,
        public triggerCal: any,
        public triggerUIEvent: any,
        public giftAmortizeBasis: string,
        public notMatchGroupOtherRow: string,
        public masterFieldMap: masterFieldMapFace,
        public fieldMap: FieldMapFace,
        public decimalMap: DecimalMapFace,
        public events: {
            [type: string]: any
        },
        public i18n: any,
        public detailDesc: any,
        public adjustedPriceMode: boolean,
        public manualMatchMode: boolean,
        public sendLog: any,
        public systemConfig: any
    ) {
        this.policyConfig = null;
        this.cacheMatchRules = [];
        this.init();
    }

    private init() {
        this.policyExecute = new PolicyExecute(
            this.requestId,
            this.masterApiName,
            this.detailApiName,
            this.fromType,
            this.request,
            this.triggerCal,
            this.masterFieldMap,
            this.fieldMap,
            this.decimalMap,
            this.fields,
            this.recordType,
            this.notMatchGroupOtherRow,
            this.manualMatchMode,
            this.sendLog
        )
        this.policyGift = new PolicyGift(
            this.masterApiName,
            this.detailApiName,
            this.fromType,
            this.recordType,
            this.request,
            this.getRowBasicData,
            this.triggerCal,
            this.masterFieldMap,
            this.fieldMap,
            this.decimalMap,
            this.fields,
            this.giftAmortizeBasis,
            this.i18n,
            this.detailDesc,
            this.systemConfig?.allowedRange || false,
            this.systemConfig?.grayNewCalGiftFun || false
        )
        this.policyAfterHandle = new PolicyAfterHandle(
            this.masterApiName,
            this.detailApiName,
            this.fromType,
            this.recordType,
            this.request,
            this.triggerCal,
            this.triggerUIEvent,
            this.fieldMap,
            this.decimalMap,
            this.fields,
            this.events,
            this.systemConfig?.grayNewCalGiftFun || false
        )
        this.policyPreCheck = new PolicyPreCheck(
            this.masterApiName,
            this.detailApiName,
            this.request,
            this.fromType,
            this.masterFieldMap,
            this.fieldMap,
            this.adjustedPriceMode
        )
        this.policyLimit = new PolicyLimit(
            this.masterApiName,
            this.detailApiName,
            this.fromType,
            this.request,
            this.fieldMap
        )
    }
    //获取match类型，主要用于区分如何更新数据
    private getMatchFrom() {
        let matchFrom = "";
        switch (this.fromType) {
            case 'edit':   //编辑、转化订单初始化:只更新有修改/失效政策的数据，未失效的补全赠品
                matchFrom = 'reviseUpdate';
                break;
            case 'mapping':  // 转化：不更新，依赖match接口初始化
                matchFrom = 'noUpdate';
                break;
            case 'cart':  //购物车转订单:清空整单政策&组合政策
                matchFrom = 'cartUpdate';
                break;
            case 'draft':
                matchFrom = this.systemConfig?.reGetPrice ? "fullyUpdate" : "reviseUpdate"
                break;
            default:      //正常匹配:完全按匹配结果更新
                matchFrom = "fullyUpdate";
        };
        return matchFrom;
    }

    private getScene() {
        switch (this.fromType) {
            case 'mapping':  // 转化：不更新，依赖match接口初始化
                return "ConversionInit"
        }
    }
    /***********************************************************/
    /********************组合具体对象方法实现业务********************/
    /***********************************************************/
    //获取客户政策配置状态
    public async getConfigStatus(account: string) {
        this.policyConfig = await this.policyExecute.getConfigStatus(account);
        return this.policyConfig;
    }

    //编辑初始化
    public async initMdByPolicy(masterData: object, detailsMap: { [key: string]: any }, hasPricePolicy: boolean,promoteProcessLog:any, extraParam: any) {
        this.cachePolicyMap = this.collectPolicies(masterData,detailsMap);
        let data = await PPM.composeAsync(
            this.handOffPolicy.bind(this),
            PPM.curry(2, this.getInvalidInfo.bind(this))(hasPricePolicy),
        )({
            masterData: masterData,
            detailDataMap: detailsMap,
            changeInfo: {
                masterUpdate: {},
                mdUpdate: {},
                mdAdd: [],
                mdDel: []
            },
            modifyInfo: {
                modifyFields: {},
                modifyIndex: [],
            },
            policyInfo: {},
            matchFrom: this.getMatchFrom(),
            promoteProcessLog: promoteProcessLog,
            ...extraParam
        });

        if (this.policyConfig && this.policyConfig.hasPricePolicy) {
            if (this.fromType !== 'add' || Object.keys(data.detailDataMap).length >= 1) {
                data.modifyInfo.triggerPolicy = true;
            }
            const scene = this.getScene();
            if (scene) {
                data.scene = scene;
            }
            data = await this.executePolicy(data); 
        }
        this.checkPolicyChange(data);
        return data;
    }

    //执行价格政策逻辑
    public async executePolicy(param: ExecuteFace) {
        const uiEventInfo = {
            "trigger": true,
            "allDetailsInfo": param.allDetailsInfo
        };
        const data = await PPM.composeAsync(
            PPM.curry(2, this.parsePolicyRes.bind(this))(uiEventInfo),
            this.generateGifts.bind(this),
            this.matchPolicy.bind(this),
            this.beforePolicy.bind(this)
        )(param);

        return data;
    }

    //执行主对象价格政策（重算分摊）
    public async executeMasterPolicy(param: executePolicyFace) {
        const uiEventInfo = {
            "trigger": true,
            "allDetailsInfo": param.allDetailsInfo
        };
        const data = await PPM.composeAsync(
            PPM.curry(2, this.parsePolicyRes.bind(this))(uiEventInfo),
            this.generateGifts.bind(this),
            this.matchMasterPolicy.bind(this),
            this.beforePolicy.bind(this)
        )(param);
        return data;
    }

    //取消价格政策,默认触发UI事件，但作为更换政策的中间过程可以不触发
    public async cancelPolicy(param: modifyPolicyFace, noTriggerUi?: boolean) {
        const uiEventInfo = {
            "trigger": !!!noTriggerUi,
            "dataKey": param.dataKey,
            "allDetailsInfo": param.allDetailsInfo
        };
        const data = await PPM.composeAsync(
            PPM.curry(2, this.parsePolicyRes.bind(this))(uiEventInfo),
            this.generateGifts.bind(this),
            this.executeCancel.bind(this),
            this.beforePolicy.bind(this)
        )(param);
        return data;
    }

    //选择价格政策
    public async selectPolicy(param: executePolicyFace, type: string, targetPolicy: string, dataKey: string) {
        const uiEventInfo = {
            "trigger": true,
            "dataKey": dataKey,
            "allDetailsInfo": param.allDetailsInfo
        };
        const data = await PPM.composeAsync(
            PPM.curry(2, this.parsePolicyRes.bind(this))(uiEventInfo),
            this.generateGifts.bind(this),
            PPM.partial(this.executeSelect.bind(this), type, targetPolicy, dataKey),
            this.beforePolicy.bind(this)
        )(param);
        return data;
    }

    //更改价格政策赠品
    public async editPolicyGift(param: executePolicyFace) {
        const uiEventInfo = {
            "trigger": true,
            "allDetailsInfo": param.allDetailsInfo
        };
        const data = await PPM.composeAsync(
            PPM.curry(2, this.parsePolicyRes.bind(this))(uiEventInfo),
            this.modifyGifts.bind(this),
            this.beforePolicy.bind(this)
        )(param);
        return data;
    }

    //根据单位类型获取赠品
    public getGiftByUnit(gifts: Array<any>, unitType: string) {
        return this.policyGift.getGiftByUnit(gifts, unitType);
    }

    //ui事件结果更新和计算
    public async updateUiResAndCal(param: executePolicyFace, result: any) {
        const data = await PPM.composeAsync(
            this.calBatchAllData.bind(this),
            PPM.curry(2, this.updateUiRes.bind(this))(result)
        )(param);
        return data;
    }

    public getGiftPrice(param: executePolicyFace, gifts: Array<any>) {
        return this.policyGift.giftPriceByPriceBook(param, gifts);
    }

    public userEditInManualMode(modifyInfo: modifyInfoFace, detailDataMap: DecimalMapFace) {
        const isTrigger = this.policyExecute.triggerPolicyCheck(modifyInfo, detailDataMap);
        return isTrigger;
    }

    /***********************************************************/
    /**************************各对象方法*************************/
    /***********************************************************/
    //
    //获取政策失效信息
    private getInvalidInfo(hasPricePolicy: boolean, param: executePolicyFace) {
        return this.policyPreCheck.getInvalidInfo(hasPricePolicy, param)
    }
    //更新初始化数据
    public handOffPolicy(res: { param: executePolicyFace, invalidRes: any }) {
        return this.policyExecute.handOffPolicy(res.param, res.invalidRes)
    }
    //匹配价格政策
    public matchPolicy(param: executePolicyFace) {
        return this.policyExecute.executePolicy(param);
    }
    //手动匹配价格政策
    public manualExecutePolicy(param: executePolicyFace) {
        return this.policyExecute.manualExecutePolicy(param);
    }
    //匹配整单指定政策，目的重算分摊
    public matchMasterPolicy(param: executePolicyFace) {
        return this.policyExecute.executeMasterPolicy(param);
    }

    public executeCancel(param: modifyPolicyFace) {
        return this.policyExecute.cancelPolicy(param);
    }
    public executeSelect(type: string, targetPolicy: string, dataKey: string, param: modifyPolicyFace,) {
        return this.policyExecute.selectPolicy(param, type, targetPolicy, dataKey);
    }
    //生成赠品
    public generateGifts(param: executePolicyFace) {
        return this.policyGift.calGifts(param);
    }
    //编辑赠品:增删改种类/数量
    public modifyGifts(param: executePolicyFace) {
        return this.policyGift.modifyGifts(param);
    }
    //更新ui事件的结果
    public updateUiRes(res: any, param: executePolicyFace) {
        return this.policyAfterHandle.updateUiRes(param, res);
    }
    //计算全量数据
    public calBatchAllData(param: executePolicyFace) {
        return this.policyAfterHandle.calBatch(param);
    }
    //价格政策开始前处理:1.缓存当前政策规则信息
    //初始化日志数据
    public beforePolicy(param: executePolicyFace) {
        this.cacheMatchRules = this.collectRules(param.masterData, param.detailDataMap);
        this.updateTriggerCalFun(param);
        return param;
    }
    updateTriggerCalFun(param: executePolicyFace){
        this.policyExecute.triggerCal = param.triggerCal ?? this.policyExecute.triggerCal;
        this.policyGift.triggerCal = param.triggerCal ?? this.policyGift.triggerCal;
        this.policyAfterHandle.triggerCal = param.triggerCal ?? this.policyAfterHandle.triggerCal;
        this.policyAfterHandle.triggerCalAndUIEvent = param.triggerCalAndUIEvent ?? this.policyAfterHandle.triggerCalAndUIEvent;   
    }
    //价格政策结束后处理:1.格式化政策数据;2.获取限额限量信息
    public async parsePolicyRes(uiInfo: {
        trigger: boolean;
        dataKey?: string;
    }, param: executePolicyFace) {
        const result = await this.policyAfterHandle.afterHandle(param, uiInfo);
        //处理限额限量
        if (param) {
            const curMatchRules = this.collectRules(result.masterData, result.detailDataMap),
                matchRuleChange = this.isMatchRuleChange(this.cacheMatchRules, curMatchRules),
                needReqLimit = param.matchFrom !== "fullyUpdate" || matchRuleChange,  //编辑初始化等其他场景默认要请求
                limitInfo = await this.policyLimit.getLimitInfo(needReqLimit, param);

            param.policyInfo = param.policyInfo || {};
            param.policyInfo.limit = limitInfo;
            //价格政策UI事件原始信息
            param.oriUiValue = result.oriUiValue;
        }
        return param;
    }

    // 主对象政策被match detail清空，match master重新匹配的场景，需要重新校验匹配的政策是否有变化
    private checkPolicyChange(param: executePolicyFace) {
        //未开启跨对象的转换 & 完全更新的新建(如购物车)，不提示
        const mappingWithSingleObj = this.fromType == "mapping" && !this.systemConfig.allowMultipleObj;
        const addInit = this.fromType == "add" && param?.matchFrom == "fullyUpdate";
        if(!param || mappingWithSingleObj || addInit){
            return;
        }
        const { price_policy_id, product_id__r,parent_gift_key,rebate_coupon_id="rebate_coupon_id",parent_rowId="parent_rowId" } = this.fieldMap;
        const { detailDataMap = {}, changeInfo , policyInfo = {} } = param;
        const initChangeInfo: { [key: string]: any } = {};

        const masterUpdate = changeInfo?.masterUpdate ?? {};
        const mdUpdate = (changeInfo?.mdUpdate ?? {}) as { [key: string]: { [price_policy_id: string]: any } }

        //使用宽松匹配 (!=) : 避免 null 和 undefined被判断为有变化
        if (masterUpdate[price_policy_id] != this.cachePolicyMap["master"]) {
            initChangeInfo["master"] = {
                oPolicy: this.cachePolicyMap["master"],
                nPolicy: masterUpdate[price_policy_id],
                rowId: "",
                productName: $t('整单')
            }
        }

        Object.entries(mdUpdate).forEach(([key, data]) => {
            const nData = detailDataMap[key];
            if (!nData) return;

            const { 
                [price_policy_id]: nPolicyId, 
                [parent_gift_key]: parentGift, 
                [rebate_coupon_id]: rebateCoupon, 
                [parent_rowId]:isBomChild,
                [product_id__r]: productName 
            } = nData;

            const isProduct = !parentGift && !rebateCoupon && !isBomChild ; 
            
            if (isProduct && nPolicyId != this.cachePolicyMap[key]) {
                initChangeInfo[key] = {
                    oPolicy: this.cachePolicyMap[key],
                    nPolicy: nPolicyId,
                    rowId: key,
                    productName: productName ?? key
                }
            }
        });
        param.policyInfo = {
            ...policyInfo,
            initChangeInfo
        }
    }

    private isMatchRuleChange(lastRules: Array<string>, curRules: Array<string>) {
        if (curRules.length == 0) {
            return false;
        } else if (lastRules.length !== curRules.length) {
            return true;
        } else {
            let i = 0,
                flag = false;
            while (i < lastRules.length && !flag) {
                flag = curRules.findIndex((r: string) => r == lastRules[i]) <= -1;
                i++
            }
            return flag;
        }
    }

    private collectRules(masterData: any, detailDataMap: { [key: string]: any }) {
        const { price_policy_id, price_policy_rule_ids } = this.fieldMap;
        const ruleSet = new Set<string>();

        Object.entries(detailDataMap || {}).forEach(([key, data]) => {
            (data[price_policy_rule_ids] || []).forEach((ruleId: string) => ruleSet.add(ruleId));
        });

        // 添加主数据中的规则
        (masterData[price_policy_rule_ids] || []).forEach((ruleId: string) => ruleSet.add(ruleId));

        return Array.from(ruleSet)
    }

    private collectPolicies(masterData: any, detailDataMap: { [key: string]: any }){
        const { price_policy_id, price_policy_rule_ids } = this.fieldMap;
        const cachePolicyMap: { [key: string]: string } = {};
   
        Object.entries(detailDataMap || {}).forEach(([key, data]) => {
            if (data[price_policy_id]) {
                cachePolicyMap[key] = data[price_policy_id];
            }
        });
        // 添加主数据中的规则
        if (masterData[price_policy_id]) {
            cachePolicyMap["master"] = masterData[price_policy_id];
        }
        return cachePolicyMap;
    }

}
