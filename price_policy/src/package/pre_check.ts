import PPM from "plugin_public_methods";
import PolicyUtil from "./policy_util";
import {
    ValidFace,
    InvalidFace,
    resCalFace,
    masterFieldMapFace,
    FieldMapFace
} from "./data_interface";

import {ExecuteFace} from "./new_data_interface";
export default class PolicyPreCheck {
    constructor(
        public masterApiName: string,
        public detailApiName: string,
        public request: any,
        public fromType: string,
        public masterFieldMap: masterFieldMapFace,
        public fieldMap: FieldMapFace,
        public adjustedPriceMode: boolean
    ) { }

    /***********************************************************/
    /*****************初始化数据匹配价格政策业务流程*****************/
    /***********************************************************/
    //校验政策失效信息=>转换为数据信息
    public getInvalidInfo(hasPricePolicy: boolean, param: ExecuteFace): Promise<{ param: ExecuteFace, invalidRes: any }> {

        return PPM.composeAsync(
            PPM.partial(this.generateLocalRes.bind(this), param),
            PPM.partial(this.getInvalidKeys.bind(this), hasPricePolicy),
        )(param);
    }

    public getInvalidKeys(hasPricePolicy: boolean, param: ExecuteFace) {
        const { _id, price_policy_id, price_policy_rule_ids } = this.masterFieldMap,
            { masterData, detailDataMap, matchFrom} = param;

        if (this.fromType == 'add') {
            return {
                updateMaster: false,
                modifyIndex: []
            };
        }
        //非新建场景，客户已无政策
        if (!hasPricePolicy) {
            return this.invalidInfoOfNoPolicy(masterData, detailDataMap);
        }
        switch (matchFrom) {
            case 'reviseUpdate':   //修改更新
                return PPM.composeAsync(
                    PPM.partial(this.collectKeysByPolicy.bind(this), masterData, detailDataMap),
                    this.validatePolicy.bind(this),
                    this.validateAccount.bind(this),
                )({
                    "args": {
                        "objectDataId": masterData[_id],
                        "objectDescribeApiName": this.masterApiName,
                    },
                    "policyIds": []
                });
            case 'noUpdate':    //不更新
                return {
                    updateMaster: false,
                    modifyIndex: []
                };
            case 'cartUpdate': //购物车更新
                return this.invalidInfoOfNoPolicy(masterData, detailDataMap,true);
            default:            //完全更新，要检查是否有必要更新
                return this.invalidInfoOfFullyUpdate(masterData, detailDataMap);
        }
    }

    /**
     * 编辑时，客户无政策场景。仅清空需要清空的数据
     * 购物车：需要清空整单政策和组合政策
     * @param masterData //
     * @param detailDataMap 
     * @param checkGroup  是否需要校验组合政策
     * @returns 
     */
    private invalidInfoOfNoPolicy(masterData: any, detailDataMap: any, checkGroupKey:boolean = false) {
        const {  price_policy_id,group_key="group_key"  } = this.fieldMap;
        const policyIdSet = new Set();

        if(masterData[price_policy_id]){
            policyIdSet.add(masterData[price_policy_id]);
        }

        Object.values(detailDataMap).forEach((d:any)=>{
            if(d[price_policy_id] && (!checkGroupKey || d[group_key])){
                policyIdSet.add(d[price_policy_id]);
            }
        })
        const policyIds :Array<string> = Array.from(policyIdSet)  as string[];
        return this.collectKeysByPolicy(masterData, detailDataMap,policyIds)
    }


    //完全更新场景下需要更新的信息
    private invalidInfoOfFullyUpdate(masterData: any, detailDataMap: any) {
        const { _id, price_policy_id, price_policy_rule_ids } = this.masterFieldMap,
            productDetails = PolicyUtil.filterGifts(detailDataMap, this.fieldMap),
            //草稿箱还原，引用类型字段price_policy_id值会被底层清空，导致无法判断
            masterHasPolicy = masterData[price_policy_id] || (masterData[price_policy_rule_ids] && masterData[price_policy_rule_ids].length >= 1),
            detailKeys = Object.keys(productDetails),
            adjustedPriceMode = this.adjustedPriceMode;   //手工改价模式，需要清空额外调整
        let detailHasPolicy = false,
            i = 0;
        while (i < detailKeys.length && !detailHasPolicy) {
            detailHasPolicy = productDetails[detailKeys[i]][price_policy_id];
            i++
        }
        return {
            updateMaster: !!masterHasPolicy || adjustedPriceMode,
            modifyIndex: (masterHasPolicy || detailHasPolicy || adjustedPriceMode) ? detailKeys : []
        };
    }
    /***********************************************************/
    /**********************初始化数据处理方法**********************/
    /***********************************************************/
    //获取当前客户可用的有效政策
    private async validateAccount(param: ValidFace): Promise<ValidFace> {
        const url = "FHH/EM1HNCRM/API/v1/object/price_policy/service/validate_account_applies_policy",
            res = await PPM.ajax(this.request, url, param.args),
            appliesIds = (res.appliesInfo || [])
                .filter((policy: any) => !policy.isApplies)
                .map((policy: any) => policy.policyId);

        param.policyIds = param.policyIds.concat(appliesIds);
        return param;
    }

    //获取当前客户可用的有效政策
    private async validatePolicy(param: ValidFace): Promise<Array<string>> {
        const url = "FHH/EM1HNCRM/API/v1/object/price_policy/service/validate_policy_effective_or_modified",
            res = await PPM.ajax(this.request, url, param.args),
            effectiveIds = (res.policyList || [])
                .filter((policy: any) => !policy.effective || policy.modified)
                .map((policy: any) => policy.policyId);

        param.policyIds = param.policyIds.concat(effectiveIds);
        return param.policyIds;
    }
    //根据更新的政策，获取要修改的从对象行索引&主对象是否需要修改
    private collectKeysByPolicy(
        mData: any,
        dDataMap: { [key: string]: any },
        policyIds: Array<string>
    ): InvalidFace {
        const me = this,
            { price_policy_id, price_policy_rule_ids } = this.fieldMap,
            dData = Object.keys(dDataMap || {}).map((key: string) => dDataMap[key]),
            isUpdateData = (data: any) => data[price_policy_id] && policyIds.includes(data[price_policy_id]),
            //查找是否有分摊规则: 主从有相同的规则，则是分摊规则
            hasAmortizeRule = (): boolean => {
                let flag = false,
                    i = 0;
                while (i < dData.length && !flag) {
                    flag = PolicyUtil.overlapArr(mData[price_policy_rule_ids], dData[i][price_policy_rule_ids]);
                    i++;
                }
                return flag;
            };

        //主对象政策更新&有分摊规则，全部数据都更新,否则按更新政策更新
        const updateMaster = isUpdateData(mData),
            updateDetails = (updateMaster && hasAmortizeRule()) ? dData : dData.filter((data: any) => isUpdateData(data)),

            modifyIndex = updateDetails.filter((data: any) => !PolicyUtil.isPolicyGift(data, me.fieldMap))
                .map((data: any) => data.prod_pkg_key);

        return {
            updateMaster: updateMaster,
            modifyIndex: modifyIndex
        };
    }
    private generateLocalRes(param: ExecuteFace, invalidInfo: InvalidFace) {
        //无更新
        if (!invalidInfo.updateMaster && invalidInfo.modifyIndex.length <= 0) {
            return {
                param: param,
                invalidRes: null
            }
        }

        let initialVal: any = {},
            initValMap: any = {};

        //按计算接口格式拼装要更新的数据
        const { price_policy_id, price_policy_id__r, price_policy_rule_ids, policy_dynamic_amount, parent_gift_key, dynamic_amount = "dynamic_amount",group_key="group_key",amortize_amount="amortize_amount" } = this.fieldMap,
            me = this,
            { detailDataMap } = param,
            cleanData = {
                [price_policy_id]: "",
                [price_policy_id__r]: "",
                [price_policy_rule_ids]: [],
                [policy_dynamic_amount]: 0,
                [group_key]: "",
                [amortize_amount]: 0,  
                icon_fake_val: "",
                gift_map:null
            },
            cleanMasterData: any = invalidInfo.updateMaster ? cleanData : {};

        //手工改价模式，主从额外调整都清0
        if (this.adjustedPriceMode) {
            cleanData[dynamic_amount] = 0;
            if (Object.keys(cleanMasterData).length >= 1) {
                cleanMasterData[dynamic_amount] = 0;
            }
        }

        //收集需要更新数据的rowId
        const modifyRowIds:string[] = [];
        const details = Object.values(detailDataMap);
        invalidInfo.modifyIndex.forEach(key => {
            const item = detailDataMap[key];
            modifyRowIds.push(item.rowId);
            //对于需要处理的bom数据，同时清除bom子件的政策相关数据
            if (item.root_prod_pkg_key && !item.parent_prod_pkg_key) {
                const childrenIds = (PPM.getChildrenByRootKey({ rootKey: item.rowId, details: details })||[]).map(d=>d.rowId);
                modifyRowIds.push(...childrenIds);
            }
        });
        const calculateResult: any = {
            [this.masterApiName]: {
                0: cleanMasterData
            },
            [this.detailApiName]: modifyRowIds.reduce((acc: any, rowId: string) => {
                acc[rowId] = Object.assign({}, cleanData);
                return acc;
            }, initialVal)
        };

        //删除失效政策的赠品
        //复制等场景时，prod_pkg_key更新，parent_gift_key不再对应，所以删除全部赠品
        const giftParentKeys = (invalidInfo.updateMaster ? ["master"] : []).concat(invalidInfo.modifyIndex||[]),

            isDel = (data: any) => {
                return param.matchFrom !== 'fullyUpdate' ? giftParentKeys.includes(data[parent_gift_key]) : true
            },
            mdDel = Object.keys(param.detailDataMap)
                .filter((key: string) => PolicyUtil.isPolicySystemGift(param.detailDataMap[key], me.fieldMap) && isDel(param.detailDataMap[key]));

        param.detailDataMap = Object.keys(param.detailDataMap)
            .reduce((acc: any, key: string) => {
                if (!mdDel.includes(key)) {
                    acc[key] = param.detailDataMap[key];
                }
                return acc;
            }, initValMap)
        param.changeInfo.mdDel = mdDel;

        return {
            param: param,
            invalidRes: {
                Value: {
                    calculateResult
                }
            }
        }
    }

}