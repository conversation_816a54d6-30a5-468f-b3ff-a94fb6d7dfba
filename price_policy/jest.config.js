module.exports = {
    preset: 'ts-jest',
    testEnvironment: "jsdom",
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'html'],
    collectCoverage: true,
    transform: {
      '^.+\\.tsx?$': 'babel-jest', //Jest 将知道如何正确处理 TypeScript 文件
      "^.+\\.jsx?$": "babel-jest",
    },
    moduleNameMapper: {
      '^ppm$': '<rootDir>/helpers/ppm.js'  // 确保ppm模块指向你的模拟
    },
    setupFilesAfterEnv: ['<rootDir>/testSetup.js'],
  };
  