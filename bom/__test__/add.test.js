import BOM from '../src/index';
import PPM from 'plugin_public_methods';
import { render_before_param, parse_data_before_save_data } from '../mock';
import { add_edit_param, filter_cal_fields_obj, allAddData , masterData, } from '../mock/add';

// 模拟插件入参
const pluginService = {
    api: {
        request: async () => { return { Result: { StatusCode: 0 }, Value: { objectDescribe: [] } } },
        showLoading: () => {
        },
        hideLoading: () => {
        },
        alert: () => {
        },
        i18n: (x) => x,
        getPluginFields: () => { },
        getPlugins: () => [],
    },
    run: () => {
    },
    runSync:() => {},
    register: () => {
    },
    registerCommand: () => {
    },
    executeCommand: () => {
    }
};
const mdApiName = 'SalesOrderProductObj';
const pluginParam = {
    describe: {
        pluginApiName: "bom",
        objectApiName: "SalesOrderObj",
        params: {
            fieldMapping: {
            },
            details: [
                {
                    detailKey: 'bom_detail'
                }
            ]
        }
    },
    params: {
        fieldMapping: {},
        details: [
            {
                objectApiName: "SalesOrderProductObj"
            }
        ]
    },
    bizStateConfig: {
        fixedCollocationOpenStatus: true,
        bom_temp_node: true,
        // bom_delete_root: 1
    },
    triggerCalAndUIEvent:()=>{},
    triggerCal:()=>{},

};
const fields = {
    "form_account_id": "account_id",
    "form_partner_id": "partner_id",
    "form_price_book_id": "price_book_id",
    "form_mc_currency": "mc_currency",
    "product_price": "price",
    "price_book_id": "price_book_id",
    "discount": "discount",
    "price_book_product_id": "price_book_product_id",
    "product_id": "product_id",
    "quantity": "quantity",
    "price_book_price": "price_book_price",
    "price_book_discount": "price_book_discount",
    "price_book_subtotal": "price_book_subtotal",
    "sales_price": "selling_price",
    "subtotal": "total_amount",
    "unit": "quote_lines_unit",
    "amount_any": "amount_any",
    "bom_core_id": "bom_core_id",
    "bom_version": "bom_version",
    "bom_type": "bom_type",
    "related_core_id": "related_core_id",
    "new_bom_path": "new_bom_path",
    "prod_pkg_key": "prod_pkg_key",
    "bom_id": "bom_id",
    "parent_prod_pkg_key": "parent_prod_pkg_key",
    "root_prod_pkg_key": "root_prod_pkg_key",
    "product_group_id": "product_group_id",
    "is_package": "is_package",
    "max_amount": "max_amount",
    "min_amount": "min_amount",
    "increment": "increment",
    "price_editable": "price_editable",
    "amount_editable": "amount_editable",
    "price_mode": "price_mode",
    "node_discount": "node_discount",
    "node_price": "node_price",
    "parent_prod_package_id": "parent_prod_package_id",
    "node_type": "node_type",
    "temp_node_group_id": "temp_node_group_id",
    "node_no": "node_no",
    "mc_currency": "mc_currency",
    "mc_exchange_rate": "mc_exchange_rate",
    "attribute_json": "attribute_json",
    "attribute_price_book_id": "attribute_price_book_id",
    "nonstandard_attribute": "nonstandard_attribute",
    "nonstandard_attribute_json": "nonstandard_attribute_json",
    "dynamic_amount": "dynamic_amount",
    "node_subtotal": "node_subtotal",
    "share_rate": "share_rate",
    "policy_dynamic_amount": "policy_dynamic_amount",
    "policy_subtotal": "policy_subtotal",
    "policy_price": "policy_price",
    "policy_discount": "policy_discount",
    "gift_amortize_price": "gift_amortize_price",
    "gift_amortize_subtotal": "gift_amortize_subtotal",
    "extra_discount": "extra_discount",
    "selling_price": "sales_price",
    "extra_discount_amount": "extra_discount_amount",
    "system_discount_amount": "system_discount_amount",
    "sales_amount": "sales_amount",
    "total_discount": "total_discount",
    "base_subtotal": "base_subtotal",
    "base_sales_price": "base_sales_price",
    "base_delivery_amount": "base_delivery_amount",
    "base_order_product_amount": "base_order_product_amount",
    "base_sales_amount": "base_sales_amount",
    "base_total_amount": "base_total_amount",
    "base_selling_price": "base_selling_price",
    "base_product_price": "base_product_price",
    "base_price": "base_price",
    "executed_order_subtotal": "executed_order_subtotal",
    "unexecuted_order_subtotal": "unexecuted_order_subtotal",
    "unexecuted_quantity": "unexecuted_quantity",
    "executed_quantity": "executed_quantity"
}
// Mock lodash
global._ = {
    uniq: (arr, fn) => {
        if (fn) {
            const seen = new Set();
            return arr.filter(item => {
                const key = fn(item);
                if (seen.has(key)) {
                    return false;
                }
                seen.add(key);
                return true;
            });
        }
        return [...new Set(arr)];
    },
    isArray: Array.isArray,
    isEmpty: (value) => {
        if (value == null) return true;
        if (Array.isArray(value) || typeof value === 'string') return value.length === 0;
        if (typeof value === 'object') return Object.keys(value).length === 0;
        return false;
    }
};

jest.mock('plugin_public_methods', () => ({
    ...jest.requireActual('plugin_public_methods').default,
    multiplicational: () => { return 1 },
}))
global.CRM = {
    util: {
        cloneBomData: (data) => {
            return JSON.parse(JSON.stringify(data))
        },
        setChildrenAmount: () => {
        },
        isGrayScale: () => {
            return false
        },
        getConfigStatusByKey: jest.fn(() => '0')
    }
}
describe('Bom Add', () => {
    const Bom = new BOM(pluginService, pluginParam);
    const Add = Bom.Add;
    let mockRunPlugin = jest.fn();
    let mockRunPluginSync = jest.fn();
    let mockParseData = jest.fn();
    let mockGetAllFields = jest.fn();
    mockRunPlugin.mockResolvedValue({ parseData:  mockParseData});
    mockRunPluginSync.mockResolvedValue({ needCalBom:  true});
    Add.parent.runPlugin = mockRunPlugin;
    Add.parent.runPluginSync = mockRunPluginSync;
    Add.parent.sendLog = jest.fn();
    Add.parent.getAllFields = mockGetAllFields;
    mockGetAllFields.mockReturnValue(fields)
    test('getDescribe', () => {
        Add.getDescribe(mdApiName, add_edit_param)
    })
    test('_batchAddAfter', async () => {
        await Add._batchAddAfter({}, add_edit_param);
        expect(mockRunPlugin).toHaveBeenCalledWith('bom.parseAddBomData.before', expect.any(Object));
        expect(mockParseData).toHaveBeenCalled();
    })

    test('_batchAddEnd', async () => {
        Add._cacheAllAddData = [
            {parent_prod_pkg_key: 'test1'},
            {parent_prod_pkg_key: null},
            {parent_prod_pkg_key: 'test2'}
        ];

        await Add._batchAddEnd({}, add_edit_param);

        // Check that the plugin was called with the correct event name
        const calls = mockRunPlugin.mock.calls;
        const batchAddEndCall = calls.find(call => call[0] === 'bom.md.batchAdd.end');
        expect(batchAddEndCall).toBeDefined();
        expect(batchAddEndCall[1]).toEqual(expect.objectContaining({
            param: add_edit_param,
            children: expect.any(Array)
        }));
        expect(Add._cacheAllAddData).toEqual([]);
    })

    test('getAllFields', () => {
        const result = Add.getAllFields(mdApiName);
        expect(mockGetAllFields).toHaveBeenCalledWith(mdApiName);
        expect(result).toEqual(fields);
    })

    test('getMultiUnitPluginFields', () => {
        Add.parent.getPluginFields = jest.fn().mockReturnValue({field1: 'value1'});
        const result = Add.getMultiUnitPluginFields(mdApiName);
        expect(Add.parent.getPluginFields).toHaveBeenCalledWith('multi-unit', mdApiName);
        expect(result).toEqual({field1: 'value1'});
    })

    test('getPeriodProductPluginFields', () => {
        Add.parent.getPluginFields = jest.fn().mockReturnValue({field2: 'value2'});
        const result = Add.getPeriodProductPluginFields(mdApiName);
        expect(Add.parent.getPluginFields).toHaveBeenCalledWith('period_product', mdApiName);
        expect(result).toEqual({field2: 'value2'});
    })

    test('getAllPluginFields', () => {
        Add.parent.getSomePluginFields = jest.fn().mockReturnValue({field3: 'value3'});
        const result = Add.getAllPluginFields(mdApiName);
        expect(Add.parent.getSomePluginFields).toHaveBeenCalledWith(['bom', 'attribute', 'period_product', 'multi-unit'], mdApiName);
        expect(result).toEqual({field3: 'value3'});
    })

    test('isOpenAdvancePrice', () => {
        // Test when price_policy plugin exists
        pluginService.api.getPlugins = jest.fn().mockReturnValue([
            {pluginApiName: 'price_policy'},
            {pluginApiName: 'other_plugin'}
        ]);
        expect(Add.isOpenAdvancePrice()).toBeTruthy();

        // Test when price_policy plugin doesn't exist
        pluginService.api.getPlugins = jest.fn().mockReturnValue([
            {pluginApiName: 'other_plugin'}
        ]);
        expect(Add.isOpenAdvancePrice()).toBeFalsy();
    })

    test('concatChildren should concatenate children data', async () => {
        const addDatas = [{ id: 1 }, { id: 2 }];
        const lookupDatas = [{ id: 3 }];
        const param = {
            addDatas,
            mdApiName: 'TestObj',
            lookupDatas,
            recordType: 'default'
        };

        Add.requestBomPrice = jest.fn().mockResolvedValue({ data: [] });

        const result = await Add.concatChildren(param, add_edit_param, pluginService);
        expect(result).toBeInstanceOf(Array);
    })

    test('setBomSpecialFieldsVal should set special field values', () => {
        const data = [{ rowId: '1', isGroup: false }];
        const param = { objApiName: 'TestObj' };

        Add.setBomSpecialFieldsVal({ data, mdApiName: 'TestObj' }, param);
        // Test passes if no errors are thrown
        expect(true).toBe(true);
    })

    test('setFieldsReadonly should set readonly fields', () => {
        const data = [{ rowId: '1', isGroup: false, node_type: '' }];
        const param = { objApiName: 'TestObj' };

        Add.setFieldsReadonly({ data, mdApiName: 'TestObj' }, param);
        // Test passes if no errors are thrown
        expect(true).toBe(true);
    })

    test('noClearSalesPrice should return boolean', () => {
        const result = Add.noClearSalesPrice();
        expect(typeof result).toBe('boolean');
    })

    test('isEditForSubPriceOrQuantity should check edit permissions', () => {
        const item = { node_type: '', isGroup: false };
        const result1 = Add.isEditForSubPriceOrQuantity('price_editable', item);
        const result2 = Add.isEditForSubPriceOrQuantity('amount_editable', item);

        expect(typeof result1).toBe('boolean');
        expect(typeof result2).toBe('boolean');
    })
})