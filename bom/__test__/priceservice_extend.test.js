import BOM from '../src/index';
import PPM from 'plugin_public_methods';
import { render_before_param } from '../mock';
import { add_edit_param, masterData } from '../mock/add';

// 模拟插件入参
const pluginService = {
    api: {
        request: async () => { return { Result: { StatusCode: 0 }, Value: { objectDescribe: [] } } },
        showLoading: () => {},
        hideLoading: () => {},
        alert: () => {},
        i18n: (x) => x,
        getPluginFields: () => {},
        getPlugins: () => [],
    },
    run: () => {},
    runSync: () => {},
    register: () => {},
    registerCommand: () => {},
    executeCommand: () => {}
};

const mdApiName = 'SalesOrderProductObj';
const pluginParam = {
    describe: {
        pluginApiName: "bom",
        objectApiName: "SalesOrderObj",
        params: {
            fieldMapping: {},
            details: [{ detailKey: 'bom_detail' }]
        }
    },
    params: {
        fieldMapping: {},
        details: [{ objectApiName: "SalesOrderProductObj" }]
    },
    bizStateConfig: {
        fixedCollocationOpenStatus: true,
        bom_temp_node: true,
    },
    triggerCalAndUIEvent: () => {},
    triggerCal: () => {},
};

const fields = {
    "parent_prod_pkg_key": "parent_prod_pkg_key",
    "discount": "discount",
    "price_book_id": "price_book_id",
    "price_book_product_id": "price_book_product_id",
    "attribute_price_book_id": "attribute_price_book_id",
    "price_mode": "price_mode",
    "product_price": "product_price",
    "is_package": "is_package",
    "price_book_price": "price_book_price"
};

jest.mock('plugin_public_methods', () => ({
    ...jest.requireActual('plugin_public_methods').default,
    parseTreeToNormal: jest.fn((data) => data),
    deleteArrChildren: jest.fn(),
    uniq: jest.fn((arr) => [...new Set(arr)])
}));

global.CRM = {
    util: {
        cloneBomData: (data) => JSON.parse(JSON.stringify(data)),
        setChildrenAmount: jest.fn(),
        isGrayScale: jest.fn(() => false),
        getConfigStatusByKey: jest.fn(() => '0')
    }
};

describe('PriceServiceExtend', () => {
    const Bom = new BOM(pluginService, pluginParam);
    const PriceServiceExtend = Bom.PriceServiceExtend;
    let mockGetAllFields = jest.fn();

    beforeEach(() => {
        mockGetAllFields.mockReturnValue(fields);
        PriceServiceExtend.parent.getAllFields = mockGetAllFields;
        PriceServiceExtend.parent.Add = {
            noClearSalesPrice: jest.fn(() => false),
            calculateBomPrice: jest.fn()
        };
    });

    test('constructor should set parent reference', () => {
        expect(PriceServiceExtend.parent).toBe(Bom);
    });

    test('getAllFields should call parent getAllFields', () => {
        const result = PriceServiceExtend.getAllFields(mdApiName);
        expect(mockGetAllFields).toHaveBeenCalledWith(mdApiName);
        expect(result).toEqual(fields);
    });

    test('getPriceModeIsConfig should check price mode', () => {
        const data1 = { price_mode: '1' };
        const data2 = { 'price_mode__v': '1' };
        const data3 = { price_mode: '0' };

        expect(PriceServiceExtend.getPriceModeIsConfig(data1)).toBe(true);
        expect(PriceServiceExtend.getPriceModeIsConfig(data2)).toBe(true);
        expect(PriceServiceExtend.getPriceModeIsConfig(data3)).toBe(false);
    });

    test('_parseFullProductList_before should process data', () => {
        const param = {
            data: [
                { id: 1, price_mode: '1' },
                { id: 2, price_mode: '0' }
            ]
        };

        PriceServiceExtend._filterConfigPrice = jest.fn().mockReturnValue(param.data);
        PriceServiceExtend.addRootKey = jest.fn();

        const result = PriceServiceExtend._parseFullProductList_before({}, param);

        expect(PriceServiceExtend._filterConfigPrice).toHaveBeenCalledWith(param.data, param);
        expect(PriceServiceExtend.addRootKey).toHaveBeenCalledWith(param.data);
        expect(PPM.parseTreeToNormal).toHaveBeenCalledWith(param.data, true);
        expect(result).toHaveProperty('data');
    });

    test('_getRealPriceAndCalculate_before should filter children', () => {
        const param = {
            objApiName: mdApiName,
            modifyIndex: [1, 2, 3],
            data: [
                { rowId: 1, parent_prod_pkg_key: null },
                { rowId: 2, parent_prod_pkg_key: 'test' },
                { rowId: 3, parent_prod_pkg_key: 'test2' }
            ]
        };

        const result = PriceServiceExtend._getRealPriceAndCalculate_before({}, param);

        expect(PPM.deleteArrChildren).toHaveBeenCalledWith([1, 2, 3], [2, 3]);
        expect(result).toHaveProperty('modifyIndex');
    });

    test('_matchRealPrice_after should update discount for children', () => {
        const mockDataUpdater = {
            updateDetail: jest.fn()
        };

        const obj = {
            data: { parent_prod_pkg_key: 'test', rowId: 'row1' },
            param: {
                objApiName: mdApiName,
                dataUpdater: mockDataUpdater
            },
            value: {}
        };

        // Mock the getDescribe method that's called in _matchRealPrice_after
        PriceServiceExtend.getDescribe = jest.fn().mockReturnValue({});

        PriceServiceExtend._matchRealPrice_after({}, obj);

        expect(mockDataUpdater.updateDetail).toHaveBeenCalledWith(
            mdApiName,
            'row1',
            { discount: 0 }
        );
    });

    test('_batchAddAfter_after should add is_package field', () => {
        const opt = {
            data: [
                { id: 1 },
                { id: 2 }
            ],
            realPriceData: [
                { is_package: '是', is_package__v: true },
                { is_package: '否', is_package__v: false }
            ]
        };

        PriceServiceExtend._batchAddAfter_after({}, opt);

        expect(opt.data[0]).toHaveProperty('is_package', '是');
        expect(opt.data[0]).toHaveProperty('is_package__v', true);
        expect(opt.data[1]).toHaveProperty('is_package', '否');
        expect(opt.data[1]).toHaveProperty('is_package__v', false);
    });

    test('_matchRealPriceData_before should filter group data', () => {
        const param = {
            data: { isGroup: true }
        };

        const result = PriceServiceExtend._matchRealPriceData_before({}, param);
        expect(result).toEqual({ noMatch: true });

        const param2 = {
            data: { isGroup: false }
        };

        const result2 = PriceServiceExtend._matchRealPriceData_before({}, param2);
        expect(result2).toEqual({ noMatch: false });
    });

    test('getHook should return correct hooks', () => {
        const hooks = PriceServiceExtend.getHook();
        expect(hooks).toBeInstanceOf(Array);
        expect(hooks.length).toBeGreaterThan(0);

        const eventNames = hooks.map(hook => hook.event);
        expect(eventNames).toContain('price-service.parseFullProductList.before');
        expect(eventNames).toContain('price-service.parseFullProductList.after');
        expect(eventNames).toContain('price-service.getRealPriceAndCalculate.before');
    });
});
