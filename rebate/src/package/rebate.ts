import {
    FieldMapFace,
    DecimalMapFace,
    ExecuteFace,
    CouponFace,
    //返利单
    MasterData,
    Product,
    Rebate,
    ProductRebate,
    RangeRebateRule,
    RebateInfo,
    RebateGoods,
    RebateQueryRequest,
    RebateQueryResponse,
    RebateDetails,
    RebateInfoDetails,
    RebateQueryData,
    RebateSubmitData,
    RebateRequest,
    RebateMatchRequest,
    RebateMatchResponse,
    ResponseProductRebate,
    MiscContent,
    RebateUsed,
    ProductUsed,
    AutoUseRebateRequest,
    UsedInfo

} from './data_interface';
import PPM from 'plugin_public_methods';

export default class RebateImp {
    constructor(
        public requestId: string,
        public masterApiName: string,
        public detailApiName: string,
        public fromType: string,
        public fields: {
            [fieldName: string]: any
        },
        public fieldMap: FieldMapFace,
        public decimalMap: DecimalMapFace,
        public request: any,
        public triggerCal: any,
    ) { }

    /*****************************************************************************/
    /***************************** 优惠券 *****************************************/
    /*****************************************************************************/

    //查询可用优惠券
    public async queryCoupon(param: ExecuteFace): Promise<Array<CouponFace>> {
        const coupons = await PPM.composeAsync(
            PPM.curry(2, this.checkUsingCoupon)(param.masterData),
            this.reqCoupon.bind(this)
        )(param)
        return coupons;
    }

    //查询指定纸质优惠券
    public async queryPaperCoupon(masterData: any, num: string) {
        const { account_id } = this.fieldMap,
            args: any = {
                accountId: masterData[account_id],
                couponNo: num,
                requestId: this.requestId
            },
            url = "FHH/EM1HNCRM/API/v1/object/coupon/service/queryPaper",
            result = await PPM.ajax(this.request, url, args);
        return result;
    }

    //绑定纸质优惠券
    public async bindPaperCoupon(masterData: any, id: string) {
        const { account_id = "account_id" } = this.fieldMap,
            args: any = {
                accountId: masterData[account_id],
                couponId: id,
                requestId: this.requestId
            },
            url = "FHH/EM1HNCRM/API/v1/object/coupon/service/bindPaper",
            result = await PPM.ajax(this.request, url, args);
        return result;

    }

    //查询优惠券接口请求
    private reqCoupon(param: ExecuteFace) {
        const { account_id } = this.fieldMap,
            args: any = {
                requestId: this.requestId,
                masterObjectApiName: this.masterApiName,
                detailObjectApiName: this.detailApiName,
                edit: this.fromType == 'edit',
                accountId: param.masterData[account_id],
                masterData: param.masterData,
                detailDataMap: param.detailDataMap
            },
            url = "FHH/EM1HNCRM/API/v1/object/coupon/service/query";
        if(param.extraArgs?.queryFilters){
            args.filters = param.extraArgs.queryFilters
        }
        return PPM.ajax(this.request, url, args, 'datas');
    }

    //检查使用中的优惠券返利单
    private checkUsingCoupon(masterData: any, data: Array<CouponFace>) {
        data = data ?? [];
        const miscContent = (masterData.misc_content || {}).coupon || [];
        data.forEach((d: any) => {
            let useItem = miscContent.find((c: any) => c.id == d.id);
            d.using = useItem ? true : false;
        });
        return data;
    }

    //使用优惠券
    public async useCoupon(editTrigger: boolean, coupons: Array<any>, param: ExecuteFace) {
        const args = this.parseCouponArgs(param, editTrigger, coupons),
            data = await this.useHandle(param, args);
        return data;
    }

    //格式化优惠券分摊接口参数
    private parseCouponArgs(
        param: ExecuteFace,
        editTriggerCoupon: boolean,
        couponData: Array<any>
    ) {
        return {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            edit: this.fromType == 'edit',
            change: editTriggerCoupon,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
            couponDatas: couponData,
        };
    }

    //查询是否有待领券
    public async queryPendingCoupons(accountId:string){
       
        const args: any = {
            customerId: accountId,
        },
           url = "FHH/EM1AMarketingKIS/weChatCoupon/checkCustomerPendingCoupon",    
        result = PPM.ajax(this.request, url, args);
        return result;
    }

    //获取待领券列表
    public async fetchPendingCoupons(accountId:string){
       
        const args: any = {
            customerId: accountId,
            pageNum:1,
            pageSize:100
        },
           url = "FHH/EM1AMarketingKIS/weChatCoupon/queryCustomerPendingCouponList",
        result = PPM.ajax(this.request, url, args);
        return result;
    }
    //领券单张优惠券
    public async collectCoupon( param: ExecuteFace,batchId:string){
       
        const { account_id } = this.fieldMap,
        args: any = {
            accountId: param.masterData[account_id],
            requestId: this.requestId,
            batchId:batchId,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName, 
        },

           url = "FHH/EM1HNCRM/API/v1/object/coupon/service/receive",
        result = PPM.ajax(this.request, url, args);
        return result;
    }


    /*****************************************************************************/
    /******************************* 返利适配客户账户 *******************************/
    /*****************************************************************************/

    //查询返利单
    public async queryRebate(param: ExecuteFace, extraArgs: RebateInfo): Promise<RebateQueryData> {
        param.fundAccountId=extraArgs.fundAccountId;
        const rebates = await PPM.composeAsync(
            PPM.curry(2, this.checkUsingRebate.bind(this))(param),
            PPM.curry(2, this.reqRebate.bind(this))(extraArgs)
        )(param)
        return rebates;
    }

    //查询返利单接口请求
    private async reqRebate(extraArgs: RebateInfo, param: ExecuteFace): Promise<RebateQueryResponse> {
        const {account_id = "account_id" } = this.fieldMap,
            { rebateType, masterData, detailDataMap } = param;

        const args: RebateQueryRequest = Object.assign({
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            masterData: masterData,
            detailDataMap: detailDataMap,
            accountId: masterData[account_id],
            edit: this.fromType == 'edit',
        }, extraArgs),
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/query",
            result = await PPM.ajax(this.request, url, args);
        return {
            rebateType,
            ...result
        };
    }

    //检查已使用的返利单
    private checkUsingRebate(param: ExecuteFace, rebateData: RebateQueryResponse) {
        const { misc_content } = this.fieldMap,
            miscContent = param.masterData[misc_content] || {};
        let result = null,  
            fundAccountId=param.fundAccountId||"";
        switch (rebateData.rebateType) {
            case "Product":
                result = this.checkProductRebate(miscContent, rebateData, param.detailsArr,fundAccountId);
                break;
            default:
                result = this.checkMoneyRebate(miscContent, rebateData,fundAccountId);
        }
        return result;
    }

    //检查已使用的金额返利单
    private checkMoneyRebate(miscContent: MiscContent, rebateResponse: RebateQueryResponse,fundAccountId:string): RebateQueryData {
        const { rebate = [], rangeRebate = [] } = miscContent,
            { ruleId = "", rules = [], limitMoney, datas = [], rangeRuleIds = [], rangeRules = [], rangeData = [] } = rebateResponse;

        const usedRebateIds: Map<string, string> = this.collectUsedRebateMap(rebate, rangeRebate, ruleId);
        //格式化无范围的金额返利
        const rebates = this.parseRebate(datas, rebate, ruleId, usedRebateIds),
            rebateUsedInfo=this.computeRebateSummary(rebate,fundAccountId);
        const parsedRebateData: Array<RebateInfoDetails> = [{
            ruleId,
            ruleName: rules.find(r => r.id === ruleId)?.name || "",
            limitMoney,
            rebates,
            usedInfo:rebateUsedInfo
        }];

        //格式化有范围的金额返利
        const rangeRebateData = rangeRules.map((rule: RangeRebateRule) => {
            const item: RebateInfoDetails = {
                ruleId: rule.id,
                ruleName: rule.name,
                limitMoney: rule.limitMoney,
                rebates: [],
                usedInfo:{} as UsedInfo
            };
            if (rule.rangeRebateIds?.length) {
                //有范围的返利使用规则对应的返利单
                const queryRebates = rule.rangeRebateIds
                    .map(rId => rangeData.find(r => r.id === rId) || datas.find(d => d.id === rId))
                    .filter((rebate): rebate is Rebate => rebate !== undefined);

                const usedRangeRebate = rangeRebate.find(item => item.rangeRuleId === rule.id)?.rangeRebates || [];

                item.rebates = this.parseRebate(queryRebates, usedRangeRebate, rule.id, usedRebateIds);
                item.usedInfo=this.computeRebateSummary(usedRangeRebate,fundAccountId);
            }
            return item;
        });

        return {
            ruleId,
            rules,
            rangeRuleIds,
            rangeRules: rangeRules || [],
            rebateData: [...rangeRebateData,...parsedRebateData],
        };
    }

    //收集已使用的返利单

    private collectUsedRebateMap(
        rebate: Array<RebateUsed>,
        rangeRebate: Array<{
            rangeRuleId: string,
            rangeRebates: Array<RebateUsed>
        }>,
        ruleId: string): Map<string, string> {

        const usedRebateMap = new Map<string, string>();
        for (let rebateUsed of rebate) {
            usedRebateMap.set(rebateUsed.id, ruleId);
        }
        for (let rangeRebateItem of rangeRebate) {
            for (let rangeRebate of rangeRebateItem.rangeRebates) {
                usedRebateMap.set(rangeRebate.id, rangeRebateItem.rangeRuleId);
            }
        }
        return usedRebateMap;
    }

    //收集已使用的返利信息
    private computeRebateSummary(rebates: Array<RebateUsed | ResponseProductRebate>,fundAccountId:string):UsedInfo {
        let total = 0;
        let detailsMap: Map<string,  {
            fundTotal: number;
            fundId: string;
            fundName?: string;
        }> = new Map();
    
        for (let rebate of rebates) {
            if(fundAccountId&&rebate.fund_account_id!==fundAccountId){
                rebate.amount=Number(rebate.amount)|| 0;
                total += rebate.amount;
        
                let detail = detailsMap.get(rebate.fund_account_id);
        
                if (detail) {
                    detail.fundTotal += rebate.amount;
                } else {
                    detail = {
                        fundTotal: rebate.amount,
                        fundId: rebate.fund_account_id,
                        fundName: '' // To be implemented
                    };
                    detailsMap.set(rebate.fund_account_id, detail);
                }
            }
        }
    
        let details = Array.from(detailsMap.values());
        return { total, details };
    }

    //根据已经使用的返利，格式化金额返利单
    private parseRebate(data: Array<Rebate>, useData: Array<RebateUsed>, ruleId: string, usedIdMap: Map<string, string>) {
        const rebates = data.map((rebate: Rebate) => {

            const useItem = useData.find(used => used.id === rebate.id);
            const isUsed = usedIdMap.has(rebate.id);
            const useInOtherRule = isUsed && usedIdMap.get(rebate.id) !== ruleId;  //已在其他规则下使用该返利单

            return {
                ...rebate,
                using: !!useItem,
                use_amount: useItem ? useItem.amount : 0,
                disabled: useInOtherRule,
            };
        })
        return rebates
    }

    //检查已使用的产品返利单
    private checkProductRebate(miscContent: MiscContent, rebateResponse: RebateQueryResponse, detailsArr: Array<any>,fundAccountId:string): RebateQueryData {
        const { product_rebate = [] } = miscContent,
            { ruleId = "", rules = [], limitMoney, datas = [], rangeRuleIds = [], rangeRules = [], rangeData = [] } = rebateResponse;

        const productRebateData = datas.map((d: ProductRebate) => {
            const useItem = product_rebate.find(used => used.id === d.id);
            const item: RebateDetails = {
                ...d,
                using: !!useItem,
                use_amount: useItem && !isNaN(Number(useItem.amount)) ? Number(useItem.amount) : 0,
                disabled: false,
                productList: [],
                productCondition: []
            }
            const { productList, productCondition } = this.parseProductRange(item, detailsArr);
            item.productList = productList;
            item.productCondition = productCondition;
            //使用类型为数量，amount需要重新计算数量总量
            if (d.use_type == 'Quantity') {
                item.use_amount = (item.productList || []).reduce((amount: any, p: any) => {
                    amount = PPM.accAdd(amount, p.quantity);
                    return amount;
                }, 0);
            }
            return item;
        })
        const usedInfo=this.computeRebateSummary(product_rebate,fundAccountId);

        return {
            ruleId,
            rules,
            rangeRuleIds,
            rangeRules,
            rebateData: [{
                ruleId: ruleId,
                ruleName: rules.find(r => r.id === ruleId)?.name || "",
                limitMoney: limitMoney,
                rebates: productRebateData,
                usedInfo:usedInfo
            }]
        };
    }
    //格式化出productList: 包括按范围已选的赠品/固定赠品实现选择数量
    private parseProductRange(rebate: RebateDetails, detailsArr: Array<any>) {
        let productList = [];
        const { product_condition_type: type, product_range: range } = rebate;
        const data = range && range.data;

        switch (type) {
            //server查询接口已返回价目表产品相关字段，需要做一下字段映射
            case "FIXED":
                const { product_id, product_id__r, product_price, price_book_price, price_book_discount, actual_unit, is_multiple_unit, quantity, subtotal, rebate_dynamic_amount, price_book_id, price_book_id__r, price_book_product_id, price_book_product_id__r } = this.fieldMap;
                if (Array.isArray(data)) {
                    productList = data.map((d: RebateGoods) => {
                        let item = rebate.using
                        ? detailsArr.find(a => a.rebate_coupon_id === rebate.id && a.product_id === d.product_id)
                        : null;

                        if(item){
                            item = {
                                ...item,
                                amount: (item.rebate_dynamic_amount || 0) * -1,
                                unit__s: item.unit__s || d.unit__s  //临时兼容保存数据时__s丢失
                            }
                        }else{
                            item = {
                                [price_book_id]: d.pricebook_id,
                                [price_book_id__r]: d.pricebook_id__r,
                                [price_book_product_id]: d._id,
                                [price_book_product_id__r]: d.name,
                                [product_id]: d.product_id,
                                [product_id__r]: d.product_id__r,
                                [actual_unit]: d.unit_id,
                                [is_multiple_unit]: d.is_multiple_unit,
                                [price_book_price]: PPM.parseNumByDecimal(d.pricebook_price, this.decimalMap[price_book_price], d.pricebook_price, false), //价目表价格
                                [price_book_discount]: d.discount,
                                [product_price]: PPM.parseNumByDecimal(d.selling_price, this.decimalMap[product_price], d.selling_price, false), //价格
                                [quantity]: 0,
                                [subtotal]: 0,
                                amount: 0,
                                unit_id: d.unit_id,
                                unit__s: d.unit__s,
                            };
                            if(d.periodic_map){
                                item.periodic_map = d.periodic_map;
                            }
                        }
                        return item;
                    })
                }
                break;
            default:
                productList = (rebate.using && detailsArr.filter(a => a.rebate_coupon_id == rebate.id) || [])
                    .map(p => ({
                        ...p,
                        amount: (p.rebate_dynamic_amount || 0) * -1,
                    }));
                break;
        }
        let conditionData = null;
        //待确认通过返利产生生成的范围返利品数据格式为什么是对象，并临时兼容
        if (type == 'CONDITION') {
            try {
                conditionData = typeof data === "string" ? JSON.parse(data) : data;
            } catch (error) {
                console.error('JSON.parse failed:', error);
            }
        }
        return {
            productList,
            productCondition: conditionData,
        }
    }

    //收集全部使用的返利单
    public collectRebates(masterData: MasterData, rebateInfo: RebateSubmitData, fundAccountId: string): RebateRequest {
        const { misc_content, rebate_rule_id = "", product_rebate_rule_id = "" } = masterData,
            { rebate = [], rangeRebate = [], product_rebate = [] } = misc_content || {},
            { ruleId = "", productRuleId = "", rebateData = [], rangeRebateData = {}, productRebateData = [] } = rebateInfo,
            { price_book_price } = this.fieldMap;

        //无范围返利单:如果更换了无范围的返利使用规则，清空原数据
        let rebates = [];
        if (ruleId && rebate_rule_id !== ruleId) {
            rebates = rebateData;
        } else {
            rebates = rebate.filter(r => r.fund_account_id !== fundAccountId).concat(rebateData);
        }

        //有范围返利单
        let rangeRebateIds: Array<string> = [];
        let rangeRebatesResult: Array<{
            rangeRuleId: string;
            rangeRebates: Array<RebateUsed>
        }> = [];
        rangeRebate.forEach(r => {
            const rangeRebateArr = r.rangeRebates.filter(r => r.fund_account_id !== fundAccountId).concat(rangeRebateData[r.rangeRuleId] || []);
            if (rangeRebateArr.length) {
                rangeRebateIds.push(r.rangeRuleId);
                rangeRebatesResult.push({
                    rangeRuleId: r.rangeRuleId,
                    rangeRebates: rangeRebateArr
                });
            }
        })
        Object.keys(rangeRebateData).forEach(id => {
            if (!rangeRebateIds.includes(id)&&rangeRebateData[id].length) {
                rangeRebatesResult.push({
                    rangeRuleId: id,
                    rangeRebates: rangeRebateData[id]
                });
            }
        });
        //产品返利单
        let usedProductRebates:Array<ProductUsed> = [];
        if (!(productRuleId && product_rebate_rule_id && product_rebate_rule_id !== productRuleId)) {
            usedProductRebates = product_rebate
                .filter(pr => pr.fund_account_id !== fundAccountId)
                .flatMap(pr => (pr.product || []).map(r => ({
                    id: pr.id,
                    amount: r.amount,
                    product_id: r.product_id,
                    price: r.price,
                    quantity: r.quantity,
                })));
        }

        const newProductRebates = productRebateData.map(p => ({
            id: p.rebate_coupon_id,
            amount: p.amount,
            product_id: p.product_id,
            price: p[price_book_price],
            quantity: p.quantity,
        }));

        //返回UseRebateInfoFace类型的结果
        return {
            ruleId: ruleId || rebate_rule_id,
            rebateDatas: rebates,
            productRuleId: productRuleId || product_rebate_rule_id,
            productRebateDatas: [...usedProductRebates, ...newProductRebates],
            rangeRebateDatas: rangeRebatesResult
        }
    }

    //手动使用返利单
    public async useRebate(editTrigger: boolean, rebates: RebateRequest,isForceUse:boolean, param: ExecuteFace) {
        const args = this.parseRebateArgs(param, editTrigger,isForceUse, rebates),
            data = await this.useHandle(param, args);
        return data;
    }

    //格式化返利单分摊接口参数
    private parseRebateArgs(
        param: ExecuteFace,
        editTriggerRebate: boolean,
        isForceUse:boolean,
        rebates: RebateRequest
    ) {
        return {
            requestId: this.requestId,
            edit: this.fromType == 'edit',
            change: editTriggerRebate,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
            compulsoryRebate:isForceUse,
            ...rebates
        };
    }

    //自动使用返利单
    public async autoUseRebate(fundArgs: any,isForceUse: boolean, param: ExecuteFace) {
        const args = {
            requestId: this.requestId,
            edit: this.fromType == 'edit',
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
            fundAccountId: fundArgs.fundAccountId,
            amount: fundArgs.amount,
            compulsoryRebate:isForceUse
        },
            data = await PPM.composeAsync(
                PPM.curry(2, this.updateMatchAmortizeRes.bind(this))(param),
                this.verifyMatchRes.bind(this),
                this.reqAutoMatch.bind(this)
            )(args);
        return data;
    }

    //调用自动使用返利接口
    private async reqAutoMatch(args: any) {
        const { ruleId, productRuleId } = args,
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/autoUse",
            result = await PPM.ajax(this.request, url, args);
        return result;
    }
    
    //获取返利条件字段
    private async reqRebateCondition(param: ExecuteFace) {
        const args = {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            dataId:param.masterData.dataId||""
        },
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/getConditionFields",
            result = await PPM.ajax(this.request, url, args);
        return result;
    }

    //查询账户最大可用返利金额
    private async reqFundAvailableAmount(param: ExecuteFace,fundIdsArr:Array<string>) {
        const args = {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
            fundAccountIds: fundIdsArr,
        },
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/canUseAmount",
            result = await PPM.ajax(this.request, url, args);
        return result;
    }

    /*****************************************************************************/
    /******************************* 公共方法 *******************************/
    /*****************************************************************************/


    //提交前校验
    public async checkMatch(editTrigger: boolean, param: ExecuteFace) {
        const args = this.parseCheckArgs(param, editTrigger),
            data = await this.useHandle(param, args);
        return data;
    }


    //格式化返保存前校验接口参数
    private parseCheckArgs(
        param: ExecuteFace,
        editTriggerCoupon: boolean,
    ) {
        return {
            requestId: this.requestId,
            masterObjectApiName: this.masterApiName,
            detailObjectApiName: this.detailApiName,
            edit: this.fromType == 'edit',
            change: editTriggerCoupon,
            masterData: param.masterData,
            detailDataMap: param.detailDataMap,
        };
    }

    private async useHandle(param: ExecuteFace, args: any) {
        const me = this,
            data = await PPM.composeAsync(
                PPM.curry(2, this.updateMatchAmortizeRes.bind(this))(param),
                this.verifyMatchRes.bind(this),
                this.reqMatchHandle.bind(this)
            )(args);
        return data;
    }

    private async reqMatchHandle(args: any) {
        const { ruleId, productRuleId } = args,
            url = "FHH/EM1HNCRM/API/v1/object/rebate/service/matchAmortize",
            result = await PPM.ajax(this.request, url, args);
        if(!result){
            return result;
        }
        return {
            ruleId,
            productRuleId,
            ...result,
        }
    }

    //没有返利单，清除对应的返利使用规则，有则更新对应的返利使用规则
    private updateRuleIds(result: RebateMatchResponse): MasterData {
        const { rebate_rule_id, product_rebate_rule_id, range_rebate_rule_ids = "range_rebate_rule_ids" } = this.fieldMap;

        const masterData: MasterData = result.masterData || {},
            miscContent: MiscContent = masterData.misc_content || {} as MiscContent;

        masterData[rebate_rule_id] = (!miscContent.rebate || miscContent.rebate.length <= 0) ? "" : result.ruleId;
        masterData[product_rebate_rule_id] = (!miscContent.product_rebate || miscContent.product_rebate.length <= 0) ? "" : result.productRuleId;
        masterData[range_rebate_rule_ids] = (!miscContent.rangeRebate || miscContent.rangeRebate.length <= 0) ? "" : miscContent.rangeRebate.map(r => r.rangeRuleId);
        return masterData;
    }

    //校验接口数据
    private checkMiscContent(result: RebateMatchResponse): MasterData{
        const masterData: MasterData = result.masterData || {},
            miscContent: MiscContent = masterData.misc_content || {} as MiscContent;
        
        const defValue= {
                rebate: [],
                coupon: [],
                rebate_amount: '0',
                coupon_amount: '0',
                rangeRebate: [],
                product_rebate: [],
          
        };
        masterData.misc_content={
            ...defValue,
            ...miscContent
        };
        return masterData;
    }

    //返利使用规则&产品返利使用规则补全到主对象数据上，补全赠品数据
    private verifyMatchRes(result: RebateMatchResponse) {
        if(result){
            result.masterData = this.checkMiscContent(result);
        }
        return result;
    }

    //更新返利优惠券分摊算接口数据
    private updateMatchAmortizeRes(param: ExecuteFace, result: RebateMatchResponse): ExecuteFace {
        if (!result) {
            return param;
        }
        return {
            masterData: Object.assign(param.masterData, result.masterData),
            detailDataMap: PPM.updateObject(param.detailDataMap, result.detailDataMap),
            policyInfo: Object.assign(param.policyInfo || {}, {
                couponChange: result.couponChange,
                rebateChange: result.rebateChange,
                productRebateChange: result.productRebateChange,
                productRebateData: result.productRebateData,
                needUpdateProduct: result.needUpdateProduct
            }),
            changeInfo: PPM.updateChangeInfo(param.changeInfo, result.masterData, result.detailDataMap),
            modifyInfo: PPM.generateModifyInfo(result.masterData, result.detailDataMap, this.masterApiName, this.detailApiName),
            detailsArr: param.detailsArr,
            rebateType: param.rebateType
        };
    }

    /*****************************************************************************/
    /**********************************计算接口*************************************/
    /*****************************************************************************/
    //调用计算接口
    public async calBatch(param: ExecuteFace) {
        const dataFromPolicy = await PPM.composeAsync(
            PPM.curry(2, this.updateCalRes.bind(this))(param),
            this.triggerCal.bind(this),
            this.parseCalArgs.bind(this)
        )(param);
        return dataFromPolicy;
    }

    //格式化计算接口入参
    private parseCalArgs(param: ExecuteFace) {
        //补全赠品等数据，做全量计算
        param = this.supplementGifts(param);
        const changeFields: any = [];
        return {
            noMerge: true,
            noLoading: true,
            noRetry: true,
            changeFields: changeFields,
            filterFields: {},
            extraFields: PPM.collectCalFields(this.fields, param.modifyInfo.modifyFields,[this.detailApiName]),
            operateType: "edit",
            dataIndex: param.modifyInfo.modifyIndex,
            objApiName: this.masterApiName,
            masterData: param.masterData,
            details: {
                [this.detailApiName]: Object.keys(param.detailDataMap || {})
                    .map((key: string) => param.detailDataMap[key])
            }
        }
    }
    private updateCalRes(param: ExecuteFace, result: any): ExecuteFace {
        if (!result) {
            return param;
        }
        const calRes = result.Value.calculateResult || {},
            resMaster = calRes[this.masterApiName][0] || {},
            resDetail = calRes[this.detailApiName] || {};
        return {
            masterData: Object.assign(param.masterData, resMaster),
            detailDataMap: PPM.updateObject(param.detailDataMap, resDetail),
            changeInfo: PPM.updateChangeInfo(param.changeInfo, resMaster, resDetail),
            modifyInfo: PPM.generateModifyInfo(resMaster, resDetail, this.masterApiName, this.detailApiName),
            policyInfo: param.policyInfo,
            detailsArr: param.detailsArr,
            rebateType: param.rebateType
        }
    }

    /**
     * 1.补全新增/删除的返利品
     * 2.补全未参加返利优惠券计算的价格政策赠品/bom子件
     */
    public supplementGifts(param: ExecuteFace) {
        param = PPM.compose(
            this.transferGifts.bind(this),
            this.supplementPolicyGifts.bind(this)
        )(param);
        return param;

    }

    //赠品转换为明细&删除失效赠品
    private transferGifts(param: ExecuteFace) {
        let initialVal: any = {},
            { mdAdd = [], mdDel = [] } = param.changeInfo;

        param.detailDataMap = Object.keys(param.detailDataMap || {})
            .reduce((acc: any, key: string) => {
                if (!mdDel.includes(key)) {
                    acc[key] = param.detailDataMap[key]
                }
                return acc;
            }, initialVal);
        param.changeInfo.mdAdd = mdAdd.filter((data: any) => !mdDel.includes(data.rowId));
        param.changeInfo.mdAdd.forEach((data: any) => {
            param.detailDataMap[data.rowId] = data;
        })
        return param;
    }

    private supplementPolicyGifts(param: ExecuteFace) {
        const isPolicyGift = (data: any) => {
            return ['1', '2'].includes(data.is_giveaway);
        },
            isBom = (data: any) => data.parent_rowId;
        param.detailsArr.forEach(d => {
            if (isPolicyGift(d) || isBom(d)) {
                param.detailDataMap[d.rowId] = d;
            }
        });
        return param;

    }

    /*****************************************************************************/
    /***************************构造取消接口数据&取消操作*****************************/
    /*****************************************************************************/
    //取消全部政策
    public cancelPolicy(param: ExecuteFace) {
        return this.cancelHandle(param, 'all');
    }

    //取消使用返利单
    public cancelRebate(param: ExecuteFace,fundAccountField:string) {
        return this.cancelHandle(param, 'rebate',fundAccountField);
    }

    //取消使用优惠券
    public cancelCoupon(param: ExecuteFace) {
        return this.cancelHandle(param, 'coupon');
    }

    private cancelHandle(param: ExecuteFace, type: string,masterField?:string) {
        let result = null;
        switch (type) {
            case 'rebate':
                result = this.generateCancelRebate(param,masterField);
                break;
            case 'coupon':
                result = this.generateCancelCoupon(param);
                break;
            default:
                result = this.generateCancelAll(param);
        }
        return PPM.composeAsync(
            this.calBatch.bind(this),
            PPM.curry(2, this.updateMatchAmortizeRes.bind(this))(param),
        )(result);
    }

    private generateCancelRebate(param: ExecuteFace,masterField?:string) {
        const {
            misc_content,
            rebate_amortize_amount,
            rebate_dynamic_amount,
            rebate_amount,
            rebate_coupon_id
        } = this.fieldMap;

        let detailsMap: any = {},
            detailsFilterRebates: any = {},
            mdDel: Array<string> = param.detailsArr.filter((d: any) => d[rebate_coupon_id]).map((d: any) => d.rowId);

        for (let key in param.detailDataMap) {
            let miscContent = param.detailDataMap[key][misc_content];
            detailsMap[key] = {
                [rebate_amortize_amount]: 0,
                [rebate_dynamic_amount]: 0,
            }
            if (miscContent) {
                detailsMap[key][misc_content] = Object.assign(miscContent, {
                    rebate_amortize: [],
                    product_rebate_amortize: [],
                })
            }
        }
        param.changeInfo.mdDel = (param.changeInfo.mdDel || []).concat(mdDel);
       // const rangeRebateRuleIds: any = [];
        const defaultMasterData = {
            ...(masterField ? { [masterField]: 0 } : {}),
            rebate_rule_id: "",
            product_rebate_rule_id: "",
            range_rebate_rule_ids: [],
            [rebate_amount]: 0,
            [misc_content]: {
                ...(param.masterData[misc_content] || {}),
                rebate: [],
                product_rebate: [],
                rangeRebate: [],
            }
        };
        
        return {
            masterData: {...defaultMasterData},
            detailDataMap: detailsMap,
        };
        
    }
    //本地构造取消接口result:misc_content,coupon_amount,coupon_amortize_amount,coupon_dynamic_amount
    private generateCancelCoupon(param: ExecuteFace) {
        const {
            misc_content,
            coupon_amortize_amount,
            coupon_dynamic_amount,
            coupon_amount
        } = this.fieldMap;

        let detailsMap: any = {};
        for (let key in param.detailDataMap) {
            let miscContent = param.detailDataMap[key][misc_content];
            detailsMap[key] = {
                [coupon_amortize_amount]: 0,
                [coupon_dynamic_amount]: 0,
            }
            if (miscContent) {
                detailsMap[key][misc_content] = Object.assign(miscContent, {
                    coupon_amortize: []
                })
            }
        }
        return {
            masterData: {
                [coupon_amount]: 0,
                [misc_content]: Object.assign(param.masterData[misc_content] || {}, {
                    coupon: []
                })
            },
            detailDataMap: detailsMap,
        }
    }
    //初始化，取消全部政策
    private generateCancelAll(param: ExecuteFace) {
        const {
            misc_content,
            rebate_amortize_amount,
            rebate_dynamic_amount,
            rebate_amount,
            rebate_coupon_id,
            coupon_amortize_amount,
            coupon_dynamic_amount,
            coupon_amount
        } = this.fieldMap;

        let detailsMap: any = {},
            detailsFilterRebates: any = {},
            mdDel: Array<string> = param.detailsArr.filter((d: any) => d[rebate_coupon_id]).map((d: any) => d.rowId);

        for (let key in param.detailDataMap) {
            let miscContent = param.detailDataMap[key][misc_content];
            detailsMap[key] = {
                [coupon_amortize_amount]: 0,
                [coupon_dynamic_amount]: 0,
                [rebate_amortize_amount]: 0,
                [rebate_dynamic_amount]: 0,
            }
            if (miscContent) {
                detailsMap[key][misc_content] = {
                    coupon_amortize: [],
                    rebate_amortize: [],
                    product_rebate_amortize: [],
                }
            }
        }
        param.changeInfo.mdDel = (param.changeInfo.mdDel || []).concat(mdDel);
        const rangeRebateRuleIds: any = [];
        return {
            masterData: {
                rebate_rule_id: "",
                product_rebate_rule_id: "",
                range_rebate_rule_ids: rangeRebateRuleIds,
                [coupon_amount]: 0,
                [rebate_amount]: 0,
                [misc_content]: {
                    coupon: [],
                    rebate: [],
                    rangeRebate: [],
                    product_rebate: [],
                }
            },
            detailDataMap: detailsMap,
        }
    }


}