import {
	FieldMapFace,
	DecimalMapFace,
	ExecuteFace,
	GetUnitGiftFace,
	UnitResFace,
	RebateSubmitData,
	MasterData,
	ProductUsed,
	Product,
	ResponseProductRebate,
	MiscContent,
	DetailData
} from './data_interface';
import PPM from 'plugin_public_methods';
import { parseCommandLine, updateNonNullExpression } from 'typescript';

interface RebateProductResult {
	mdDel: Array<string>,
	mdUpdate: { [rowId: string]: any },
	mdAdd: Array<DetailData>
}
export default class RebateProduct {
	public mcCurrency: string;
	constructor(

		public masterApiName: string,
		public detailApiName: string,
		public detailDesc: any,
		public fromType: string,
		public recordType: string,
		public fields: {
			[fieldName: string]: any
		},
		public fieldMap: FieldMapFace,
		public decimalMap: DecimalMapFace,
		public request: any,
		public triggerCal: any,
		public getRowBasicData: any,
	) {
		this.mcCurrency = "";
	}

	/*****************************************************************************/
	/***********************************计算赠品***********************************/
	/*****************************************************************************/
	public async calGifts(param: ExecuteFace, submitData: RebateSubmitData | null): Promise<ExecuteFace> {

		const { productRebateData } = submitData || {};
		if (!productRebateData?.length && !param.policyInfo.productRebateChange) {
			return param;
		}

		this.mcCurrency = param.masterData.mcCurrency;

		param = await PPM.composeAsync(
			PPM.curry(2, this.updateChangeInfo.bind(this))(param),//更新变更信息
			PPM.curry(2, this.calUpdateProducts.bind(this))(param),//计算更新的返利品
			PPM.curry(2, this.calAddProducts.bind(this))(param),//计算新增的返利品
			this.calPeriodData.bind(this),
			PPM.curry(2, this.calMultiUnit.bind(this))(param.detailsArr),//补全多单位相关数据
			this.parseProductsAttr.bind(this),
			PPM.curry(2, this.separateRebateProduct.bind(this))(productRebateData),//补全多单位相关数据
		)(param)
		return param;
	}

	//1.分离返利品:删除的返利品，更新的返利品，新增的返利品
	private separateRebateProduct(product: Array<Product>, param: ExecuteFace) {
		const { masterData, detailsArr, changeInfo } = param;
		const productRebate: Array<ResponseProductRebate> = masterData.misc_content?.product_rebate || [];
		const { product_id, quantity, rebate_dynamic_amount } = this.fieldMap;

		const productMap: Map<string, Product> = new Map(product?.map(item => [`${item.rebate_coupon_id}-${item[product_id]}`, item]) || []);
		const detailMap: Map<string, DetailData> = new Map(detailsArr.filter(d => d.is_giveaway == '2').map(item => [`${item.rebate_coupon_id}-${item[product_id]}`, item]));
		const rebateProductIds: Set<string> = new Set();
		const basicData = this.getRowBasicData();
		const result: RebateProductResult = {
			mdDel: [],
			mdUpdate: {},
			mdAdd: []
		};
		const resMdUpdate = changeInfo.mdUpdate || {};

		//根据match接口返回的返利品信息做更新
		productRebate.forEach((pr: ResponseProductRebate) => {
			(pr.product || []).forEach((item: ProductUsed) => {
				const tempUniqId = `${pr.id}-${item.product_id}`;
				// 如果返利品不在明细里，从提交数据里取出详细数据，并更新到结果中
				if (!detailMap.has(tempUniqId)) {
					const uniqId = PPM.uniqueCode(),
						itemDetail: any = productMap.get(tempUniqId) || {};
					const newItem = {
						...basicData,
						...itemDetail,
						[quantity]: item.quantity,
						[rebate_dynamic_amount]: item.amount * -1,
						rowId: itemDetail.rowId || uniqId,
						is_giveaway: "2",
						record_type: this.recordType
					}
					result.mdAdd.push(newItem);
					rebateProductIds.add(uniqId);
					item.priceBookPriceFlag = itemDetail.priceBookPriceFlag;
				} else {
					//否则检查该数据是否需要更新
					const dItem = detailMap.get(tempUniqId);
					if (dItem) {
						if (item.quantity !== dItem[quantity]) {
							result.mdUpdate[dItem.rowId] = {
								...resMdUpdate[dItem.rowId] || {},
								[quantity]: item.quantity,
								[rebate_dynamic_amount]: item.amount * -1,
							}
						}
						dItem[quantity] = item.quantity;
						dItem[rebate_dynamic_amount] = item.amount * -1,
							rebateProductIds.add(dItem.rowId);
						item.priceBookPriceFlag = dItem.priceBookPriceFlag;
					}
					

				}
			})
		})
		//明细里的返利品，如果不在接口返回里，删除明细
		detailsArr.forEach(item => {
			if (item.is_giveaway == "2" && !rebateProductIds.has(item.rowId)) {
				result.mdDel.push(item.rowId);
			}
		});
		return result;
	}

	// 2. 新增返利品，更新属性信息（周期性属性）
	private parseProductsAttr(result: RebateProductResult) {
		const { mdAdd } = result;
		const { pricing_period , pricing_mode, pricing_cycle,pricing_rate, service_start_time,whole_period_sale,settlement_mode,settlement_cycle,settlement_rate } = this.fieldMap;

		(mdAdd || []).forEach(data => {
			const { periodic_map = {} } = data;
			if (Object.keys(periodic_map).length > 0) {
				Object.assign(data, {
					[pricing_cycle]:periodic_map.pricing_cycle,
					[pricing_rate]: periodic_map.pricing_frequency,
					[pricing_mode]: periodic_map.pricing_mode,
					[service_start_time]:periodic_map.service_start_time,	
					[settlement_cycle]:periodic_map.settlement_cycle,
					[settlement_rate]: periodic_map.settlement_frequency,
					[settlement_mode]:periodic_map.settlement_mode,
					[whole_period_sale]:periodic_map.whole_period_sale		
				});
			}
			if(pricing_period){
				data[pricing_period] = 1;
			}
			delete data.periodic_map;
		})

		return result;
	}

	//2.多单位计算
	private async calMultiUnit(detailsArr: Array<DetailData>, result: RebateProductResult) {
		const { product_id, actual_unit, is_multiple_unit, base_unit_count, conversion_ratio, stat_unit_count, quantity } = this.fieldMap,
			{ mdUpdate, mdAdd } = result,
			updateKeys = Object.keys(mdUpdate),
			unitGifts = mdAdd.filter((d: DetailData) => d[is_multiple_unit]),
			oriUnitProducts = detailsArr.filter((d: DetailData) => updateKeys.includes(d.rowId) && d.is_multiple_unit__v);

		let reqUnitRes = await this.reqUnit([...unitGifts, ...oriUnitProducts]);

		if (!reqUnitRes || reqUnitRes.length <= 0) {
			reqUnitRes = [];
		}

		result.mdAdd = mdAdd.map(m => {
			const res = reqUnitRes.find((r: any) => r.rowId == m.rowId);
			return {
				...m,
				[base_unit_count]: res?.[base_unit_count] ?? m[quantity],
				[conversion_ratio]: res?.[conversion_ratio] ?? 1,
				[stat_unit_count]: res?.[stat_unit_count] ?? m[quantity] + m.unit__s
			}
		});

		result.mdUpdate = updateKeys.reduce((acc, key) => {
			const res = reqUnitRes.find((r: any) => r.rowId == key);
			acc[key] = res
				? {
					...mdUpdate[key],
					[base_unit_count]: res[base_unit_count],
					[conversion_ratio]: res[conversion_ratio],
					[stat_unit_count]: res[stat_unit_count]
				}
				: mdUpdate[key];
			return acc;
		}, {} as { [key: string]: object })

		return result;
	}

	//多单位计算接口请求
	private reqUnit(unitGifts: Array<any>) {
		if (!unitGifts || unitGifts.length <= 0) {
			return [];
		}
		const { product_id, price_book_product_id, actual_unit, quantity } = this.fieldMap;
		const url = "FHH/EM1HNCRM/API/v1/object/mutipleUnit/service/calcPriceByUnit",
			args = {
				params: unitGifts.map((gift: any) => {
					return {
						productId: gift[product_id],
						priceBookProductId: gift[price_book_product_id] || "",
						unitId: gift[actual_unit],
						count: gift[quantity],
						rowId: gift.rowId,
					};
				}),
				describeApiName: this.detailApiName,
				mcCurrency: this.mcCurrency,
			};
		return PPM.ajax(this.request, url, args, 'caclResult');
	}
 
	// 3. 计算周期性赠品服务结束时间
	private async calPeriodData(result: RebateProductResult) {
		const {mdAdd} = result;
		const {
			pricing_mode ,
			pricing_cycle,
			pricing_rate,
			whole_period_sale,
			pricing_period,
			service_start_time,
			service_end_time,
			settlement_mode,
			settlement_cycle,
			settlement_rate
		} = this.fieldMap;
	
		const periodGifts = mdAdd
        .filter(gift => pricing_mode && gift[pricing_mode] === "cycle")
        .map(gift => ({
            rowId: gift.rowId,
            pricingModel: gift[pricing_mode],
            pricingCycle: gift[pricing_cycle],
            pricingRate: gift[pricing_rate],
            wholePeriodSale: gift[whole_period_sale],
            pricingPeriod: gift[pricing_period],
            serviceStartTime: gift[service_start_time],
            settlementMode: gift[settlement_mode],
            settlementCycle: gift[settlement_cycle],
            settlementRate: gift[settlement_rate],
            calculateField: "serviceEndTime" // 要计算的字段
        }));

		if (periodGifts.length == 0) return result;

		const url = `FHH/EM1HNCRM/API/v1/object/periodic_product/service/calculate`,
			args = { dataList: periodGifts };
		const periodResult = await PPM.ajax(this.request, url, args);
		const dataListMap = new Map(
            (periodResult?.dataList || []).map((item:any) => [item.rowId, item.serviceEndTime])
        );

		mdAdd.forEach(g => {
			if (dataListMap.has(g.rowId)) {
				g[service_end_time] = dataListMap.get(g.rowId);
			}
		})
		return result;
	}


	//3.计算新增返利品
	private async calAddProducts(param: ExecuteFace, result: RebateProductResult) {
		if (!result.mdAdd.length) {
			return result;
		}
		const calResult = await PPM.composeAsync(
			this.triggerCal.bind(this),
			PPM.partial(this.parseCalArgs.bind(this), null, param.masterData),
		)(result.mdAdd);
		if (calResult) {
			const calRes = calResult.Value.calculateResult,
				detailRes = calRes[this.detailApiName];

			result.mdAdd = result.mdAdd.map(d => {
				return {
					...d,
					...detailRes[d.rowId] || {}
				}
			});

		}
		return result;
	}

	//4.计算更新返利品
	private async calUpdateProducts(param: ExecuteFace, result: RebateProductResult) {
		const { mdUpdate } = result;

		if (!Object.keys(mdUpdate).length) {
			return result;
		}
		let updateGifts: Array<DetailData> = [];
		let changeFieldsSet = new Set();

		param.detailsArr.forEach((d, index) => {
			if (mdUpdate[d.rowId]) {
				let updatedItem = {
					...d,
					...mdUpdate[d.rowId]
				};
				param.detailsArr[index] = updatedItem;
				updateGifts.push(updatedItem);
			}
		});

		const { quantity, base_unit_count, conversion_ratio, stat_unit_count, rebate_dynamic_amount } = this.fieldMap,
			changeFields = [quantity, base_unit_count, conversion_ratio, stat_unit_count, rebate_dynamic_amount];
		const calResult = await PPM.composeAsync(
			this.triggerCal.bind(this),
			PPM.partial(this.parseCalArgs.bind(this), changeFields, param.masterData),
		)(updateGifts);
		if (calResult) {
			const calRes = calResult.Value.calculateResult,
				detailRes = calRes[this.detailApiName];

			param.detailsArr.forEach((d) => {
				const key = d.rowId;
				const item = detailRes[key];
				if (item) {
					mdUpdate[key] = { ...mdUpdate[key], ...item };
					Object.assign(d, item);
				}
			});

		}
		return result;
	}
	//5.更新修改信息
	private updateChangeInfo(param: ExecuteFace, result: RebateProductResult) {

		const { mdAdd, mdDel, mdUpdate } = result;
		let { mdAdd: changeMdAdd, mdDel: changeMdDel, mdUpdate: changeMdUpdate } = param.changeInfo;

		changeMdAdd = (changeMdAdd || []).concat(mdAdd);
		changeMdDel = (changeMdDel || []).concat(mdDel);
		param.detailsArr = param.detailsArr.map(item => {
			if (mdUpdate[item.rowId]) {
				changeMdUpdate[item.rowId] = {
					...(changeMdUpdate[item.rowId] || {}),
					...mdUpdate[item.rowId]
				};
				return {
					...item,
					...mdUpdate[item.rowId]
				}
			} else {
				return item;
			}
		});
		// Assign updated values back to param.changeInfo
		param.changeInfo = { ...param.changeInfo, mdAdd: changeMdAdd, mdDel: changeMdDel, mdUpdate: changeMdUpdate };

		return param;
	}

	public parseCalArgs(changeFields: Array<string> | null, masterData: MasterData, detailData: Array<DetailData>) {
		const { product_price, price_book_price, price_book_discount, quantity,pricing_period="pricing_period",service_start_time="service_start_time",service_end_time="service_end_time" } = this.fieldMap;
		const { masterApiName, detailApiName, fields } = this;
		let calFieldsObj: any = PPM.getCalFields(fields[detailApiName], true);
		//计算赠品不可计算字段
		const noCalculateFields = [
			product_price,
			price_book_price,
			price_book_discount,
			quantity,
			pricing_period,
			service_start_time,
			service_end_time
		];
		if (changeFields) {
			calFieldsObj = PPM.collectCalFields(fields, {
				[detailApiName]: changeFields
			},[detailApiName]);
		} else {
			calFieldsObj = PPM.getCalFields(fields[detailApiName], true);
		}
		calFieldsObj[detailApiName] = calFieldsObj[detailApiName].filter((f: string) => !noCalculateFields.includes(f));
		const args = {
			noMerge: true,
			noLoading: true,
			noRetry: true,
			changeFields: [],
			filterFields: {},
			extraFields: calFieldsObj,
			operateType: 'edit',
			dataIndex: detailData.map((g: any) => g.rowId),
			objApiName: masterApiName,
			masterData: masterData,
			details: {
				[detailApiName]: detailData
			}
		}
		return args;
	}


	/*****************************************************************************/
	/***************************格式化按范围选择的返利品数据***************************/
	/*****************************************************************************/
	public async calRangeProducts(masterData: any, gifts: Array<any>, unitType: string, detailsArr: any) {
		let getUnitParam: GetUnitGiftFace = {
			gifts: gifts,
			unitType: unitType,
			info: null,
			detailsArr,
		};
		const result = await PPM.composeAsync(
			PPM.curry(2, this.giftPriceByPriceBook.bind(this))(masterData),
			PPM.curry(2, this.parseUnitGifts.bind(this))(getUnitParam),
			this.requestUnit.bind(this),
			this.parsePickData.bind(this),
		)(getUnitParam);
		return result;
	}
	//1.格式化基本数据
	private parsePickData(param: GetUnitGiftFace) {
		const productField = this.fields[this.detailApiName].product_id,
			productNameConfig = productField.is_open_display_name;
		const { product_id, product_id__r, actual_unit, is_multiple_unit, quantity, subtotal } = this.fieldMap;
		param.gifts = param.gifts.map((g: any) => {
			const uniqId = PPM.uniqueCode();
			let item = {
				[product_id]: g._id,
				[product_id__r]: productNameConfig ? g.display_name : g.name,
				[quantity]: 1,
				[subtotal]: 0,
				[actual_unit]: g.unit,
				[is_multiple_unit]: g.is_multiple_unit,
				rowId: uniqId,
				amount: 0,
				unit_id: g.unit,
				unit__s: g.unit__tpd,
			}
			if(g.pricing_mode == "cycle"){
				item.periodic_map =	this.generatePeriodData(g)
			}
			return item;
		});
		return param;
	}
	private generatePeriodData(data:any){
		const fields = [
			"pricing_mode",
			"pricing_cycle",
			"pricing_frequency",
			"whole_period_sale",
			"pricing_period",
			"settlement_mode",
			"settlement_cycle",
			"settlement_frequency",
		];
	
		const periodMap:any={
			"service_start_time":CRM.util.getStartOfDayTimestamp()
		}

		return fields.reduce((result, field) => {
			result[field] = data[field];
			return result;
		}, periodMap);
	}

	//2.根据单位类型获取返利品实际单位('baseUnit'类型不需要获取)
	private parseUnitGifts(param: GetUnitGiftFace, res: UnitResFace) {
		if (!res) {
			return param;
		}
		//更新赠品，可能有赠品无法选中
		const { product_id, product_id__r, actual_unit } = this.fieldMap;
		let usableGift: Array<any> = [],
			disableGift: Array<any> = [];

		param.gifts.forEach(gift => {
			let unitData = res[gift[product_id]];
			if (unitData) {
				const item = unitData[param.unitType];
				gift[actual_unit] = gift.unit_id = item.unit_id;
				gift.unit__s = item.unit__s;
				usableGift.push(gift);
			} else {
				disableGift.push(`"${gift[product_id__r]}"`)
			}
		});
		param.gifts = usableGift;
		if (disableGift.length >= 1) {
			param.info = `${disableGift.join(', ')}没有符合规则配置单位的产品，所以无法选中`;
		}
		return param;
	}


	private requestUnit(param: GetUnitGiftFace) {
		if (!param.unitType || param.unitType == 'baseUnit') {
			return null;
		}
		let initialObj: any = {};
		const url = 'FHH/EM1HNCRM/API/v1/object/price_policy/service/checkGiftUnit',
			args = {
				arg: param.gifts.reduce((argsObj: any, item: any) => {
					argsObj[item.product_id] = [param.unitType];
					return argsObj;
				}, initialObj)
			};
		return PPM.ajax(this.request, url, args, 'result');
	}

	//3.取价接口获取价格&价目表相关信息
	private async giftPriceByPriceBook(masterData: any, param: GetUnitGiftFace) {
		const me = this,
			{ product_id, quantity, subtotal, actual_unit, is_multiple_unit, price_book_id, price_book_id__r, price_book_product_id, price_book_product_id__r, price_book_price, price_book_discount, product_price } = this.fieldMap,
			reqRealPriceRes = await this.reqRealPrice(masterData, param.detailsArr, param.gifts),
			priceRes = reqRealPriceRes.newRst || [];

		param.gifts.map((gift: any,index:number) => {
			
			let item = priceRes.find((r: any) => r.rowId == gift.rowId);
			if (item) {
				Object.assign(gift, {
					[price_book_id]: item.pricebook_id, // 价目表id
					[price_book_id__r]: item.pricebook_id__r, // 价目表
					[price_book_product_id]: item._id, // 价目表产品id
					[price_book_product_id__r]: item.name, // 价目表明细
					[price_book_discount]: item.discount,//价目表折扣
					[price_book_price]: PPM.parseNumByDecimal(item.pricebook_price, this.decimalMap[price_book_price], item.pricebook_price, false), //价目表价格
					[product_price]: PPM.parseNumByDecimal(item.selling_price, this.decimalMap[product_price], item.selling_price, false), //价格
				})
			}else{
				Object.assign(gift, {
					[price_book_price]: 0, //价目表价格
					priceBookPriceFlag:"not_found"
				})
			}

			let tempSubtotal = PPM.multiplicational(gift[quantity], gift[price_book_price]);

			gift[subtotal] = PPM.parseNumByDecimal(
				tempSubtotal,
				this.decimalMap[subtotal],
				tempSubtotal,
				false
			)
			gift.amount = gift.subtotal;
		});
		return param;
	}

	private reqRealPrice(masterData: any, detailsArr: any, gifts: Array<any>) {
		if (!gifts || gifts.length <= 0) {
			return { newRes: [] };
		}
		const url = `FHH/EM1HNCRM/API/v1/object/available_range/service/get_real_price`,
			oDetails = { [this.detailApiName]: detailsArr },
			details = (PPM as any).parsePriceBookDataRangeDetails(oDetails, this.detailDesc?.fields?.price_book_id),
			args = {
				accountId: masterData.account_id,
				partnerId: masterData.partner_id || "",
				mcCurrency: masterData.mcCurrency,
				fullProductList: gifts.map((gift) => {
					return {
						"rowId": gift.rowId,
						"productId": gift.product_id,
						"unit": gift.unit_id || gift.actual_unit,
						"priceBookId": masterData.price_book_id || "" //取主对象价目表数据
					};
				}),
				productIdList: gifts.map((gift) => gift.product_id),
				object_data: masterData,
				details
			};
		return PPM.ajax(this.request, url, args);
	}

}

