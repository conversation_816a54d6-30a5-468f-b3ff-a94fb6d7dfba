import { createRebateInstance, fieldMapData } from '../helpers/rebatehelper';
import { MiscContent, RebateQueryResponse } from '../src/package/data_interface';
import PPM from 'plugin_public_methods';

describe("rebate类测试", () => {
  let rebateInstance;

  beforeEach(() => {
    rebateInstance = createRebateInstance();
  });

  describe('reqRebate 查询返利接口入参方法校验', () => {
    // 模拟PPM.ajax方法
    const spy = jest.spyOn(PPM, 'ajax');
    spy.mockImplementation(() => Promise.resolve('mocked response'));

    // 初始化一些其他参数
    const param = {
      masterData: { ruleId1: 'ruleId1Value', ruleId2: 'ruleId2Value', misc: { rangeRebate: [{ rangeRuleId: 'rangeRuleIdValue' }] }, accountId: 'accountIdValue' },
      detailDataMap: { rowId1: { rowId: 'rowId1Value', misc_content: 'miscContentValue' } },
    };

    test('should handle missing extraArgs', () => {

      rebateInstance.reqRebate({}, param);

      expect(PPM.ajax).toHaveBeenCalled();
      const calledWithArgs = PPM.ajax.mock.calls[0][2];
      expect(calledWithArgs.fundAccountId).toBe("");
      expect(calledWithArgs.needRule).toBe(true);
      expect(calledWithArgs.changeRule).toBe(false);
      expect(calledWithArgs.rebateType).toBeUndefined();
      expect(calledWithArgs.ruleId).toBe("");
      expect(calledWithArgs.rangeRuleIds).toEqual([]);
    });

    test('should handle partial extraArgs', () => {

      const paramArgs = {
        masterData: {
          account_id: "accountIdValue",
          rebate_rule_id: 'ruleIdValue',
          rangeRebate: []
        },
        detailDataMap: { rowId1: { rowId: 'rowId1Value', misc_content: 'miscContentValue' } },
        rebateType: "Money"
      },
        extraArgs = {
          fundAccountId: "fundAccountIdValue"
        };

      rebateInstance.reqRebate(extraArgs, paramArgs);

      expect(PPM.ajax).toHaveBeenCalled();
      const calledWithArgs = PPM.ajax.mock.calls[1][2];
      expect(calledWithArgs).toEqual({
        requestId: expect.any(String),
        accountId: 'accountIdValue',
        edit: false,
        masterObjectApiName: 'SalesOrderObj',
        detailObjectApiName: 'SalesOrderProductObj',
        masterData: expect.any(Object),
        detailDataMap: expect.any(Object),
        fundAccountId: 'fundAccountIdValue',
        needRule: true,
        changeRule: false,
        rebateType: 'Money',
        ruleId: 'ruleIdValue',
        rangeRuleIds: []// ['rangeRule1', 'rangeRule2'],
      });
    });

    // 更多的测试...
  });



  describe('测试 parseRebate 方法', () => {

    it('入参为空数组时，返回空数组', () => {
      const result = rebateInstance.parseRebate([], [], []);
      expect(result.rebates).toEqual([]);
      expect(result.usedRebateIds).toEqual([]);
    });

    it('当未使用过返利单时，验证格式化 use_amount 为 0 && using 为false ', () => {
      const rebates = [{ id: '1' }, { id: '2' }] as Rebate[];
      const result = rebateInstance.parseRebate(rebates, [], []);
      expect(result.rebates[0].use_amount).toBe(0);
      expect(result.rebates[0].using).toBe(false);
      expect(result.rebates[1].use_amount).toBe(0);
      expect(result.rebates[1].using).toBe(false);
    });

    it('当匹配上使用过的返利单时，验证格式化 use_amount 正确 && using 为true ', () => {
      const rebates = [{ id: '1' }, { id: '2' }] as Rebate[];
      const usedRebates = [{ id: '1', amount: 10 }] as RebateUsed[];
      const result = rebateInstance.parseRebate(rebates, usedRebates, []);
      expect(result.rebates[0].use_amount).toBe(10);
      expect(result.rebates[0].using).toBe(true);
    });

    it('当返利单已被其他规则使用，验证格式化 disabled 为 true', () => {
      const rebates = [{ id: '1' }] as Rebate[];
      const usedIds = ['1'];
      const result = rebateInstance.parseRebate(rebates, [], usedIds);
      expect(result.rebates[0].disabled).toBe(true);
    });
  });


  describe('测试 checkMoneyRebate 方法，校验金额返利查询结果格式化', () => {

    const miscContent: MiscContent = {
      rebate: [
        { id: "1", amount: 50 },
        { id: "2", amount: 20 },
      ],
      rangeRebate: [
        { rangeRuleId: "range1", rangeRebates: [{ id: "3", amount: 10 }] },
      ]
    };

    const rebateData: RebateQueryResponse = {
      ruleId: "rule1",
      rules: [{ id: "rule1", name: "Rule One" }],
      limitMoney: 100,
      datas: [
        { id: "1", total: 100 },
        { id: "2", total: 50 },
        { id: "3", total: 30 },
      ],
      rangeRuleIds: ["range1"],
      rangeRules: [{ id: "range1", name: "Range One", limitMoney: 80, rangeRebateIds: ["3"] }],
      rangeData: [{ id: "3", total: 30 }]
    };

    test('验证无范围规则的返利单数组数据处理结果', () => {
      const result = rebateInstance.checkMoneyRebate(miscContent, rebateData);

      expect(result).toBeDefined();
      //expect(result).toBe(null);
      expect(result.rebateData[0].rebates[0].using).toBe(true);
      expect(result.rebateData[0].rebates[0].use_amount).toBe(50);
      expect(result.rebateData[0].rebates[1].using).toBe(true);
      expect(result.rebateData[0].rebates[1].use_amount).toBe(20);
      expect(result.rebateData[0].rebates[2].using).toBe(false);
      expect(result.rebateData[0].rebates[2].use_amount).toBe(0);
    });

    test('验证范围规则的返利单数组数据处理结果', () => {
      const result = rebateInstance.checkMoneyRebate(miscContent, rebateData);

      expect(result).toBeDefined();
      expect(result.rebateData[1].rebates[0].using).toBe(true);
      expect(result.rebateData[1].rebates[0].use_amount).toBe(10);
    });

    test('验证ruleId不在rules中的异常情况', () => {
      const alteredRebateData = { ...rebateData, ruleId: "nonexistentId" };
      const result = rebateInstance.checkMoneyRebate(miscContent, alteredRebateData);

      expect(result).toBeDefined();
      expect(result.rebateData[0].ruleName).toBe("");
    });

    test('验证已使用的返利单对应的返利规则和接口当前返回不一致，返利单应该为未使用', () => {
      const alteredMiscContent = {
        ...miscContent,
        rangeRebate: [
          { rangeRuleId: "nonexistentId", rangeRebates: [{ id: "3", amount: 10 }] },
        ]
      };
      const result = rebateInstance.checkMoneyRebate(alteredMiscContent, rebateData);

      expect(result).toBeDefined();
      expect(result.rebateData[1].rebates[0].using).toBe(false);
      expect(result.rebateData[1].rebates[0].use_amount).toBe(0);
    });
  });


describe('测试 collectUsedRebateMap 方法，校验收集已使用的金额返利单', () => {
    let yourClassInstance;
    let rebate;
    let rangeRebate;
    let ruleId;

    beforeEach(() => {
       

        // 初始化参数
        ruleId = 'testRuleId';
        rebate = [
            { id: 'rebateId1' },
            { id: 'rebateId2' },
        ];
        rangeRebate = [
            {
                rangeRuleId: 'rangeRuleId1',
                rangeRebates: [{ id: 'rangeRebateId1' }, { id: 'rangeRebateId2' }]
            },
            {
                rangeRuleId: 'rangeRuleId2',
                rangeRebates: [{ id: 'rangeRebateId3' }, { id: 'rangeRebateId4' }]
            },
        ];
    });

    it('should return a map with correct keys and values', () => {
        const result = yourClassInstance.collectUsedRebateMap(rebate, rangeRebate, ruleId);

        // 验证结果是否符合预期
        expect(result.get('rebateId1')).toBe(ruleId);
        expect(result.get('rebateId2')).toBe(ruleId);
        expect(result.get('rangeRebateId1')).toBe('rangeRuleId1');
        expect(result.get('rangeRebateId2')).toBe('rangeRuleId1');
        expect(result.get('rangeRebateId3')).toBe('rangeRuleId2');
        expect(result.get('rangeRebateId4')).toBe('rangeRuleId2');
    });
});






})
