import Rebate from '../src/package/rebate';

const fieldMapData = {
	"product_price": "product_price",
    "price_book_discount":"price_book_discount",
    "price_book_price":"price_book_price",
    "price_book_id":"price_book_id",
    "price_book_id__r":"price_book_id__r",
    "price_book_product_id": "price_book_product_id",
    "price_book_product_id__r":"price_book_product_id__r",
    "dataId":"dataId",
    "account_id":"account_id",
    "misc_content":"misc_content",
    "rebate_amortize_amount":"rebate_amortize_amount",
    "rebate_dynamic_amount":"rebate_dynamic_amount",
    "rebate_amount":"rebate_amount",
    "rebate_coupon_id":"rebate_coupon_id",
    "coupon_amortize_amount":"coupon_amortize_amount",
    "coupon_dynamic_amount":"coupon_dynamic_amount",
    "coupon_amount":"coupon_amount",
    "rebate_rule_id":"rebate_rule_id",
    "product_rebate_rule_id":"product_rebate_rule_id",
    "quantity":"quantity",
    "actual_unit":"actual_unit",
    "product_id":"product_id",
    "subtotal":"subtotal",
    "range_rebate_rule_ids":"range_rebate_rule_ids"
}; // 模拟字段映射对象

export function createRebateInstance() {
	// 替换为实际参数
	const requestId = "813e0c7876e4484c8e0fcff5d7cbc04e";
	const masterApiName = "SalesOrderObj";
	const detailApiName = "SalesOrderProductObj";
	const fromType = 'add';
	const request = {}; // 模拟请求对象
	const triggerCal = {}; // 模拟触发计算对象
	const masterFieldMap = {
		"_id": "_id",
		"account_id": "account_id",
		"partner_id": "partner_id",
		"mcCurrency": "mcCurrency",
		"price_policy_id": "price_policy_id",
		"price_policy_id__r": "price_policy_id__r",
		"price_policy_rule_ids": "price_policy_rule__ids",
		"policy_dynamic_amount": "policy_dynamic_amount",
	}; // 模拟主字段映射对象
	const fieldMap = fieldMapData; // 模拟字段映射对象

	const decimalMap = {  // 模拟小数映射对象
		"quantity": 2,
		"gift_amortize_price": 2,
		"product_price": 2,
		"price_book_price": 2,
		"policy_dynamic_amount": 2
	};
	const fields = {}; // 模拟字段对象
	const recordType = 'default__C';


	const rebateInstance = new Rebate(
		requestId,
		masterApiName,
		detailApiName,
		fromType,
        fields,
		fieldMap,
        decimalMap,
		request,
		triggerCal,
		recordType
	);

	return rebateInstance;
}

export { Rebate,fieldMapData,createRebateInstance };