import {HandlerManager} from "./package/HandlerManager";
import {MultiUnitContext} from "./package/MultiUnitContext";
import {events} from "./package/events";

/**
 * 多单位插件：http://wiki.firstshare.cn/pages/viewpage.action?pageId=163655731
 */
export default class MultiUnitPlugin {

    constructor(pluginService, pluginParam) {
        this.handlerManager = new HandlerManager(new MultiUnitContext(pluginService, pluginParam));
        this._uploadLog(pluginService, pluginParam);
    }

    apply() {
        let self = this;
        return Object.values(events).map(event => {
            return {
                event: event,
                functional: function (pluginExecResult, options) {
                    return self.handlerManager.handleEvent(event, pluginExecResult, options);
                }
            }
        });
    }

    _uploadLog(pluginService, pluginParam) {
        try {
            let describe = pluginParam && pluginParam.describe;
            let {objectApiName, pluginApiName} = describe || {};
            let bizLog = pluginService.api && pluginService.api.bizLog;
            bizLog && bizLog.log && bizLog.log({
                eventId: 'fs-crm-sfa-plugin-used',
                eventType: 'PROD',
                eventName: 'pv',
                apiName: objectApiName,
                module: pluginApiName,
            });
        } catch (e) {
        }
    }
};