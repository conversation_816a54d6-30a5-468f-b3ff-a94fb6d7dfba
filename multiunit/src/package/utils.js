export function isMultipleUnit(objectData, fieldApiName) {
    if (!objectData || !fieldApiName) {
        return false;
    }
    objectData = objectData.product_id__ro || objectData;
    let field_value = objectData[fieldApiName];
    let field_value__v = objectData[`${fieldApiName}__v`];
    let isMultiUnit = (field_value__v === undefined || field_value__v === null) ? field_value : field_value__v;
    if (typeof isMultiUnit === 'boolean') {
        return isMultiUnit;
    } else if (typeof isMultiUnit === 'string') {
        return isMultiUnit === '是' || isMultiUnit === 'true'
    }
    return false;
}

export function isObject(val) {
    return typeof val === 'object' && val != undefined;
}

export const isArray = (val) => {
    return Object.prototype.toString.call(val) === '[object Array]';
};

export function isEmpty(val) {
    return isObject(val) ? Object.keys(val).length === 0 : (typeof val === "boolean" || typeof val === "number") ? false : !val;
}

export function uuid() {
    var s = [];
    var hexDigits = '0123456789abcdef';
    for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-';
    var uuid = s.join('');
    return uuid.replace(new RegExp('-', 'gm'), '');
}

export const multiUnitPlacesDecimal = 'multi_unit_places_decimal';//多单位取价接口返回的数量字段的小数位数
export const getRealPriceResult = 'get_real_price_result';//getRealPrice接口返回的明细数据
export const rowId = "key_row_id";//存放在数据中的rowId，保证唯一，用来匹配接口返回的数据