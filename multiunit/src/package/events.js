export const events = Object.freeze({
    form_render_after: "form.render.after",//整个主从表单页渲染结束
    md_batchAdd_after: "md.batchAdd.after",//批量选lookup对象回来后
    field_change_before: "field.change.before",//字段变更前
    field_change_after: "field.change.after",//字段变更后
    md_edit_before: "md.edit.before",//字段变更前
    md_edit_after: "md.edit.after",//字段变更后
    md_batchAdd_before_cal_uievent: "md.batchAdd.before_cal_uievent",//批量添加从对象后在计算和ui事件前
    multiunit_batchTodoMultiunit: "multiunit.batchTodoMultiunit",//给其他插件用，订单从历史添加的时候用了
    set_field_readonly: "multiunit.setFieldReadOnly", // 设置字段只读

    //下方为取价插件事件
    md_batchAdd_selectSkuConfig: "md.batchAdd.selectSkuConfig",//取价插件事件：跳转选产品页面时添加多单位配置信息
    priceService_batchAdd_getPriceParam: "priceService.batchAdd.getPriceParam",//批量选sku后，处理取价接口入参
    priceService_batchAdd_matchGetPriceResult: "priceService.batchAdd.matchGetPriceResult",//批量选sku后，处理匹配取价结果数据
    priceService_form_getPriceParam: "priceService.form.getPriceParam",//在表单页面取价时，处理取价接口入参。（复制映射、更换主对象客户、更换从对象价目表等入口触发的取价）
    priceService_form_matchGetPriceResult: "priceService.form.matchGetPriceResult",//在表单页面取价时，处理匹配取价结果数据
    priceService_batchAddAfter_parseData: "price-service.batchAddAfter.parseData",//在表单页面取价时，处理匹配取价结果数据
    bom_md_edit_after: "bom.md.edit.after", //bom编辑之后
    bom_md_batchAdd_end: "bom.md.batchAdd.end", //bom批量添加之后

    md_render_before: "md.render.before",//整个主从表单页渲染结束
    md_copy_after: "md.copy.after",    // 复制

    md_tile_before: "md.tile.before",  // 平铺展开


});

//向外抛出的事件
export const emitEvent = Object.freeze({
    multiUnit_batchAdd_calcPriceParam: "multiUnit.batchAdd.calcPriceParam",//批量选sku后，处理多单位取价接口入参
    multiUnit_form_calcPriceParam: "multiUnit.form.calcPriceParam",//在表单页进行多单位取价时，处理多单位取价接口入参
});