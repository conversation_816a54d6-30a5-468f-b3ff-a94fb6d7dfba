/*
 * @Author: wang<PERSON><PERSON>
 * @Date: 2022-02-10 10:36:36
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-08-01 18:36:58
 * @FilePath: /plugins/multiunit/src/package/HandlerManager.js
 * @Description: 
 * 
 * Copyright (c) 2022 by 用户/公司名, All Rights Reserved. 
 */
import {BatchAddAfter} from "./handler/BatchAddAfter";
import {FieldChangeAfter} from "./handler/FieldChangeAfter";
import {FieldChangeBefore} from "./handler/FieldChangeBefore";
import {BatchTodoMultiunit} from "./handler/BatchTodoMultiunit";
import {FormRenderAfter} from "./handler/FormRenderAfter";
import {BatchAddBeforeCalUiEvent} from "./handler/BatchAddBeforeCalUiEvent"
import {PriceService} from './handler/PriceService'
import {Bom} from './handler/Bom'
import {CopyAfter} from './handler/CopyAfter'
import {MdTileBefore} from './handler/MdTileBefore'
import {BaseHandler} from './handler/BaseHandler'
import {events} from "./events";

export class HandlerManager {

    constructor(multiUnitContext) {
        this.handlers = {};
        this.multiUnitContext = multiUnitContext;
    }

    async handleEvent(event, pluginExecResult, options) {
        let handler = this.getHandler(event, options);
        return handler && handler.handleEvent && await handler.handleEvent(options, pluginExecResult, event);
    }

    getHandler(event, options) {
        let handler = this.handlers[event];
        if (handler) {
            return handler;
        }
        if (event === events.form_render_after || event === events.md_render_before) {
            handler = new FormRenderAfter(this.multiUnitContext);
        } else if (event === events.md_batchAdd_after) {
            handler = new BatchAddAfter(this.multiUnitContext);
        } else if ([events.field_change_after, events.md_edit_after].includes(event)) {
            handler = new FieldChangeAfter(this.multiUnitContext);
        } else if ([events.field_change_before, events.md_edit_before].includes(event)) {
            handler = new FieldChangeBefore(this.multiUnitContext);
        } else if (event === events.multiunit_batchTodoMultiunit) {
            handler = new BatchTodoMultiunit(this.multiUnitContext);
        } else if (event === events.md_batchAdd_before_cal_uievent) {
            handler = new BatchAddBeforeCalUiEvent(this.multiUnitContext);
        } else if ([events.md_batchAdd_selectSkuConfig,
            events.priceService_batchAdd_getPriceParam,
            events.priceService_batchAdd_matchGetPriceResult,
            events.priceService_form_getPriceParam,
            events.priceService_form_matchGetPriceResult,
            events.priceService_batchAddAfter_parseData].includes(event)) {
            handler = new PriceService(this.multiUnitContext)
        }else if (event === events.md_copy_after) {
            handler = new CopyAfter(this.multiUnitContext);
        }else if (event === events.md_tile_before) {
            handler = new MdTileBefore(this.multiUnitContext);
        }else if (event === events.set_field_readonly) {
            let BH = new BaseHandler(this.multiUnitContext);
            BH.setFieldReadonly(options);
        } else if (
            [events.bom_md_edit_after, events.bom_md_batchAdd_end].includes(event)
        ) {
            handler = new Bom(this.multiUnitContext);
        }
        if (handler) {
            this.handlers[event] = handler;
        }
        return handler;
    }
}