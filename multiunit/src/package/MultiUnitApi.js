export class MultiUnitApi {

    constructor(http) {
        this.http = http;
    }

    calc({params, mcCurrency, detailObjectApiName, masterObjectApiName}) {
        return this.request({
            url: 'FHH/EM1ANCRM/API/v1/object/mutipleUnit/service/calc',
            data: {
                params,
                mcCurrency: mcCurrency,
                objectApiName: masterObjectApiName,
                detailObjectApiName,
            }
        }).then(res => {
            return res && res.caclResult;
        })
    }

    getOptions(dataId, sourceObjApiName) {
        return this.request({
            url: 'FHH/EM1ANCRM/API/v1/object/lazyLoadOptions/service/getLazyLoadOptions',
            data: {
                dataId,
                dataObjApiName: 'ProductObj',
                targetObjApiName: 'UnitInfoObj',
                sourceObjApiName,
                extend_info: undefined
            }
        }).then(res => {
            return res && res.optionList
        });
    }

    batchCalculate(opts) {
        let {masterObjectApiName, masterData, detailDataMap, modifiedObjectApiName, modifiedDataIndexList, calculateFields} = opts;
        return this.request({
            url: 'FHH/EM1HNCRM/API/v1/object/calculate/service/batchCalculate',
            data: {
                masterObjectApiName: masterObjectApiName,
                masterData: masterData,
                detailDataMap: detailDataMap,
                modifiedObjectApiName: modifiedObjectApiName,
                modifiedDataIndexList: modifiedDataIndexList,
                calculateFields: calculateFields
            }
        }).then(res => {
            return res && res.calculateResult;
        })
    }

    request({url, data}) {
        try {
            if (this.http) {
                return this.http({url, data})
                    .then(result => {
                        return this.handleHttpResult(result);
                    })
            }
            return Promise.reject('MultiUnitApi：http can not be null');
        }catch (e) {
            console.log(e)
        }
    }

    handleHttpResult(rst) {
        //异常情况没解析成 json
        if (typeof rst === 'string') {
            throw rst;
        }
        if (rst.Error || rst.Result.FailureCode != 0 || rst.Result.StatusCode != 0) {
            if (rst.Error && rst.Error.Message) {
                throw rst.Error.Message
            }
            if (rst.Result.FailureMessage) {
                throw rst.Result.FailureMessage
            }
            throw '未知异常'
        } else {
            return rst.Value
        }
    }
}