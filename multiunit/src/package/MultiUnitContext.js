import {MultiUnitApi} from "./MultiUnitApi";
import {FieldConvert} from "./FieldConvert";

export class MultiUnitContext {

    constructor(pluginService, pluginParam) {
        let request = pluginService && pluginService.api && pluginService.api.request;
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
        this.multiUnitApi = new MultiUnitApi(request);
        this.fieldConvert = new FieldConvert(pluginParam);
    }

    alert(message) {
        this.pluginService.api.alert && this.pluginService.api.alert(message);
    }

    getMasterFields() {
        return this.fieldConvert.getMasterFields();
    }

    getDetailFields(objApiName) {
        return this.fieldConvert.getDetailFields(objApiName);
    }

    getFirstDetailObjApiName() {
        let objApiNames = this.fieldConvert.getDetailObjApiNames();
        return objApiNames && objApiNames.length && objApiNames[0];
    }

    calcPriceByUnit(param) {
        this.showLoading();
        return this.multiUnitApi.calc(param)
            .then(rst => {
                this.hideLoading();
                return rst;
            }).catch(err => {
                this.hideLoading();
                throw err;
            });
    }

    getLazyLoadOptions(productId, objApiName) {
        this.showLoading();
        return this.multiUnitApi.getOptions(productId, objApiName)
            .then(rst => {
                this.hideLoading();
                return rst;
            }).catch(err => {
                this.hideLoading();
                throw err;
            });
    }

    batchCalculate(opts) {
        this.showLoading();
        return this.multiUnitApi.batchCalculate(opts)
            .then(rst => {
                this.hideLoading();
                return rst;
            }).catch(err => {
                this.hideLoading();
                throw err;
            });
    }

    //是否开启了价目表
    isOpenPriceBook() {
        let bizStateConfig = this.pluginParam && this.pluginParam.bizStateConfig;
        return bizStateConfig && bizStateConfig['openPriceList']
    }

    // 兼容web和移动端开关
    isOpenMultiUnitPriceBook() {
        let status = this.pluginParam && this.pluginParam.bizStateConfig && this.pluginParam.bizStateConfig['multi_unit_price_book'];
        return status == '1' || status === true;
    }

    async runPlugin(name, param) {
        let result = await this.pluginService.run(name, param);
        if (result && result.StatusCode === 0) {
            return result.Value;
        }
    }

    showLoading() {
        this.pluginService.api && this.pluginService.api.showLoading && this.pluginService.api.showLoading();
    }

    hideLoading() {
        this.pluginService.api && this.pluginService.api.hideLoading && this.pluginService.api.hideLoading();
    }
}