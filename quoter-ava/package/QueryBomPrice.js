import {QuoterCalculate} from "./QuoterCalculate";
import {isEmpty} from '../../pluginbase-ava/package/pluginutils';
import advancedFormulaCache from "./advancedFormulaCache";

export class QueryBomPrice {

    constructor(context) {
        let {fieldMapping, pluginApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.quoterCalculate = new QuoterCalculate(context);
    }

    bomQueryBomPriceParseParamsBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData;
        let {objApiName, objectData} = options;
        if (!isEmpty(objectData)) {
            let fieldMapping = this.fieldMapping.getDetailFields(objApiName);
            let {product_id, bom_id, price} = fieldMapping;
            let {[product_id]: productId, [bom_id]: bomId} = objectData;
            let triggerResultFields = advancedFormulaCache.batchGetTriggerResultFields([productId, bomId]);
            if (triggerResultFields && triggerResultFields.length) {
                let advancePriceFlag = triggerResultFields.includes(price);
                let parseResult = this.pluginApi.runPluginSync('quoter.parseQueryBomPriceParams.before.sync', Object.assign({}, options, {
                    advancePricingResultFields: triggerResultFields
                }));
                return Object.assign({}, preData, {advancePriceFlag, updateFlag: true}, parseResult);
            }
        }
    }

    async bomReconfigurationEnd(pluginExecResult, options) {
        let {objApiName, dataGetter, dataIndex} = options;
        objApiName = objApiName || this.fieldMapping.getFirstDetailObjApiName();
        let objectData = dataGetter.getData(objApiName, dataIndex);
        if (!objectData) {
            return;
        }
        let subDetails = this.pluginApi.runPluginSync('bom.getSubDetailsFromPkg.sync', Object.assign(options,
            {objApiName, dataIndex}));
        let detailDataList = [objectData, ...(subDetails || [])];
        await this.quoterCalculate.bomReconfigurationEnd(options, detailDataList);
    }
}