<view hidden="{{hidden}}" style="display: flex; flex-direction: column; background: #ffffff;">
    <block wx:for="{{dSingleAttributeDataList}}" wx:key="constraintId">
        <attribute wx:if="{{item.isStandardAttribute}}" attribute="{{item}}" hidden="{{hidden||dHiddenAttributeMap[item.constraintId]}}"
                   bindattributeValueChanged="attributeValueChanged"></attribute>
        <nostandardattribute wx:else noStandardAttribute="{{item}}" hidden="{{hidden||dHiddenAttributeMap[item.constraintId]}}"
                             bindattributeValueChanged="attributeValueChanged"></nostandardattribute>
    </block>
    <block wx:for="{{dRepeatAttributeDataList}}" wx:key="constraintId">
        <attribute wx:if="{{item.isStandardAttribute}}" attribute="{{item}}" hidden="{{hidden||dHiddenAttributeMap[item.constraintId]}}"
                   bindattributeValueChanged="attributeValueChanged"></attribute>
        <nostandardattribute wx:else noStandardAttribute="{{item}}" hidden="{{hidden||dHiddenAttributeMap[item.constraintId]}}"
                             bindattributeValueChanged="attributeValueChanged"></nostandardattribute>
    </block>
    <view hidden="{{hidden}}" style="display: flex; flex-direction: row-reverse; margin: 20rpx">
        <view class="generate-details" bindtap="generateDetails">
            <text style="font-size: 24rpx; color: #ffffff">{{dGenerateDetails}}</text>
        </view>
    </view>
</view>