//不能引用objformmain/base/form/fieldcell/fieldcell_b，getFieldCom()会导致找不到组件，不触发refreshFieldValue()
import {each, i18n, isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import {AttributePicker} from '../AttributePicker';
import fsapi from "fs-hera-api";
import FieldMapping from "../../../pluginbase-ava/package/FieldMapping";
import {defFieldMapping} from "../utils";

Component({

    properties: {
        item: {
            type: Object,
            value: null
        },

        hidden: {
            type: Boolean,
            value: false,
        }
    },

    data: {
        dAttributeDataList: undefined,//要展示属性、非标属性数据列表
        dSingleAttributeDataList: undefined,//单条展示属性、非标属性数据列表
        dRepeatAttributeDataList: undefined,//重复展示属性、非标属性数据列表
        dHiddenAttributeMap: undefined,//隐藏的属性、非标属性数据，{constraintId:true}
        dGenerateDetails: undefined,//生成明细
    },

    methods: {
        refreshFieldValue({value: quoterData}) {
            if (isEmpty(quoterData)) {
                return;
            }
            quoterData = JSON.parse(quoterData);
            if (!quoterData || !quoterData.refreshCmpt) {
                return;
            }
            let picker = this.getAttributePicker();
            picker.release();
            let {attributeData, attributeConstraintList, selectedData, version} = quoterData;
            this.attributeData = attributeData || {};//不要修改该数据
            this.attributeConstraintList = attributeConstraintList || [];//不要修改该数据
            this.version = version;
            let attributeList = this.getAttributeList(attributeConstraintList, attributeData);
            if (!isEmpty(selectedData)) {
                each(selectedData, (value, key) => {
                    let {attribute_value} = value;
                    let attributeData = attributeList && attributeList.find(it => it.id === key);
                    let {isStandardAttribute} = attributeData || {};
                    if (isStandardAttribute) {
                        (attribute_value && attribute_value.length) && (picker.pick({[key]: attribute_value}, false));
                    } else {
                        (!isEmpty(attribute_value)) && (picker.pick({[key]: [attribute_value]}, false));
                    }
                })
            }
            attributeList = this.simplifyAttributeList(attributeList, this.attributeData, picker.getAttributeMap());
            attributeList && attributeList.length && attributeList.forEach(attribute => {
                let {constraintId, attributeValues} = attribute;
                attributeValues && attributeValues.length && attributeValues.forEach(attributeValue => {
                    let {_id, isRequired, isSelected} = attributeValue;
                    (isRequired || isSelected) && (picker.pick({[constraintId]: [_id]}, false));
                });
            });
            let attributeMap = picker.getAttributeMap();
            let hiddenAttributeMap = this.getHiddenAttributeMap(attributeList, attributeConstraintList, attributeMap);
            let {singleAttributeDataList, repeatAttributeDataList} = this.getDisplayDataList(attributeList, hiddenAttributeMap, attributeMap);
            this.data.dAttributeDataList = attributeList;
            this.setData({
                dSingleAttributeDataList: singleAttributeDataList,
                dRepeatAttributeDataList: repeatAttributeDataList,
                dHiddenAttributeMap: hiddenAttributeMap
            });
            this.updateCommitFieldValue(false);//触发一次更新字段值逻辑，保证字段值正确
        },

        simplifyAttributeList(attributeList, attributeData, selectedAttributeMap) {
            return attributeList && attributeList.length && attributeList.map(it => {
                let { values, isMultiSelected, isRequired, isStandardAttribute, id, parentId, requiredSelected, nodeType } = it;
                let attributeId = values && values.length && values[0];
                let showAttributeValues = [];//当前属性要显示的属性值
                if (nodeType === 1) {
                    //该属性值的所有约束
                    let allConstraints = attributeList.filter(it => {
                        let _attributeId = it.values && it.values.length && it.values[0];
                        return attributeId === _attributeId;
                    });
                    //每一条约束约束的属性值，取并集
                    allConstraints && allConstraints.length && allConstraints.forEach(it => {
                        it.children && it.children.length && it.children.forEach(it => {
                            let { nodeType, values } = it;
                            if (nodeType === 2) {
                                showAttributeValues.push(...(values || []))
                            }
                        })
                    })
                }
                let currentAttributeData = attributeData && attributeData[attributeId];
                let {name, field_num, attribute_values} = currentAttributeData || {};
                let selectedValues = selectedAttributeMap && selectedAttributeMap[id];
                let attributeValues = attribute_values && attribute_values.length && attribute_values.map(it => {
                    let isRequired = requiredSelected && requiredSelected.includes(it._id);
                    let selected = selectedValues && selectedValues.length && selectedValues.includes(it._id);
                    return Object.assign({}, it, {isRequired, isSelected: isRequired || selected});
                });
                if (showAttributeValues && showAttributeValues.length) {
                    attributeValues = attributeValues && attributeValues.filter(it => {
                        return showAttributeValues.includes(it._id)
                    }).sort((a, b) => {
                        return showAttributeValues.indexOf(a._id) - showAttributeValues.indexOf(b._id);
                    })
                }
                let multiSelectText = i18n('ava.object_form.multi_select')/*(多选)*/;
                let attributeName = isMultiSelected ? (name + `(${multiSelectText})`) : name;
                return Object.assign({
                    _id: attributeId,
                    name: attributeName,
                    orgName: name,
                    isMultiSelected,
                    isRequired,
                    isStandardAttribute,
                    constraintId: id,
                    parentConstraintId: parentId,
                    field_num
                }, isStandardAttribute ? {attributeValues,} : {value: selectedValues && selectedValues.length ? selectedValues[0] : null})
            }) || [];
        },

        getAttributeList(dataList, attributeData) {
            let attributeList = [];
            dataList && dataList.length && dataList.forEach(it => {
                let appendChild = true;
                let {nodeType, children, values} = it;
                if (nodeType === 1) {
                    let attributeId = values && values.length && values[0];
                    let currentAttributeData = attributeData && attributeData[attributeId];
                    if (isEmpty(currentAttributeData)) {
                        appendChild = false;
                    } else {
                        attributeList.push(it);
                    }
                }
                if (appendChild) {
                    let childResult = this.getAttributeList(children, attributeData);
                    if (childResult && childResult.length) {
                        attributeList.push(...childResult);
                    }
                }
            });
            return attributeList;
        },

        attributeValueChanged(e) {
            let attributeData = e.detail || {};
            let picker = this.getAttributePicker();
            //更新重复属性的属性值
            let sameAttributeData = {};
            let attributeDataList = this.data.dAttributeDataList;
            if (attributeDataList && attributeDataList.length) {
                let attributeMap = picker.getAttributeMap();
                let newAttributeMap = Object.assign({}, attributeMap, attributeData);
                each(attributeData, (attributeValue, constraintId) => {
                    let attribute = attributeDataList.find(it => it.constraintId === constraintId);
                    let sameAttributeList = attributeDataList.filter(it => {
                        let {_id, constraintId: currentConstraintId} = it;
                        return _id === (attribute && attribute._id) && currentConstraintId !== constraintId
                    });
                    if (sameAttributeList && sameAttributeList.length) {
                        sameAttributeList.forEach(sameAttribute => {
                            let {constraintId} = sameAttribute;
                            newAttributeMap[constraintId] = attributeValue;
                        });
                        //根据最新的已选属性的属性值，获取隐藏的属性
                        let hiddenAttributeMap = this.getHiddenAttributeMap(attributeDataList, this.attributeConstraintList, newAttributeMap);
                        sameAttributeList.forEach(sameAttribute => {
                            let {constraintId} = sameAttribute;
                            let isHidden = hiddenAttributeMap && hiddenAttributeMap[constraintId];
                            if (!isHidden) {
                                sameAttributeData[constraintId] = attributeValue;
                            }
                        })
                    }
                });
            }
            picker.pick(Object.assign(attributeData, sameAttributeData));
            this.updateCommitFieldValue();
        },

        getAttributePicker() {
            let picker = this.attributePicker;
            if (!picker) {
                picker = new AttributePicker(() => {
                    this.onSelectedAttributeChanged();
                });
                this.attributePicker = picker;
            }
            return picker;
        },

        onSelectedAttributeChanged() {
            let attributeDataList = this.data.dAttributeDataList;
            let picker = this.getAttributePicker();
            let attributeMap = picker.getAttributeMap();
            let hiddenAttributeMap = this.getHiddenAttributeMap(attributeDataList, this.attributeConstraintList, attributeMap);
            //清除隐藏的属性的已选属性值（属性值必选的不清除）
            each(hiddenAttributeMap, (hidden, key) => {
                if (hidden) {
                    let hiddenAttributeData = attributeDataList && attributeDataList.find(it => it.constraintId === key);
                    let {isStandardAttribute, attributeValues} = hiddenAttributeData || {};
                    hiddenAttributeData && (hiddenAttributeData.toastText = null);//隐藏后清除err信息
                    picker.unpick(key, false);
                    if (isStandardAttribute) {
                        let requiredAttributeValueIds = attributeValues && attributeValues.filter(it => it.isRequired).map(it => it._id);
                        picker.pick({[key]: requiredAttributeValueIds}, false);
                    }
                }
            });
            //再根据最新的已选数据更新页面
            let newAttributeMap = picker.getAttributeMap();
            attributeDataList && attributeDataList.forEach(attributeData => {
                let {constraintId, isStandardAttribute, attributeValues, isRequired, toastText} = attributeData;
                let selectedValues = newAttributeMap[constraintId];
                if (isStandardAttribute) {
                    attributeValues && attributeValues.forEach(it => {
                        let {_id, isRequired} = it;
                        it.isSelected = isRequired ? true : (selectedValues && selectedValues.includes(_id));
                    })
                } else {
                    let selectValue = selectedValues && selectedValues[0];
                    attributeData.value = selectValue || null;
                }
                if (isRequired && selectedValues && selectedValues.length) {//必填且选择了属性值，清除err信息
                    attributeData.toastText = null;
                }
            });
            let {singleAttributeDataList, repeatAttributeDataList} = this.getDisplayDataList(attributeDataList, hiddenAttributeMap, newAttributeMap);
            this.data.dAttributeDataList = attributeDataList;
            this.setData({
                dSingleAttributeDataList: singleAttributeDataList,
                dRepeatAttributeDataList: repeatAttributeDataList,
                dHiddenAttributeMap: hiddenAttributeMap
            });
        },

        getHiddenAttributeMap(attributeDataList, attributeConstraintList, selectedAttributeMap) {
            let hiddenAttributeMap = {};
            attributeDataList && attributeDataList.forEach(attributeData => {
                let selected = this.checkIsSelected(attributeData, attributeDataList, attributeConstraintList, selectedAttributeMap);
                hiddenAttributeMap[attributeData.constraintId] = !selected;
            });
            return hiddenAttributeMap;
        },

        findAttributeConstraintNode(attributeConstraintId, attributeConstraintList) {
            let result;
            attributeConstraintList && attributeConstraintList.length && attributeConstraintList.forEach(it => {
                let {id, children} = it;
                if (attributeConstraintId === id) {
                    result = it;
                }
                if (!result) {
                    result = this.findAttributeConstraintNode(attributeConstraintId, children);
                }
            })
            return result;
        },

        checkIsSelected(attributeData, attributeDataList, attributeConstraintList, selectedAttributeMap) {
            let {parentConstraintId} = attributeData;
            if (isEmpty(parentConstraintId)) {
                return true;
            }
            let selected = false;
            let fatherNode = this.findAttributeConstraintNode(parentConstraintId, attributeConstraintList);
            let {parentId: grandpaId, values: fatherNodeValues, isStandardAttribute} = fatherNode || {};
            let grandpaNode = this.findAttributeConstraintNode(grandpaId, attributeConstraintList);
            let {id: grandpaNodeConstraintId} = grandpaNode || {};
            let selectedAttributeValues = selectedAttributeMap && selectedAttributeMap[grandpaNodeConstraintId];
            if (selectedAttributeValues && selectedAttributeValues.length) {
                if (isStandardAttribute) {//属性
                    selected = selectedAttributeValues.some(selectedAttributeValue => {
                        return fatherNodeValues && fatherNodeValues.includes(selectedAttributeValue);
                    });
                } else {//非标属性
                    let noStandardAttributeValue = selectedAttributeValues[0];//输入的非标属性值
                    let ranges = fatherNodeValues && fatherNodeValues.length && fatherNodeValues.map(it => {
                        let arr = it.split(',');
                        return {left: arr[0], right: arr[1]}
                    });
                    selected = ranges && ranges.length && ranges.some(it => {
                        return this.inRange(noStandardAttributeValue, it.left, it.right);
                    });
                }
            }
            if (selected) {
                let grandpaAttributeData = attributeDataList && attributeDataList.find(it => it.constraintId === grandpaNodeConstraintId);
                return this.checkIsSelected(grandpaAttributeData, attributeDataList, attributeConstraintList, selectedAttributeMap);
            }
            return false;
        },

        inRange(value, left, right) {
            if (isEmpty(value)) {
                return false;
            }
            if (isEmpty(left) && isEmpty(right)) {
                return false;
            }
            if (isEmpty(left)) {
                return Number(value) <= Number(right);
            }
            if (isEmpty(right)) {
                return Number(value) > Number(left);
            }
            return Number(value) > Number(left) && Number(value) <= Number(right);
        },

        generateDetails() {
            let {dAttributeDataList: attributeDataList, dHiddenAttributeMap: hiddenAttributeMap} = this.data;
            if (!attributeDataList || !attributeDataList.length) {
                fsapi.util.showToast(i18n('ava.object_form.quoter.selected_attribute_constraint_first')/*请先选择属性级联约束*/);
                return;
            }
            //校验显示的必填属性是否填写，未填写给提示
            let picker = this.getAttributePicker();
            let attributeMap = picker.getAttributeMap();
            let checkOK = attributeDataList && attributeDataList.filter(it => {
                let {isRequired, constraintId} = it;
                let isHidden = hiddenAttributeMap && hiddenAttributeMap[constraintId];
                return isRequired && !isHidden;
            }).every(it => {
                let {isStandardAttribute, constraintId} = it;
                let selectedValues = attributeMap && attributeMap[constraintId];
                let hasSelected = selectedValues && selectedValues.length;
                it.toastText = (hasSelected ? null : (isStandardAttribute
                    ? i18n('ava.object_form.pleaseselect')/*请选择*/
                    : i18n('ava.object_form.please_fill_in')/*请填写*/));
                return hasSelected;
            });
            if (!checkOK) {
                let {singleAttributeDataList, repeatAttributeDataList} = this.getDisplayDataList(attributeDataList, hiddenAttributeMap, attributeMap);
                this.data.dAttributeDataList = attributeDataList;
                this.setData({
                    dSingleAttributeDataList: singleAttributeDataList,
                    dRepeatAttributeDataList: repeatAttributeDataList,
                })
                fsapi.util.showToast(i18n('ava.object_form.quoter.must_attribute_not_filled')/*有必填的属性未填写*/);
                return;
            }
            let showAttributeDataList = attributeDataList && attributeDataList.filter(it => {
                let isHidden = hiddenAttributeMap && hiddenAttributeMap[it.constraintId];
                return !isHidden;
            });
            let selectedAttributeValues = {};
            let selectedNoStandardAttributeValues = {};
            let attributeFieldNum = {};
            showAttributeDataList && showAttributeDataList.length && showAttributeDataList.forEach(it => {
                let {isStandardAttribute, constraintId, _id, field_num} = it;
                let selectedValues = attributeMap && attributeMap[constraintId];
                if (selectedValues && selectedValues.length) {
                    if (isStandardAttribute) {
                        let preValues = selectedAttributeValues[_id];
                        if (!preValues) {
                            preValues = [];
                            selectedAttributeValues[_id] = preValues;
                        }
                        preValues.push(...selectedValues);
                        attributeFieldNum[_id] = field_num;
                    } else {
                        let preValues = selectedNoStandardAttributeValues[_id];
                        if (!preValues) {
                            preValues = [];
                            selectedNoStandardAttributeValues[_id] = preValues;
                        }
                        preValues.push(...selectedValues);
                    }
                }
            });
            let formContext = this.data.item && this.data.item.getFormContext && this.data.item.getFormContext();
            formContext && formContext.catchRunPluginHook("quoter.generateDetails.before", {
                selectedAttributeValues,
                selectedNoStandardAttributeValues,
                attributeFieldNum
            });
        },

        updateCommitFieldValue(triggerChanged = true) {
            let picker = this.getAttributePicker();
            let attributeMap = picker.getAttributeMap();
            let {apiname: fieldName, setFormObjData} = this.data.item;
            let {dAttributeDataList: attributeDataList} = this.data;
            let fieldValue = {};
            let fieldValueLabels = [];
            let fieldMapping = this.getFieldMapping();
            let {quoter_label} = fieldMapping.getMasterFields()
            attributeDataList && attributeDataList.length && attributeDataList.forEach(it => {
                let {_id, name, orgName, constraintId, isStandardAttribute, attributeValues: orgAttributeValues} = it;
                let attributeName = orgName || name || '';
                let attributeValues = attributeMap && attributeMap[constraintId];
                if (attributeValues && attributeValues.length) {
                    fieldValue[constraintId] = {
                        attribute_id: _id,
                        attribute_value: isStandardAttribute ? attributeValues : attributeValues[0],
                        isStandardAttribute
                    }
                    let attributeValueName;
                    if (isStandardAttribute) {
                        attributeValueName = attributeValues.map(it => {
                            let result = orgAttributeValues && orgAttributeValues.find(ij => {
                                return it === ij._id;
                            });
                            return result && result.name || '';
                        }).join(',');
                    } else {
                        attributeValueName = attributeValues[0];
                    }
                    fieldValueLabels.push(`【${attributeName}:${attributeValueName}】`);
                }
            })
            let changeData = Object.assign({},
                triggerChanged && {'quoter_value_changed': true},
                !isEmpty(fieldValue) && {
                    [fieldName]: JSON.stringify({version: (this.version || 0), selectedData: fieldValue}),
                }, (quoter_label) && {
                [quoter_label]: (fieldValueLabels && fieldValueLabels.length) ? fieldValueLabels.join(' ') : null
            })
            setFormObjData && setFormObjData({changeData});
        },

        getFieldMapping() {
            let fieldMapping = this.fieldMapping;
            if (!fieldMapping) {
                let fieldAttr = this.data.item && this.data.item.getFieldAttr && this.data.item.getFieldAttr();
                let params = fieldAttr && fieldAttr.customProps && fieldAttr.customProps.pluginInfo && fieldAttr.customProps.pluginInfo.params;
                fieldMapping = new FieldMapping(params, defFieldMapping);
                this.fieldMapping = fieldMapping;
            }
            return fieldMapping;
        },

        getDisplayDataList(attributeList, hiddenAttributeMap, selectedMap) {
            let singleAttributeDataList = [];
            let repeatAttributeDataList = [];
            if (!attributeList || !attributeList.length) {
                return {singleAttributeDataList, repeatAttributeDataList}
            }
            let countObj = {};
            let indexObj = {};
            let index = 0;
            attributeList.forEach(attribute => {
                let {_id} = attribute;
                let count = countObj[_id];
                countObj[_id] = ((isEmpty(count)) ? 1 : (++count));
                let realIndex;
                let indexValue = indexObj[_id];
                if (isEmpty(indexValue)) {
                    indexObj[_id] = index;
                    realIndex = index;//非重复属性index唯一
                    index++;
                } else {
                    realIndex = indexValue;//重复属性的index保持一致
                }
                attribute.index = realIndex;//为每一条属性添加index，排序用
            });
            attributeList.forEach(attribute => {
                let {_id, constraintId} = attribute;
                let count = countObj[_id];
                if (count > 1) {
                    let isHidden = hiddenAttributeMap && hiddenAttributeMap[constraintId];//是否隐藏
                    let selected = repeatAttributeDataList.find(it => it._id === _id);
                    let hasSelected = !!selected;//是否已选
                    let sameAttributeList = attributeList.filter(it => it._id === _id);
                    let selectedAttribute = sameAttributeList && sameAttributeList.find(it => {
                        let selectedValue = selectedMap && selectedMap[it.constraintId];
                        return !isEmpty(selectedValue);
                    });
                    let canPush = selectedAttribute ? (selectedAttribute.constraintId === constraintId) : true;
                    if ((!isHidden) && (!hasSelected) && canPush) {
                        repeatAttributeDataList.push(attribute);
                    }
                } else {
                    singleAttributeDataList.push(attribute);
                }
            });
            repeatAttributeDataList = repeatAttributeDataList.sort((pre, next) => {
                return Number(pre.index) - Number(next.index);
            });
            return {singleAttributeDataList, repeatAttributeDataList}
        }
    },

    lifetimes: {
        attached() {
            this.setData({
                dGenerateDetails: i18n('ava.object_form.quoter.generate_details')/*生成明细*/
            })
        },
        detached() {
            let picker = this.getAttributePicker();
            picker.release();
        }
    }
})