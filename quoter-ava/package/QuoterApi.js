import {request} from "../../pluginbase-ava/package/pluginutils";

export default class QuoterApi {

    constructor(http) {
        this.http = http;
    }

    queryAttributeConstraintById(params) {
        return request(this.http, {
            url: 'FHH/EM1HNCRM/API/v1/object/quoter_sfa/service/query_by_id',
            data: params
        }).then(result => {
            return result;
        });
    }

    queryAdvancedFormulaList(params) {
        return request(this.http, {
            url: 'FHH/EM1HNCRM/API/v1/object/quoter_sfa/service/queryAdvancedFormulaList',
            data: params
        }).then(result => {
            return result;
        });
    }

    quoteBatchCalculate(params) {
        return request(this.http, {
            url: 'FHH/EM1HNCRM/API/v1/object/quoter_sfa/service/batchCalculate',
            data: params
        }).then(result => {
            return result;
        });
    }

    list(apiName, filters) {
        return request(this.http, {
            url: `/FHH/EM1HNCRM/API/v1/object/${apiName}/controller/List`,
            data: {
                include_describe: false,
                include_layout: false,
                need_tag: false,
                object_describe_api_name: apiName,
                search_query_info: JSON.stringify({
                    limit: 2000,
                    offset: 0,
                    filters: filters,
                }),
            }
        }).then(result => {
            return result && result.dataList || [];
        })
    }
}