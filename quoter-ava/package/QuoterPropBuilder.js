import {isEmpty, uniq, uuid} from "../../pluginbase-ava/package/pluginutils";

export class QuoterPropBuilder {

    constructor(context) {
        let {pluginApi, requestApi} = context || {};
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    async queryAttributeConstraintData(attributeConstraintId, currentVersion = 0, dataGetter) {
        function getResult(attributeData, attributeConstraintList, changed = false, version, refreshCmpt = true) {
            return {attributeData, attributeConstraintList, changed, version, refreshCmpt};
        }

        if (isEmpty(attributeConstraintId)) {
            return getResult(undefined, undefined, false, currentVersion);
        }
        let pageId = dataGetter.getPageId();
        let token = 'quoter_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        let params = {id: attributeConstraintId, version: currentVersion};
        let attributeConstraintResult = await this.requestApi.queryAttributeConstraintById(params).then(rst => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            return rst;
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        });
        let {dataList: attributeConstraintList, changed, version} = attributeConstraintResult || {};
        if (!attributeConstraintList || !attributeConstraintList.length) {
            return getResult(undefined, undefined, changed, version);
        }
        let values = this.findValues(attributeConstraintList);
        let {attributeIds, nonstandardAttributeIds} = values || {};
        let promiseList = [];
        if (attributeIds && attributeIds.length) {
            promiseList.push(this.requestApi.list('AttributeObj', [this.newInFilter('_id', attributeIds)]));
            promiseList.push(this.requestApi.list('AttributeValueObj', [this.newInFilter('attribute_id', attributeIds)]));
        }
        if (nonstandardAttributeIds && nonstandardAttributeIds.length) {
            promiseList.push(this.requestApi.list('NonstandardAttributeObj', [this.newInFilter('_id', nonstandardAttributeIds)]));
        }
        let newToken = 'quoter_' + uuid();
        this.pluginApi.showSingletonLoading(newToken, {}, pageId);
        return Promise.all(promiseList).then(resultList => {
            this.pluginApi.hideSingletonLoading(newToken, pageId);
            let attributeList = resultList && resultList.length && resultList[0];
            let attributeValueList = resultList && resultList.length && resultList[1];
            let nonstandardAttributeList = resultList && resultList.length && resultList[2];
            let attributeData = {};
            attributeList && attributeList.length && attributeList.forEach(attribute => {
                let {_id: attributeId, name, name__r, field_num} = attribute;
                let attribute_values = attributeValueList && attributeValueList.length && attributeValueList.filter(attributeValue => {
                    return attributeValue.attribute_id === attributeId
                }).map(attributeValue => {
                    let {_id, name, name__r} = attributeValue
                    return {_id, name: name__r || name}
                }) || [];
                attributeData[attributeId] = {_id: attributeId, name: name__r || name, field_num, attribute_values}
            });
            nonstandardAttributeList && nonstandardAttributeList.length && nonstandardAttributeList.forEach(nonstandardAttribute => {
                let {_id, name, name__r} = nonstandardAttribute;
                attributeData[_id] = {_id: _id, name: name__r || name}
            });
            return getResult(attributeData, attributeConstraintList, changed, version);
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(newToken, pageId);
            this.pluginApi.showToast(err);
            return getResult(undefined, undefined, changed, version)
        })
    }

    findValues(dataList) {
        let attributeIds = [];
        let attributeValueIds = [];
        let nonstandardAttributeIds = [];
        dataList && dataList.length && dataList.forEach(it => {
            /*
             * isStandardAttribute:true 标准属性 false 非标属性
             * nodeType: 1 属性 2属性值
             * values":["xxxxx"]: 属性或属性值，根据nodeType确定
             * children: 子节点
             */
            let {isStandardAttribute, nodeType, values, children} = it;
            if (values && values.length) {
                if (nodeType === 1) {
                    isStandardAttribute ? (attributeIds.push(...values)) : (nonstandardAttributeIds.push(...values));
                } else if (nodeType === 2) {
                    isStandardAttribute && (attributeValueIds.push(...values))
                }
            }
            let childResult = this.findValues(children);
            let {
                attributeIds: childAttributeIds,
                attributeValueIds: childAttributeValueIds,
                nonstandardAttributeIds: childNonstandardAttributeIds
            } = childResult || {};
            if (childAttributeIds && childAttributeIds.length) {
                attributeIds.push(...childAttributeIds);
            }
            if (childAttributeValueIds && childAttributeValueIds.length) {
                attributeValueIds.push(...childAttributeValueIds);
            }
            if (childNonstandardAttributeIds && childNonstandardAttributeIds.length) {
                nonstandardAttributeIds.push(...childNonstandardAttributeIds);
            }
        })
        return {
            attributeIds: uniq(attributeIds),
            attributeValueIds: uniq(attributeValueIds),
            nonstandardAttributeIds: uniq(nonstandardAttributeIds)
        };
    }

    newInFilter(fieldName, fieldValues) {
        return {
            field_name: fieldName,
            field_values: fieldValues,
            operator: 'IN'
        }
    }
}