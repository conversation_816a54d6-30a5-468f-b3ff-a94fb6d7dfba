export class GenerateDetails {

    constructor(context) {
        let {fieldMapping, bizStateConfig} = context || {};
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
    }

    generateDetails(pluginExecResult, options) {
        let {formApis, selectedAttributeValues, selectedNoStandardAttributeValues, attributeFieldNum, dataUpdater} = options;
        dataUpdater.updateMaster({'quoter_value_changed': false});
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {product_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName);
        let isOpenPriceBook = this.bizStateConfig.isOpenPriceBook();
        let isOpenPriceBookPriority = this.bizStateConfig.isOpenPriceBookPriority();
        let fieldName = isOpenPriceBook ? (isOpenPriceBookPriority ? product_id : price_book_product_id) : product_id;
        formApis && formApis.triggerAddDetail && formApis.triggerAddDetail({
            apiName: objApiName,
            button: {action: 'Batch_Lookup_Add', lookup_field_name: fieldName},
            addOpt: {
                isQuoterGenerateDetails: true,
                selectedAttributeValues,
                selectedNoStandardAttributeValues,
                attributeFieldNum
            }
        });
    }
}