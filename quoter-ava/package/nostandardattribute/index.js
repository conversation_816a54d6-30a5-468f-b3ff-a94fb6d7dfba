import fsapi from "fs-hera-api";
import {i18n, isEmpty} from "../../../pluginbase-ava/package/pluginutils";

Component({

    properties: {
        noStandardAttribute: {
            type: Object,
            value: null
        },

        hidden: {
            type: Boolean,
            value: false,
        }
    },

    observers: {
        noStandardAttribute(noStandardAttribute) {
            let {value, toastText} = noStandardAttribute || {};
            this.setData({
                dValue: value,
                dToastText: toastText
            })
        }
    },

    data: {
        dPlaceholder: undefined,
        dValue: undefined,
        dToastText: undefined
    },

    methods: {
        _inputHandle(e) {
            let {value, cursor} = e.detail;
            let val = value ? fsapi.format.field_formatNumber(value) : value;
            if (this._checkMaxLength(val)) {
                val = this.data.dValue
                cursor = cursor - 1
            }
            this.data.dValue = val;
            return {
                value: val,
                cursor: cursor
            };
        },

        _blurHandle(e) {
            let {noStandardAttribute, dValue} = this.data;
            let value = this._handleValue(dValue);
            this.setData({
                dValue: value
            });
            let constraintId = noStandardAttribute.constraintId;
            this.triggerEvent('attributeValueChanged', {[constraintId]: (isEmpty(value) ? [] : [value])});
        },

        _checkMaxLength(val) {
            if (!val) {
                return false
            }
            val = val.replace(/[^\d.]/g, '');
            let length = 14, decimal_places = 6;
            const _arr = `${val}`.split('.');
            if (_arr[0].length > length) {
                return true
            }
            return !!(_arr[1] && _arr[1].length > decimal_places);
        },

        _handleValue(value) {
            if (typeof (value) == 'string') {
                let valStr = value && value.replace(/^\s+|\s+$/g, '')
                //没有小数位，失焦去掉末尾‘.’
                if (valStr.length && valStr.indexOf('.') == valStr.length - 1) {
                    valStr = valStr.replace('.', '')
                }
                return valStr
            }
            return value
        },
    },

    lifetimes: {
        attached() {
            this.setData({
                dPlaceholder: i18n('ava.object_form.pleaseinput')/*请输入*/
            })
        }
    }
})