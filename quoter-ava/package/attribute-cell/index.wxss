@import "/miniprogram_npm/ava-ui/fxui/common_css/var.wxss";
.i-cell-outer{
    position:relative;
}
.i-cell-outer:not(.noLine):after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 200%;
    height: 200%;
    margin-left:24rpx;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 0 solid #e9eaec;
    border-bottom-width: 1px;
}

.i-cell-outer-error{
    position:relative;
    background: #FFE9E4;
    border-radius: 8rpx;
    margin: 0 12rpx;
}

.i-cell {
    position:relative;
    display:flex;
    align-items:flex-start;
    line-height: 48rpx;
    font-size:32rpx;
    overflow:hidden;
}

.i-cell-label{
    width: 254rpx;
    word-break: break-word;
}
.i-cell-hd{
    order: 0;
    position:relative;
    display:flex;
    flex-direction: row;
    padding-left: 16rpx;
    padding-top: 22rpx;
    padding-bottom: 22rpx;
}

.i-cell-bd {
    position: relative;
    order: 1;
    display:flex;
    flex:1;
    margin-left: 8px;
    margin-right: 8px;
    padding: 22rpx 0;
}

.require-class{
    color: #f27474;
}

.no-require-class{
    color: transparent;
}

.toast_text_error {
    display: flex;
    background-color: #FF5027;
    font-size: 24rpx;
    color: #fff;
    padding: 4rpx 10rpx;
    margin: 8rpx 12rpx;
    border-radius: 8rpx;
}