Component({

    options: {
        multipleSlots: true,
        addGlobalClass: true
    },

    properties: {
        label: {
            type: String,
            value: ''
        },

        is_required: {
            type: Boolean,
            value: false
        },

        hidden: {
            type: Boolean,
            value: false,
        },

        toastText: {
            type: String,
            value: ""
        },

        toastType: {
            type: String,
            value: "error"
        },

        noLine: {
            type: Boolean,
            value: false
        },

        isHiddenICell: {
            type: Boolean,
            value: false
        },

        inputRectStyle: {
            type: String,
            value: ""
        },
    }
})
