<view style="background-color: white;" hidden="{{hidden}}">
    <view class="{{noLine?'i-cell-outer-noline':('i-cell-outer'+(toastText?'-error':''))}}">
        <view class="i-cell" hidden="{{isHiddenICell}}">
            <view class="i-cell-hd" wx:if="{{label}}">
                <view class="text-14-2 {{is_required?'':'no-'}}require-class">*</view>
                <view class="text-14-2 i-cell-label" catchtap="showHelpText">{{label}}</view>
            </view>
            <view class="i-cell-bd" style="{{inputRectStyle}}">
                <slot name="inputRect"></slot>
            </view>
        </view>
    </view>
    <text class="{{'toast_text_'+toastType}}" wx:if="{{toastText}}">{{toastText}}</text>
</view>