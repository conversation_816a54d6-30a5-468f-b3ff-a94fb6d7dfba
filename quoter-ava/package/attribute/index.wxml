<attribute-cell label="{{attribute.name}}" is_required="{{attribute.isRequired}}" toastText="{{dToastText}}" hidden="{{hidden}}">
    <view class="item-container" slot="inputRect">
        <block wx:for="{{attribute.attributeValues}}" wx:key="_id">
            <view class="item-bg {{item.isSelected ? 'item-bg-selected' : 'item-bg-unselected'}}"
                  data-attributevalue="{{item._id}}"
                  bindtap="onAttributeValueClick">
                <text class="item-text {{item.isSelected ? 'item-text-selected' : 'item-text-unselected'}}">{{item.name}}</text>
            </view>
        </block>
    </view>
</attribute-cell>