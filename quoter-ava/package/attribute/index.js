import fsapi from "fs-hera-api";
import {i18n} from "../../../pluginbase-ava/package/pluginutils";

Component({

    properties: {
        attribute: {
            type: Object,
            value: null
        },

        hidden: {
            type: Boolean,
            value: false,
        }
    },

    observers: {
        attribute(attribute) {
            let {toastText} = attribute || {};
            this.setData({
                dToastText: toastText
            })
        }
    },

    data: {
        dToastText: undefined
    },

    methods: {

        onAttributeValueClick(event) {
            let {attributevalue} = event.currentTarget.dataset;
            let {constraintId, isMultiSelected, attributeValues} = this.data.attribute || {};
            let clickAttributeValue = attributeValues && attributeValues.find(it => it._id === attributevalue);
            if (!clickAttributeValue) {
                return;
            }
            let {isSelected, isRequired} = clickAttributeValue;
            if (isRequired) {
                fsapi.util.showToast(i18n('ava.object_form.quoter.must_attribute_change_tip')/*该属性为必选，不可更改*/);
                return;
            }
            let requiredAttributeValue = attributeValues.find(it => it.isRequired);
            if (!isMultiSelected && requiredAttributeValue) {//如果是单选且必选，则不处理
                fsapi.util.showToast(i18n('ava.object_form.quoter.single_must_attribute_change_tip')/*该属性为单选且必选，不可更改*/);
                return;
            }
            if (isMultiSelected) {//多选
                clickAttributeValue.isSelected = !isSelected;
            } else {//单选，选中当前点击的属性值
                attributeValues.forEach(attributeValue => {
                    let isSelected = attributeValue.isSelected;
                    attributeValue.isSelected = isSelected ? false : (attributeValue._id === attributevalue);
                })
            }
            this.setData({
                "attribute.attributeValues": attributeValues,
                dToastText: null
            });
            let selectedAttributeValueIds = attributeValues.filter(it => it.isSelected).map(it => it._id) || [];
            this.triggerEvent('attributeValueChanged', {[constraintId]: selectedAttributeValueIds})
        }
    }
})