import {i18n, isEmpty} from "../../pluginbase-ava/package/pluginutils";
import {getDetailComponent} from './utils/formutils';
import bomPicker from "./bomtree/bomPicker";

export class MDRender {

    constructor(context) {
        let {fieldMapping, pluginApi} = context || {};
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
    }

    mdRenderBefore(pluginExecResult, options) {
        let self = this;
        let preData = pluginExecResult && pluginExecResult.preData;
        let preFilterShowObjectDataList = preData && preData.filterShowObjectDataList || [];
        let handleBarButtons = preData && preData.handleBarButtons || [];
        let {objApiName, formApis} = options;
        let describeLayout = options.dataGetter.getDescribeLayout();
        let component = getDetailComponent(describeLayout, objApiName);
        let renderType = component && component.render_type || 'card';
        let mdChildRenderTypesResult = Object.assign({}, preData.mdChildRenderTypes, renderType === 'table' && {'all': 'table_bom'});
        return Object.assign({}, preData, {
            mdChildRenderTypes: mdChildRenderTypesResult,

            filterShowObjectDataList: [...preFilterShowObjectDataList, (opt) => {
                let {dataList} = opt;
                if (['card', 'compact'].includes(renderType)) {
                    let {parent_prod_pkg_key} = self.fieldMapping.getDetailFields(objApiName)
                    return dataList.filter(it => {
                        let isSubLines = !isEmpty(it[parent_prod_pkg_key]);
                        return !isSubLines
                    });
                }
                return dataList;
            }],

            handleBarButtons: [...handleBarButtons, (opt) => {
                let isExpand = bomPicker.isBomExpand();
                opt = opt || {};
                opt.buttons = opt.buttons || [];
                opt.buttons.unshift({
                    action: "config-bom",
                    icon: "Bomzhankai",//Bomshouqi
                    hideIfNoData: true,
                    label: isExpand ? i18n('ava.object_form.onsale.bom.collapse')/*收起多级*/ : i18n('ava.object_form.onsale.bom.expand')/*展开多级*/,
                    onClick: () => {
                        let fun = function (p) {
                            return new Promise(resolve => {
                                let {dataGetter, dataUpdater,} = p;
                                let detailDataList = dataGetter.getDetail(objApiName) || [];
                                let isExpand = bomPicker.isBomExpand();
                                let result = !isExpand;
                                bomPicker.setBomExpand(result);
                                bomPicker.expandAll(result, detailDataList);
                                dataUpdater.updateDetailByApiName(objApiName, detailDataList);
                                let tip = result ? i18n('ava.object_form.bom.expand_tip'/*展开多级产品结构*/)
                                    : i18n('ava.object_form.bom.collapse_tip'/*收起多级产品结构*/)
                                self.pluginApi.showToast(tip);
                                resolve()
                            })
                        }
                        formApis.npcRun(fun)

                    }
                })
                return opt;
            }]
        })
    }

    mdItemRenderBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData && pluginExecResult.preData;
        let preFooterComs = preData && preData.footerComs || [];
        let {objApiName, dataGetter} = options;
        let describeLayout = dataGetter.getDescribeLayout();
        let component = getDetailComponent(describeLayout, objApiName);
        let renderType = component && component.render_type || 'card';
        let footerComs = {};
        if (['card', 'compact'].includes(renderType)) {
            Object.assign(footerComs, {name: "bom-tree-cmpt"});
        }
        return Object.assign({}, preData, {
            footerComs: [footerComs, ...preFooterComs],
        });
    }
}