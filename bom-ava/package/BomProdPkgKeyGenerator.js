import {each, isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";
import bomUtil from "./bomUtil";

const KEY_SUB_LINES_LIST = "key_sub_lines_list";
const TAG = 'BomProdPkgKeyGenerator';

class BomProdPkgKeyGenerator {

    /**
     * 重新生成虚拟key，根节点也会重新生成
     * @param objectDataList
     * @param detailFieldMapping
     */
    generatorProdPkgKeyWithRoot(objectDataList, detailFieldMapping) {
        if (!objectDataList || !objectDataList.length || isEmpty(detailFieldMapping)) {
            return;
        }
        let {prod_pkg_key, root_prod_pkg_key, parent_prod_pkg_key} = detailFieldMapping;
        objectDataList.forEach(objectData => {
            let isPkg = bomUtil.isProductPackage(objectData);
            if (!isPkg) {//非包不给虚拟key赋值
                return;
            }
            let orgProdPkgKey = objectData[prod_pkg_key];
            let subLinesList = objectDataList.filter(it => {
                return it[root_prod_pkg_key] === orgProdPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key];
            });
            let newProdPkgKey = uuid();
            Object.assign(objectData, {[prod_pkg_key]: newProdPkgKey, [root_prod_pkg_key]: newProdPkgKey})
            subLinesList && subLinesList.length && subLinesList.forEach(subLines => {
                let {[parent_prod_pkg_key]: parentProdPkgKey} = subLines;
                if (parentProdPkgKey === orgProdPkgKey) {
                    subLines[parent_prod_pkg_key] = newProdPkgKey;
                }
                subLines[root_prod_pkg_key] = newProdPkgKey;
            })
        });
        this.generatorProdPkgKey(objectDataList, detailFieldMapping);
    }

    /**
     * 重新生成虚拟key：prod_pkg_key，parent_prod_pkg_key，root_prod_pkg_key
     * 1、母件的虚拟key保持不变，因为母件的虚拟key已经保证了不会重复，而且价格政策也在使用，不要随意改变。
     * 2、子件的prod_pkg_key要重新生成，因为子件的虚拟key可能有重复的（选择相同的bom，相同子产品）。
     * 3、子件的parent_prod_pkg_key要取它母件的prod_pkg_key。
     * 4、子件的root_prod_pkg_key不用重新生成，保持原值就好，因为母件的没有变。
     *
     * @param objectDataList 拍平的数据列表，包含产品包、普通产品、子件
     * @param detailFieldMapping 从对象字段映射
     */
    generatorProdPkgKey(objectDataList, detailFieldMapping) {
        if (!objectDataList || !objectDataList.length || isEmpty(detailFieldMapping)) {
            return;
        }
        //先将root_prod_pkg_key不同的子产品数据分离，root_prod_pkg_key相同的放在一起
        let subLinesMap = {};
        let {parent_prod_pkg_key, root_prod_pkg_key} = detailFieldMapping || {};
        objectDataList.forEach(objectData => {
            let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = objectData || {};
            if (isEmpty(parentProdPkgKey)) {//非子产品不处理
                return;
            }
            let subLinesList = subLinesMap[rootProdPkgKey];
            if (!subLinesList) {
                subLinesList = [];
                subLinesMap[rootProdPkgKey] = subLinesList;
            }
            subLinesList.push(objectData);
        });
        //再将分离的子产品数据逐个处理
        each(subLinesMap, (subLinesList, rootProdPkgKey) => {
            this.buildCascade(rootProdPkgKey, subLinesList, detailFieldMapping);
        })
    }

    /**
     * 将rootProdPkgKey相同的的子件构建级联结构
     *
     * @param rootProdPkgKey 根节点的prod_pkg_key
     * @param subLinesList   子产品数据
     * @param detailFieldMapping 从对象字段映射
     */
    buildCascade(rootProdPkgKey, subLinesList, detailFieldMapping) {
        if (!subLinesList || !subLinesList.length) {
            return;
        }
        let {parent_prod_pkg_key} = detailFieldMapping || {};
        let childSubLinesMap = {};
        let i = subLinesList.length;
        while (i--) {
            let subLines = subLinesList[i];
            let {[parent_prod_pkg_key]: parentProdPkgKey} = subLines || {};
            if (parentProdPkgKey === rootProdPkgKey) {//一级子件不处理
                continue;
            }
            let childSubLinesList = childSubLinesMap[parentProdPkgKey];
            if (!childSubLinesList) {
                childSubLinesList = [];
                childSubLinesMap[parentProdPkgKey] = childSubLinesList;
            }
            childSubLinesList.push(subLines);
            subLinesList.splice(i, 1);
        }
        this.fillChildSubLinesList(subLinesList, childSubLinesMap, detailFieldMapping);//构建级联结构
        let beforeLogObj = {log: ''};
        let beforeLog = this.log(beforeLogObj, subLinesList, 0, detailFieldMapping);
        console.info(`${TAG} 虚拟key重置前：\n ${beforeLog}`);
        this.resetProdPkgKey(subLinesList, rootProdPkgKey, rootProdPkgKey, detailFieldMapping);//通过级联结构重置虚拟key
        let afterLogObj = {log: ''};
        let afterLog = this.log(afterLogObj, subLinesList, 0, detailFieldMapping);
        console.info(`${TAG} 虚拟key重置后：\n ${afterLog}`);
    }

    /**
     * 填充子件下的子件数据
     *
     * @param subLinesList 子件列表
     * @param subLinesMap  子件下的子件数据map
     * @param detailFieldMapping 从对象字段映射
     */
    fillChildSubLinesList(subLinesList, subLinesMap, detailFieldMapping) {
        if (!subLinesList || !subLinesList.length) {
            return;
        }
        let {prod_pkg_key} = detailFieldMapping || {};
        subLinesList.forEach(subLines => {
            let {[prod_pkg_key]: prodPkgKey} = subLines || {};
            let childSubLinesList = subLinesMap[prodPkgKey];
            if (childSubLinesList && childSubLinesList.length) {
                subLines[KEY_SUB_LINES_LIST] = childSubLinesList;
                this.fillChildSubLinesList(childSubLinesList, subLinesMap, detailFieldMapping);
            } else {
                delete subLines[KEY_SUB_LINES_LIST];
            }
        })
    }

    /**
     * 重置子产品虚拟key
     *
     * @param subLinesList     子产品数据
     * @param parentProdPkgKey 父节点的prod_pkg_key
     * @param rootProdPkgKey   根节点prod_pkg_key
     * @param detailFieldMapping 从对象字段映射
     */
    resetProdPkgKey(subLinesList, parentProdPkgKey, rootProdPkgKey, detailFieldMapping) {
        if (!subLinesList || !subLinesList.length) {
            return;
        }
        let {prod_pkg_key, parent_prod_pkg_key} = detailFieldMapping || {};
        subLinesList.forEach(subLines => {
            let prodPkgKey = uuid();
            subLines[prod_pkg_key] = prodPkgKey;
            let {[parent_prod_pkg_key]: _parentProdPkgKey} = subLines || {};
            if (_parentProdPkgKey !== rootProdPkgKey) {//一级子件的parent_prod_pkg_key不用变，因为母件的没有变
                //更新parent_prod_pkg_key
                subLines[parent_prod_pkg_key] = parentProdPkgKey;
            }
            let childList = subLines[KEY_SUB_LINES_LIST];
            this.resetProdPkgKey(childList, prodPkgKey, rootProdPkgKey, detailFieldMapping);//递归更新子件的虚拟key
        })
    }

    log(beforeLogObj, subLinesList, level, detailFieldMapping) {
        if (!subLinesList || !subLinesList.length) {
            return beforeLogObj.log;
        }
        let {product_id, prod_pkg_key, parent_prod_pkg_key} = detailFieldMapping || {};
        level++;
        subLinesList.forEach(subLines => {
            let {[product_id]: productId, [`${product_id}__r`]: _productName, [prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey} = subLines || {};
            let prefix = '';
            let tempLevel = level;
            while (tempLevel > 0) {
                prefix += '-----';
                tempLevel--;
            }
            let productName = (_productName || productId);
            let info = prefix + productName + ' prod_pkg_key: ' + prodPkgKey + ' parent_prod_pkg_key: ' + parentProdPkgKey + '\n';
            beforeLogObj.log += info;
            let childList = subLines[KEY_SUB_LINES_LIST];
            this.log(beforeLogObj, childList, level, detailFieldMapping);
        });
        return beforeLogObj.log;
    }
}

export default new BomProdPkgKeyGenerator();