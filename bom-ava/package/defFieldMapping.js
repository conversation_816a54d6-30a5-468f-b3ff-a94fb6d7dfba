import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
//默认提供的字段映射
const defFieldMapping = Object.freeze({
    masterFields: {
        form_account_id: 'form_account_id',// 客户id
        form_partner_id: 'form_partner_id',// 合作伙伴id
        form_price_book_id: 'form_price_book_id',// 价目表id
        mc_currency: 'mc_currency'
    },
    detailFields: {
        quantity: 'quantity',// 数量
        product_id: 'product_id',// 产品名称
        price_book_product_id: 'price_book_product_id', // 价目表产品
        price_book_id: 'price_book_id',// 价目表id
        bom_id: "bom_id",
        prod_pkg_key: "prod_pkg_key",
        parent_prod_pkg_key: "parent_prod_pkg_key",
        root_prod_pkg_key: "root_prod_pkg_key",
        is_package: "is_package",
        product_group_id: "product_group_id",
        product_price: 'product_price',// 价格
        node_discount: "node_discount",
        node_subtotal: 'node_subtotal',
        node_price: "node_price",
        node_type: "node_type",
        share_rate: "share_rate",
        price_editable: "price_editable",
        amount_editable: "amount_editable",


        discount: 'discount',// 折扣
        sales_price: 'sales_price',// 销售单价
        selling_price: 'selling_price',// 报价
        subtotal: 'subtotal',// 小计
        price_book_price: 'price_book_price',// 价目表价格
        price_book_discount: 'price_book_discount',// 价目表折扣
        unit: 'unit',
        actual_unit: 'actual_unit',
        price_mode: "price_mode",
        extra_discount: 'extra_discount',
        attribute_price_book_id: 'attribute_price_book_id',
        product_life_status: 'product_life_status',
        product_status: "product_status",
        is_saleable: 'is_saleable',
        rebate_coupon_id: 'rebate_coupon_id',
        parent_gift_key: 'parent_gift_key',
        temp_node_group_id: 'temp_node_group_id',
        mc_currency: 'mc_currency',
        mc_exchange_rate: 'mc_exchange_rate',
        system_discount_amount: "system_discount_amount",
        total_discount_amount: 'total_discount_amount',
        node_no: 'node_no',
        dynamic_amount: "dynamic_amount",

        bom_core_id: 'bom_core_id',
        related_core_id: 'related_core_id',
        bom_type: 'bom_type',
        bom_version: 'bom_version',
        new_bom_path: 'new_bom_path',
        amount_any: 'amount_any',
        increment: 'increment'
    }
});

var fieldMapping;
var detailObjApiName;

/**
 * 更新fieldMapping
 * @param {FieldMapping} _fieldMapping FieldMapping对象的实例
 */
function updateFieldMapping(_fieldMapping) {
    fieldMapping = _fieldMapping;
    detailObjApiName = fieldMapping && fieldMapping.getFirstDetailObjApiName();
}

/**
 * 获取fieldMapping
 * @returns {FieldMapping}
 */
function getFieldMapping() {
    if (!fieldMapping) {
        fieldMapping = new FieldMapping({}, defFieldMapping);
        detailObjApiName = fieldMapping.getFirstDetailObjApiName();
    }
    return fieldMapping;
}

function getMasterFields() {
    return getFieldMapping().getMasterFields();
}

function getDetailFields() {
    return getFieldMapping().getDetailFields(detailObjApiName);
}

export default {
    defFieldMapping,
    updateFieldMapping,
    getMasterFields,
    getDetailFields
}