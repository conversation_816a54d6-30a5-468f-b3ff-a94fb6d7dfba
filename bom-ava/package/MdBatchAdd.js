import bomUtil from "./bomUtil";
import {cloneDeep, formatDataDecimalPlaces, isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";
import {emitEvent} from "./events";
import bomPicker from "./bomtree/bomPicker";
import bomPriceQuerier from './BomPriceQuerier';
import {getDetailComponent, getReadonlyFields} from './utils/formutils';

export class MdBatchAdd {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi} = context || {};
        this.context = context;
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
    }

    async mdBatchAddBefore(pluginExecResult, options) {
        let {dataGetter, objApiName, selectObjectParams, recordType, lookupField} = options;
        let fieldName = lookupField && lookupField.api_name;
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {product_id, price_book_product_id} = detailFieldMapping || {};
        if (!([product_id, price_book_product_id].includes(fieldName))) {
            return;
        }
        let {form_account_id, form_price_book_id, form_partner_id} = this.fieldMapping.getMasterFields();
        let masterData = dataGetter.getMasterData && dataGetter.getMasterData();
        let {
            [form_account_id]: accountId, [form_price_book_id]: priceBookId, [`${form_price_book_id}`]: priceBookName, [form_partner_id]: partner_id,
        } = masterData;
        let masterObjectData = Object.assign({}, selectObjectParams.masterObjectData, {
            account_id: accountId,
            partner_id: partner_id,
            price_book_id: priceBookId,
            price_book_id__r: priceBookName
        });
        let openPriceBookPriority = this.bizStateConfig.isOpenPriceBookPriority();
        let openPriceBook = this.bizStateConfig.isOpenPriceBook();
        let isOpenCPQ = this.bizStateConfig.isOpenCpq();
        let isOpenSimpleCPQ = this.bizStateConfig.isOpenSimpleCpq();
        let isNotShowBom = this.bizStateConfig.isNotShowBom();
        let isCpqEnabledLatestVersion = this.bizStateConfig.isCpqEnabledLatestVersion();
        let {product_price} = this.fieldMapping.getDetailFields(objApiName);
        let layoutFields = dataGetter.getLayoutFields(objApiName, recordType)
        let priceField = layoutFields && layoutFields[product_price];
        let formObjectData = Object.assign({}, selectObjectParams.formObjectData, {
            pricebook_id: priceBookId,
            selectSKUConfig: Object.assign({}, selectObjectParams.formObjectData && selectObjectParams.formObjectData.selectSKUConfig, {
                enforcePriority: openPriceBookPriority,//是否强制执行价目表优先级
                isOpenPriceBook: openPriceBook,//是否开启了价目表
                selectObjectType: 'sku',
                isOpenCPQ,//是否开启了CPQ
                isOpenSimpleCPQ,//是否开启了固定组合
                enableConfigBom: true,
                isShowPackagePrice: !isEmpty(priceField),
                notShowBom: isNotShowBom,
                isCpqEnabledLatestVersion: isCpqEnabledLatestVersion, // 是否开启产品组合默认最新版本
            })
        });
        Object.assign(selectObjectParams, {
            masterObjectData,
            formObjectData,
            disableAdd: true,
            includeAssociated: true,
            useWx: true,
        });
    }

    async mdBatchAddAfter(pluginExecResult, options) {
        let {lookupDatas, lookupField, newDatas, objApiName, dataGetter, recordType, formApis} = options || {};
        let fieldName = lookupField && lookupField.api_name;
        let {
            product_id, price_book_product_id, bom_id, quantity, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, is_package,
            product_price, product_group_id, bom_core_id, related_core_id, new_bom_path
        } = this.fieldMapping.getDetailFields(objApiName);
        if (![product_id, price_book_product_id].includes(fieldName)) {
            return;
        }
        if (!newDatas || !lookupDatas) {
            return;
        }
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let isOpenBomDeleteRoot = this.bizStateConfig.isOpenBomDeleteRoot();
        let addNewSubLinesList = [];
        let isSelectPriceBookProduct = fieldName === price_book_product_id;
        for (let i = 0; i < newDatas.length; i++) {
            let lookupData = lookupDatas[i];
            let newData = newDatas[i];
            let {
                bom_id: bomId, bom_id__r, [bomUtil.BomConst.subProductSelectedInProduct]: subProducts, _selected_num, adjust_total_price,
                object_describe_api_name, price, pricebook_sellingprice, bomCoreData,
                product_id: pkgProductId, product_id__r: pkgProductName
            } = lookupData || {};
            let isProductPkg = bomUtil.isProductPackage(lookupData);
            let rootProdPkg = uuid();
            let targetPrice;
            if (isProductPkg) {//产品包取配置的价格
                targetPrice = adjust_total_price;
            } else {//非产品包取产品、价目表产品本身的价格
                let productPrice = newData[product_price];
                if (isEmpty(productPrice)) {
                    targetPrice = (object_describe_api_name === 'ProductObj') ? price : pricebook_sellingprice;
                } else {
                    targetPrice = productPrice;
                }
            }
            let {_id, name} = bomCoreData || {};
            let updateData = Object.assign({
                [bom_id]: bomId,
                [`${bom_id}__r`]: bom_id__r,
                [quantity]: _selected_num,
                [prod_pkg_key]: rootProdPkg,
                [root_prod_pkg_key]: rootProdPkg,
                [`${is_package}__v`]: isProductPkg,
                [product_price]: targetPrice,
                [bom_core_id]: _id,
                [`${bom_core_id}__r`]: name,
                [related_core_id]: null,
                [new_bom_path]: bomId,
            }, isSelectPriceBookProduct && {//选价目表明细，回填产品名称
                [product_id]: pkgProductId,
                [`${product_id}__r`]: pkgProductName
            })
            if (isProductPkg) {
                let backFills = this.parseBomData2DetailData(objApiName, lookupData);
                if (!isEmpty(backFills)) {
                    Object.assign(updateData, backFills);
                }
            }
            formatDataDecimalPlaces(updateData, objectDescribe);
            Object.assign(newData, updateData);
            if (subProducts && subProducts.length) {
                const detailDefaultData = dataGetter.getDetailDefaultData(objApiName, recordType)
                for (let j = 0; j < subProducts.length; j++) {
                    let subProduct = subProducts[j];
                    let subLinesData = bomUtil.subProduct2SubLines(newData, subProduct, {
                        objApiName,
                        getDescribe: dataGetter.getDescribe.bind(this),
                        parseBomData2DetailData: this.parseBomData2DetailData.bind(this)
                    });
                    Object.assign(subLinesData, subProduct.multiUnitResult);
                    if (!isEmpty(subLinesData)) {
                        if (isOpenBomDeleteRoot) {
                            delete subLinesData[prod_pkg_key];
                            delete subLinesData[parent_prod_pkg_key];
                            delete subLinesData[root_prod_pkg_key];
                            //添加root_product_id，供UI事件使用
                            subLinesData.root_product_id = pkgProductId;
                            subLinesData.root_product_id__r = pkgProductName;
                        }
                        addNewSubLinesList.push(Object.assign({}, detailDefaultData, subLinesData));
                    }
                }
            }
            let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);

            await bomUtil.handleQuantityAmount(newData, addNewSubLinesList, options, detailFieldMapping, this.pluginApi) || [];

            if (!isProductPkg) {//非包不给虚拟key赋值
                [prod_pkg_key, root_prod_pkg_key].forEach(it => {
                    delete newData[it]
                })
            }
        }
        if (isOpenBomDeleteRoot) {//过滤母件数据
            let length = newDatas.length;
            while (length--) {
                let newData = newDatas[length];
                let isProductPkg = bomUtil.isProductPackage(newData);
                if (isProductPkg) {
                    newDatas.splice(length, 1);
                }
            }
        }
        if (addNewSubLinesList && addNewSubLinesList.length) {
            newDatas.push(...addNewSubLinesList);
        }
        return this.getNoCalFieldsBatchAdd(pluginExecResult, options);
    }

    async mdBatchAddEnd(pluginExecResult, options) {
        let {lookupField, newDatas, objApiName} = options || {};
        let fieldName = lookupField && lookupField.api_name;
        let {product_id, price_book_product_id} = this.fieldMapping.getDetailFields(objApiName);
        if (![product_id, price_book_product_id].includes(fieldName)) {
            return;
        }
        if (!newDatas || !newDatas.length) {
            return;
        }
        let isOpenBomDeleteRoot = this.bizStateConfig.isOpenBomDeleteRoot();
        if (isOpenBomDeleteRoot) {
            await this.calcDiscount(options);
        } else {
            this.setFieldsReadonly(newDatas, options);
            await bomPriceQuerier.addNewDataTrigger(newDatas, this.context, options);
        }
    }

    mdDelAfter(pluginExecResult, options) {
        let {dataGetter, objApiName, delDatas} = options;
        let describeLayout = dataGetter.getDescribeLayout();
        let component = getDetailComponent(describeLayout, objApiName);
        let renderType = component && component.render_type || 'card';
        if (['card', 'compact'].includes(renderType)) {//卡片模式、紧凑模式处理子产品数据
            if (delDatas && delDatas.length) {
                let deleteSubLinesList = [];
                let detailDataList = dataGetter.getDetail(objApiName) || [];
                delDatas.forEach(delData => {
                    let isPkg = bomUtil.isProductPackage(delData);
                    if (!isPkg) {
                        return;
                    }
                    let subLinesList = this.findChild(delData, detailDataList);
                    if (subLinesList && subLinesList.length) {
                        bomPicker.expandAll(false, [delData, ...subLinesList]);
                        deleteSubLinesList.push(...subLinesList)
                    }
                });
                if (deleteSubLinesList && deleteSubLinesList.length) {
                    delDatas.push(...deleteSubLinesList);
                }
            }
        }
    }

    mdCloneAfter(pluginExecResult, options) {
        function removeFieldData(d, key) {
            ["", "__r", "__o", "__p", "__s", "__l", "__v"].forEach(it => {
                delete d[key + it]
            })
        }

        let {dataGetter, copyDataIndexs, formApis, newDatas, objApiName} = options;
        if (copyDataIndexs && copyDataIndexs.length) {
            let describeLayout = dataGetter.getDescribeLayout();
            let component = getDetailComponent(describeLayout, objApiName);
            let renderType = component && component.render_type || 'card';
            if (['card', 'compact'].includes(renderType)) {//卡片模式、紧凑模式处理子产品数据
                let copySubLinesList = [];
                let detailDataList = dataGetter.getDetail(objApiName) || [];
                let filterFields = ["dataIndex", "_id", "version", "create_time", "created_by", "last_modified_by", "last_modified_time", "avatemp_showRequired"];
                let {prod_pkg_key, root_prod_pkg_key, parent_prod_pkg_key} = this.fieldMapping.getDetailFields(objApiName);
                copyDataIndexs.forEach((copyDataIndex, index) => {
                    let sourceCopyData = detailDataList && detailDataList.find(it => it.dataIndex === copyDataIndex);
                    let isPkg = bomUtil.isProductPackage(sourceCopyData);
                    if (!isPkg) {//非包不给虚拟key赋值
                        return;
                    }
                    let newProdPkgKey = uuid();
                    let orgProdPkgKey = sourceCopyData[prod_pkg_key];
                    let newData = newDatas[index];
                    newData[prod_pkg_key] = newProdPkgKey;
                    newData[root_prod_pkg_key] = newProdPkgKey;
                    let subLinesList = detailDataList.filter(it => {
                        return it[root_prod_pkg_key] === orgProdPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key];
                    });
                    //复制子产品数据
                    subLinesList && subLinesList.length && subLinesList.forEach(subLines => {
                        let copySubLines = cloneDeep(subLines);
                        filterFields.forEach(filterField => {
                            removeFieldData(copySubLines, filterField);
                        })
                        let {[parent_prod_pkg_key]: parentProdPkgKey} = copySubLines;
                        if (orgProdPkgKey === parentProdPkgKey) {
                            copySubLines[parent_prod_pkg_key] = newProdPkgKey;
                        }
                        copySubLines[root_prod_pkg_key] = newProdPkgKey;
                        copySubLines.dataIndex = formApis.createNewDataIndex();
                        copySubLinesList.push(copySubLines);
                    })
                });
                if (copySubLinesList && copySubLinesList.length) {
                    newDatas.push(...copySubLinesList);
                }
            }
            return this.getNoCalFieldsBatchAdd(pluginExecResult, options);
        }
    }

    async mdCloneEnd(pluginExecResult, options) {
        let {newDatas} = options;
        this.setFieldsReadonly(newDatas, options);
        await bomPriceQuerier.addNewDataTrigger(newDatas, this.context, options);
    }

    setFieldsReadonly(objectDataList, options) {
        let self = this;
        let {dataGetter, dataUpdater, objApiName} = options || {};
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let isOpenCpq = this.bizStateConfig.isOpenCpq();
        objectDataList && objectDataList.length && objectDataList.forEach(objectData => {
            let readonlyFields = getReadonlyFields(objApiName, objectData, objectDescribe, this.fieldMapping, isOpenCpq, (readonlyFields) => {
                self.pluginApi.runPluginSync(emitEvent.bom_updateReadonlyFields_before_sync, Object.assign({}, options, {
                    objectData,
                    readonlyFields
                }));
            }) || [];
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex: objectData.dataIndex,
                fieldName: readonlyFields,
                status: true,
                biz: 'bom',
                priority: 11
            });
        });
    }

    parseBomData2DetailData(objApiName, subProduct) {
        return this.pluginApi.runPluginSync(emitEvent.bom_parseBomData2DetailData_sync, {
            objApiName,
            subProduct: Object.assign({}, subProduct, {
                key_select_sku_attribute_value: subProduct.selectedAttributeValues,
                key_select_sku_nonstandard_attribute_value: subProduct.nonstandardAttribute
            }),
        });
    }

    async calcDiscount(options) {
        let {newDatas, objApiName, dataGetter, dataUpdater, formApis} = options || {};
        let {discount} = this.fieldMapping.getDetailFields(objApiName);
        let modifiedDataIndexList = [];
        let detailDataList = dataGetter.getDetail(objApiName);
        let newDataIndexs = newDatas && newDatas.length && newDatas.map(it => it.dataIndex);
        detailDataList && detailDataList.length && newDataIndexs && newDataIndexs.length && newDataIndexs.forEach(dataIndex => {
            let objectData = detailDataList.find(it => it.dataIndex === dataIndex);
            if (objectData) {
                let {[discount]: discountValue, dataIndex} = objectData;
                if (isEmpty(discountValue) || (parseFloat(discountValue) == 0)) {
                    dataUpdater.updateDetail(objApiName, dataIndex, {[discount]: '100'});
                    modifiedDataIndexList.push(dataIndex);
                }
            }
        });
        if (modifiedDataIndexList && modifiedDataIndexList.length) {
            await formApis.triggerCalAndUIEvent({
                objApiName: objApiName,
                modifiedDataIndexs: modifiedDataIndexList,
                changeFields: [discount]
            });
        }
    }

    getNoCalFieldsBatchAdd(pluginExecResult, options) {
        let {objApiName} = options || {};
        let {quantity, product_price, product_group_id, amount_any} = this.fieldMapping.getDetailFields(objApiName);
        let preData = pluginExecResult && pluginExecResult.preData;
        let preObjectFilterFields = preData
            && preData.extraCalUiParams
            && preData.extraCalUiParams.filterFields
            && preData.extraCalUiParams.filterFields[objApiName];
        let noCalFields = [quantity, product_price, product_group_id, amount_any];
        return Object.assign({}, preData, {
            extraCalUiParams: Object.assign({}, preData && preData.extraCalUiParams, {
                filterFields: Object.assign({}, preData && preData.extraCalUiParams && preData.extraCalUiParams.filterFields,
                    {[objApiName]: [...(preObjectFilterFields || []), ...noCalFields]}),
            })
        })
    }

    findChild(objectData, detailDataList) {
        if (!objectData || !detailDataList || !detailDataList.length) {
            return;
        }
        let {object_describe_api_name: objApiName} = objectData;
        let {root_prod_pkg_key, prod_pkg_key, parent_prod_pkg_key} = this.fieldMapping.getDetailFields(objApiName);
        let {[prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey} = objectData;
        let isPkg = bomUtil.isProductPackage(objectData);
        let isSubLines = !isEmpty(parentProdPkgKey)
        if (!isPkg && !isSubLines) {
            return;
        }
        let childList = [];
        if (isPkg) {//包
            childList = detailDataList.filter(it => {
                return it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key];
            });
        } else {//子，需要递归去找子
            let subDataList = detailDataList.filter(it => {
                return it[parent_prod_pkg_key] === prodPkgKey;
            });
            if (subDataList && subDataList.length) {
                childList.push(...subDataList);
                subDataList.forEach(it => {
                    let result = this.findChild(it, detailDataList);
                    if (result && result.length) {
                        childList.push(...result);
                    }
                })
            }
        }
        return childList;
    }
}