import {each, isEmpty} from "../../../pluginbase-ava/package/pluginutils";

class FieldChangedRecorder {

    constructor() {
        this.changedFieldMap = {};
    }

    record(objectApiName, dataIndex, changedFields) {
        if (isEmpty(objectApiName) || isEmpty(dataIndex) || !changedFields) {
            return;
        }
        if (!this.changedFieldMap) {
            this.changedFieldMap = {};
        }
        let changedFieldMap = this.changedFieldMap;
        let objChangedFieldMap = changedFieldMap[objectApiName];
        if (!objChangedFieldMap) {
            objChangedFieldMap = {};
            changedFieldMap[objectApiName] = objChangedFieldMap;
        }
        let currentChangedFields = objChangedFieldMap[dataIndex];
        if (!currentChangedFields) {
            currentChangedFields = [];
            objChangedFieldMap[dataIndex] = currentChangedFields;
        }
        changedFields.forEach(it => {
            if (!currentChangedFields.includes(it)) {
                currentChangedFields.push(it);
            }
        });
    }

    recordByCalcResult(masterApiName, detailApiName, calcResult, recordDataIndexList) {
        if (isEmpty(calcResult)) {
            return;
        }
        each(calcResult, (calcDataList, objectApiName) => {
            if (objectApiName === masterApiName) {
                let masterModifyData = calcDataList[0];
                if (masterModifyData) {
                    let fields = Object.keys(masterModifyData);
                    this.record(objectApiName, '0', fields);
                }
            } else if (objectApiName === detailApiName) {
                each(calcDataList, (calcData, dataIndex) => {
                    let fields = Object.keys(calcData);
                    let needRecord = true;
                    if (recordDataIndexList && recordDataIndexList.length) {
                        needRecord = recordDataIndexList.includes(dataIndex);
                    }
                    needRecord && (this.record(objectApiName, dataIndex, fields));
                })
            }
        });
    }

    getChangedFieldMap() {
        return Object.assign({}, this.changedFieldMap);
    }

    clear() {
        this.changedFieldMap = {};
    }
}

export default new FieldChangedRecorder();