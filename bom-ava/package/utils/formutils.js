import bomUtil from "../bomUtil";
import defFieldMapping from '../defFieldMapping';
import {divide, i18n, isEmpty, uuid} from "../../../pluginbase-ava/package/pluginutils";
import log from "../../../pluginbase-ava/package/log";

const bomDescribe = {};//bom对象描述

export function updateBomDescribe(describe) {
    Object.assign(bomDescribe, describe);
}

export function getBomFieldDescribe(fieldName) {
    return bomDescribe && bomDescribe.fields && bomDescribe.fields[fieldName];
}

export const timeAnalysis = {
    PREFIX: 'select_bom_',
    start(tag = '') {
        console.time(this.PREFIX + tag)
    },

    end(tag = '') {
        console.timeEnd(this.PREFIX + tag);
    }
}

export function getReadonlyFields(objApiName, objectData, objectDescribe, fieldMapping, isOpenCPQ = true, preHandle) {
    let {
        quantity, product_price, product_id, price_book_product_id, price_book_id, node_discount, node_price, price_book_price,
        parent_prod_pkg_key, price_editable, amount_editable, node_type,
    } = fieldMapping.getDetailFields(objApiName);
    let {
        [parent_prod_pkg_key]: parentProdPkgKey, [`${price_editable}__v`]: priceEditableValue,
        [`${amount_editable}__v`]: amountEditableValue, [node_type]: nodeType
    } = objectData || {};
    let readonlyFields = [product_id, price_book_product_id, price_book_id];
    let isProductPkg = bomUtil.isProductPackage(objectData);
    if (isProductPkg) {//产品包的价格、选配折扣字段设置为只读
        readonlyFields.push(product_price, node_discount, node_price, price_book_price)
    } else if (parentProdPkgKey !== undefined && parentProdPkgKey !== null) {//子产品的非自定义字段设置为只读
        let fields = objectDescribe && objectDescribe.fields || {};
        let editableFields = [];//子产品支持编辑的字段
        if (isOpenCPQ) {
            editableFields.push(node_discount);//选配折扣
        }
        let priceEditable;
        let amountEditable;
        if (nodeType === 'temp') {//临时子产品
            let priceEditableDesc = getBomFieldDescribe(price_editable);
            let amountEditableDesc = getBomFieldDescribe(amount_editable);
            priceEditable = priceEditableDesc && priceEditableDesc.default_value;
            amountEditable = amountEditableDesc && amountEditableDesc.default_value;
        } else {//普通子产品
            priceEditable = priceEditableValue;
            amountEditable = amountEditableValue;
        }
        if (isOpenCPQ && priceEditable) {
            editableFields.push(product_price)//价格支持编辑
        }
        if (isOpenCPQ && amountEditable) {
            editableFields.push(quantity)//数量支持编辑
        }
        Object.keys(fields).forEach(fieldApiName => {
            let {api_name, define_type} = fields[fieldApiName] || {};
            if (define_type !== 'custom' && !editableFields.includes(api_name)) {
                readonlyFields.push(fieldApiName);
            }
        });
    } else {//普通明细的选配折扣、标准选配价格设置为只读
        readonlyFields.push(node_discount, node_price);
    }
    preHandle && preHandle(readonlyFields);
    return readonlyFields;
}

/**
 * 更新子产品数据
 */
export function updateSubLinesData(objApiName, dataGetter, dataUpdater) {
    let detailDataList = dataGetter && dataGetter.getDetail && dataGetter.getDetail(objApiName);
    if (!detailDataList || !detailDataList.length) {
        return;
    }
    let pkgDataList = detailDataList.filter(it => bomUtil.isProductPackage(it));
    if (!pkgDataList || !pkgDataList.length) {
        return;
    }
    let {prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
    pkgDataList.forEach(pkgData => {
        let {_id, object_describe_api_name, dataIndex} = pkgData;
        if (isEmpty(pkgData[prod_pkg_key])) {//初始化时，产品包虚拟key为空进行赋值及上报
            let prodPkgKey = uuid();
            let updateData = {[prod_pkg_key]: prodPkgKey, [root_prod_pkg_key]: prodPkgKey};
            Object.assign(pkgData, updateData);
            dataUpdater.updateDetail(objApiName, dataIndex, updateData);
            let content = JSON.stringify({_id, object_describe_api_name, error_info: 'prod_pkg_key is empty'});
            console.log("sfa", 'generatorProdPkgKeyWhenInit', content);
            log.kLog("sfa", 'generatorProdPkgKeyWhenInit', content);
        }
        let subLinesList = detailDataList && detailDataList.filter(it => {
            return it[root_prod_pkg_key] === pkgData[prod_pkg_key] && it[root_prod_pkg_key] !== it[prod_pkg_key];
        });
        subLinesList && subLinesList.length && subLinesList.forEach(subLines => {
            let updateData = handleSubProducts(pkgData, subLines, detailDataList);
            if (!isEmpty(updateData)) {
                dataUpdater.updateDetail(objApiName, subLines.dataIndex, updateData)
            }
        })
    });
}

export function handleSubProducts(pkgData, subProduct, subProductList) {
    if (isEmpty(pkgData) || isEmpty(subProduct)) {
        return;
    }
    let {quantity, price_book_discount, discount, sales_price, subtotal, share_rate, node_type, product_group_id, temp_node_group_id, amount_any} = defFieldMapping.getDetailFields();
    let {[quantity]: pkgQuantity, [price_book_discount]: priceBookDiscount} = pkgData;
    let {
        [quantity]: subProductQuantity, [share_rate]: shareRate, [node_type]: nodeType,
        [product_group_id]: productGroupId, [`${product_group_id}__v`]: productGroupName,
        [temp_node_group_id]: tempNodeGroupId, [`${temp_node_group_id}__r`]: tempNodeGroupName, [`${amount_any}__v`]: amountAny
    } = subProduct;
    let amountOriginSubBom = subProduct[bomUtil.BomConst.amountOriginSubBom];
    let amountAnyDesc = getBomFieldDescribe(amount_any);
    amountAny = !isEmpty(amountAny) ? amountAny : (amountAnyDesc && amountAnyDesc.default_value);
    const recentSubBom = bomUtil.getRecentSubBomData(subProduct, subProductList) || pkgData;
    return Object.assign({
        [price_book_discount]: priceBookDiscount
    }, (isEmpty(shareRate) || shareRate == '0') && {//子件的分摊比例为空或为0，将子件折扣、销售单价、小计设为0
        [discount]: '0', [sales_price]: '0', [subtotal]: '0'
    }, (nodeType === 'temp' && (!productGroupId || !productGroupName)) && {//如果是临时子件，为分组信息赋值
        [product_group_id]: tempNodeGroupName, [`${product_group_id}__v`]: tempNodeGroupId
    }, nodeType === 'temp' && amountAnyDesc && { // 临时子件的数量是否任意值，赋默认值
        [amount_any]: String(amountAnyDesc.default_value) === 'true' ? i18n("ava.object_form.crm.yes") : i18n("ava.object_form.crm.no"),
        [`${amount_any}__v`]: amountAnyDesc.default_value
    }, (isEmpty(amountOriginSubBom)) && {
        [bomUtil.BomConst.amountOriginSubBom]: recentSubBom[quantity] == 0 ? 0 : (amountAny ? subProductQuantity : divide(subProductQuantity, recentSubBom[quantity]))
    },)

}

export function getDetailComponent(describeLayoutResult, objectApiName) {
    let components = describeLayoutResult && describeLayoutResult.layout && describeLayoutResult.layout.components;
    return components && components.length && components.find(it => it.ref_object_api_name === objectApiName);
}