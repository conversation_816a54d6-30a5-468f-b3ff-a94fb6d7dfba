import bomUtil from "./bomUtil";
import {divide, getEa, i18n, isEmpty, multiply, is_multiple} from "../../pluginbase-ava/package/pluginutils";
import bomPriceQuerier from './BomPriceQuerier'
import fsBomHook from "./hook/fsBomHook";

export class DetailFieldEdit {

    constructor(context) {
        let {fieldMapping, pluginApi} = context;
        this.context = context;
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
    }

    fieldEditAfter(pluginExecResult, options) {
        let {objApiName, fieldName} = options;
        let {quantity} = this.fieldMapping.getDetailFields(objApiName);
        if (fieldName === quantity) {
            return this.quantityEditAfter(pluginExecResult, options);
        }
    }

    async fieldEditEnd(pluginExecResult, options) {
        let {fieldName, changeData, dataIndex} = options;
        if (isEmpty(changeData)) {
            return;
        }
        await bom<PERSON><PERSON><PERSON><PERSON><PERSON>.changeFieldTrigger(fieldName, dataIndex, this.context, options);
        let ea = getEa();
        if (['fs', '794608_sandbox'].includes(ea)) {
            return fsBomHook.hookQuantityChanged(fieldName, dataIndex, this.context, options);
        }
    }

    quantityEditAfter(pluginExecResult, options) {
        let {objApiName, changeData, dataGetter, dataIndex} = options;
        let { quantity, amount_any, max_amount, product_id, min_amount, parent_prod_pkg_key, increment } = this.fieldMapping.getDetailFields(objApiName);
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let quantityFieldDesc = objectDescribe && objectDescribe.fields && objectDescribe.fields[quantity];
        let quantityDecimalPlaces = quantityFieldDesc && quantityFieldDesc.decimal_places || 0;
        let detailDataList = dataGetter.getDetail(objApiName);
        let objectData = dataGetter.getData(objApiName, dataIndex);
        if (!objectData) {
            return;
        }
        let {[parent_prod_pkg_key]: parentProdKey} = objectData;
        let isOverFlow = false; // 
        let changedValue = changeData[quantity];
        if (!isEmpty(parentProdKey)) {//改子的数量
            const recentSubBom = bomUtil.getRecentSubBomData(objectData, detailDataList)
            const parentProductName = recentSubBom[`${product_id}__r`];
            const productName = objectData[`${product_id}__r`];
            let newAmountOrigin = changedValue; // 任意值，取当前值
            if(!objectData[`${amount_any}__v`]){ // 非任意值，取单个复用bom数量
                newAmountOrigin = divide(changedValue, recentSubBom[quantity]); 
            }
            let toastString;
            newAmountOrigin = parseFloat(newAmountOrigin);
            if (bomUtil.isDecimalPlacesInvalid(newAmountOrigin, quantityDecimalPlaces)) {
                isOverFlow = true;
                toastString = i18n('ava.object_form.cpq.amount_origin_subBom_decimal_invalid', [newAmountOrigin, productName]);/*'单包产品数量为{0}，【 {1} 】产品的数量小数位越界，请重新修改'*/
            } else if (objectData[max_amount] && (newAmountOrigin > objectData[max_amount])) {
                isOverFlow = true;
                toastString = i18n('ava.object_form.cpq.amount_origin_subBom_maxAmount_invalid', [parentProductName, productName, objectData[max_amount]]);/*'一个【{0}】内子件【{1}】最大数量为{2}，请重新修改'*/
            } else if (objectData[min_amount] && (newAmountOrigin < objectData[min_amount])){
                isOverFlow = true;
                toastString = i18n('ava.object_form.cpq.amount_origin_subBom_minAmount_invalid', [parentProductName, productName, objectData[min_amount]]);/*'一个【{0}】内子件【{1}】最小数量为{2}，请重新修改'*/
            } else if (objectData[increment] && !is_multiple(newAmountOrigin, objectData[increment])){
                isOverFlow = true;
                toastString = i18n('ava.object_form.cpq.amount_increment_multiple', [objectData[increment], newAmountOrigin]);/*'数量应该为增减数量幅度({0}）的整数倍，当前一个产品组合内子件数量为{1}，校验不通过'*/
            }
            if (isOverFlow) {
                this.pluginApi.showToast(toastString);
                Object.keys(changeData).forEach(it => {
                    delete changeData[it];
                });
            }
        }
    }
}