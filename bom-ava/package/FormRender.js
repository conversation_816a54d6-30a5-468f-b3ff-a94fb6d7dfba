import {getReadonlyFields, updateBomDescribe, updateSubLinesData} from "./utils/formutils";
import bomPriceQuerier from './BomPriceQuerier'
import BomProdPkgKeyGenerator from "./BomProdPkgKeyGenerator";
import {emitEvent} from "./events";

export class FormRender {

    constructor(context) {
        let {fieldMapping, bizStateConfig, requestApi, pluginApi} = context || {};
        this.context = context;
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.requestApi = requestApi;
        this.pluginApi = pluginApi;
    }

    formRenderBefore(pluginExecResult, options) {
        let self = this;
        let preResult = pluginExecResult && pluginExecResult.preData;
        return Object.assign({}, preResult, {
            beforeSaveDraft(param) {
                let {draft} = param || {};
                let objApiName = self.fieldMapping.getFirstDetailObjApiName();
                let detailFieldMapping = self.fieldMapping.getDetailFields(objApiName);
                let objectDataList = draft && draft.slave_draft_data && draft.slave_draft_data[objApiName];
                BomProdPkgKeyGenerator.generatorProdPkgKey(objectDataList, detailFieldMapping);
                return Promise.resolve({draft});
            }
        });
    }

    async formRenderEnd(pluginExecResult, options) {
        let {dataGetter, dataUpdater} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        // 页面初始化,处理子产品数据
        updateSubLinesData(objApiName, dataGetter, dataUpdater);
        let promiseA = this.setDetailData(pluginExecResult, options);
        let promiseB = this.calcBomPrice(pluginExecResult, options)
        return Promise.all([promiseA, promiseB]).then(() => pluginExecResult);
    }

    async setDetailData(pluginExecResult, options) {
        // 更新bom对象描述
        let bomDescribe = await this.requestApi.requestBomDescribe().catch(err => {
            console.log(err);
        })
        updateBomDescribe(bomDescribe);
        // 设置字段只读
        this.setDetailFieldReadonly(pluginExecResult, options);
    }

    setDetailFieldReadonly(pluginExecResult, options) {
        let self = this;
        let {dataGetter, dataUpdater} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let detailDataList = dataGetter.getDetail && dataGetter.getDetail(objApiName);
        let isOpenCpq = this.bizStateConfig.isOpenCpq();
        detailDataList && detailDataList.length && detailDataList.forEach(detailData => {
            let readonlyFields = getReadonlyFields(objApiName, detailData, objectDescribe, this.fieldMapping, isOpenCpq, (readonlyFields) => {
                self.pluginApi.runPluginSync(emitEvent.bom_updateReadonlyFields_before_sync, Object.assign({}, options, {
                    objectData: detailData,
                    readonlyFields
                }));
            }) || [];
            dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                objApiName,
                dataIndex: detailData.dataIndex,
                fieldName: readonlyFields,
                status: true,
                biz: 'bom',
                priority: 11
            });
        })
    }

    calcBomPrice(pluginExecResult, options) {
        if (options && options.applicablePriceSystem) {
            //复制、映射走一次bom取价，计算分摊
            return bomPriceQuerier.copyMappingTrigger(this.context, options);
        }
        return Promise.resolve();
    }
}