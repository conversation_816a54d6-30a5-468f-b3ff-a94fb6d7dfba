import bomPriceQuerier from './BomPriceQuerier'

export class MasterFieldEdit {

    constructor(context) {
        let {fieldMapping, pluginApi, bizStateConfig} = context;
        this.context = context;
        this.fieldMapping = fieldMapping;
        this.pluginApi = pluginApi;
        this.bizStateConfig = bizStateConfig;
    }

    async fieldEditEnd(pluginExecResult, options) {
        let {fieldName, changeData, masterObjApiName} = options;
        let {form_account_id, form_partner_id, form_price_book_id} = this.fieldMapping.getMasterFields();
        let priceBookValidField = this.bizStateConfig.getMatchPriceBookValidField(masterObjApiName);
        let fieldChanged = changeData && (!(typeof changeData[fieldName] === 'undefined'));
        if (fieldChanged) {
            if (fieldName === form_price_book_id) {
                await bomPriceQuerier.changeMasterPriceBookTrigger(this.context, options);
            } else if ([form_account_id, form_partner_id, priceBookValidField].includes(fieldName)) {
                await bomPriceQuerier.changeMasterAvailableRangeFieldTrigger(this.context, options);
            }
        }
    }
}