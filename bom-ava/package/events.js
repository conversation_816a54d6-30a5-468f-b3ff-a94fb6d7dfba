//向外抛出的事件
export const emitEvent = Object.freeze({
    bom_parseBomData2DetailData_sync: "bom.parseBomData2DetailData.sync",//解析bom数据到从对象数据
    bom_parseDetailData2BomData_sync: "bom.parseDetailData2BomData.sync",//解析从对象数据到bom数据
    bom_queryBomPrice_parseParams_before: "bom.queryBomPrice.parseParams.before",//bom取价前参数处理
    bom_queryBomPrice_before: "bom.queryBomPrice.before",//bom取价前事件，可拦截取价逻辑
    bom_queryBomPrice_end: "bom.queryBomPrice.end",//bom取价结束事件
    bom_updateReadonlyFields_before_sync: "bom.updateReadonlyFields.before.sync",//bom插件更新字段只读状态前
    bom_calcSubProduct_before_sync: "bom.calcSubProduct.before.sync",//更新子件后触发计算前钩子
    bom_calcSubProduct_end: "bom.calcSubProduct.end",//更新子件后触发计算结束钩子
});