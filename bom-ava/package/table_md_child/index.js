import mdchildcell_b from "../../../objformmain/base/fields/md/mdchildcell_b";
import {each, i18n, isEmpty, uuid} from "../../../pluginbase-ava/package/pluginutils";
import fsapi from 'fs-hera-api';
import bomPicker from "../bomtree/bomPicker";
import bomUtil from "../bomUtil";
import {handleSubProducts} from "../utils/formutils"
import defFieldMapping from "../defFieldMapping";
import {emitEvent} from "../events";

Component({

    options: {
        addGlobalClass: true
    },
    behaviors: [mdchildcell_b],

    data: {
        dShowGroupRows: undefined,//要显示分组的行列表
        dShowHierarchyRows: undefined,//要显示层级的行列表
    },

    methods: {

        beforeResetPageData(updatePageData) {
            let childCom = this.getMdChildCom();
            let simplePageList = updatePageData.dSimplePageList;
            let dataIndexList = simplePageList && simplePageList.map(it => it.dataIndex);
            let objectDataList = childCom._getObjDatasByDataIndexs(dataIndexList);
            let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
            objectDataList && objectDataList.forEach(it => {
                let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = it;
                let root = objectDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                let {dataIndex} = root || {};
                it[bomUtil.BomConst.productPackageDataIndex] = dataIndex;
                if (!isEmpty(parentProdPkgKey)) {//子产品
                    handleSubProducts(root, it, objectDataList);
                }
            });
            updatePageData.dHiddenDataIndexs = this.getHiddenDataIndexs(objectDataList);
            return updatePageData;
        },

        beforeCheckedDataIndexChanged(opt) {
            let {dataIndex, checked} = opt;
            let childCom = this.getMdChildCom();
            let objectDataList = childCom._getObjectDataListRef();
            let currentData = objectDataList && objectDataList.length && objectDataList.find(it => it.dataIndex === dataIndex);
            let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
            let {[prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey} = currentData || {};
            if (!isEmpty(parentProdPkgKey)) {//子产品
                fsapi.util.showToast(i18n('ava.object_form.cpq.select_sub_product_tip')/*子产品不允许单独选择*/);
                return;
            }
            if (bomUtil.isProductPackage(currentData)) {//产品包
                let checkedDataIndex = {[dataIndex]: checked};
                let childList = objectDataList.filter(it => {
                    return it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key];
                });
                childList && childList.forEach(it => {
                    checkedDataIndex[it.dataIndex] = checked
                });
                return checkedDataIndex
            }
            return {[dataIndex]: checked};
        },

        configBom(opt) {
            let {checkedDataIndexs = {}} = opt;
            let selectedIndexList = [];
            each(checkedDataIndexs, (value, key) => {
                if (value == 1) {
                    selectedIndexList.push(key);
                }
            });
            let childCom = this.getMdChildCom();
            let {dConfig} = childCom.data;
            let {apiname, formContext} = dConfig || {};
            let detailDataList = formContext.getDetailData(apiname);
            let selectedPkgList = detailDataList && detailDataList.filter(it => {
                return selectedIndexList.includes(it.dataIndex)
            }).filter(it => {
                return bomUtil.isProductPackage(it);
            });
            let isSelectedOneBom = selectedPkgList && selectedPkgList.length === 1;
            if (!isSelectedOneBom) {
                fsapi.util.showToast(i18n('ava.object_form.cpq.select_package_tip')/*请选择一条产品包数据*/);
                return;
            }
            let productPkgData = selectedPkgList[0];
            let masterData = formContext.getMasterData();
            let {dataIndex, object_describe_api_name, record_type} = productPkgData;
            let layoutFields = formContext.getDetailLayoutFields(object_describe_api_name, record_type);
            let {product_price} = defFieldMapping.getDetailFields();
            let priceField = layoutFields && layoutFields[product_price];
            let bizStateConfig = formContext.catchRunPluginHookSync('bom.getBizStateConfig', {});
            let params = bomUtil.createBomPageParams(masterData, productPkgData, detailDataList, {
                isShowPackagePrice: !isEmpty(priceField),
                bizStateConfig,
                parseDetailData2BomData: (detailData) => {
                    return formContext.catchRunPluginHookSync(emitEvent.bom_parseDetailData2BomData_sync, {objectData: detailData});
                }
            });
            bomUtil.go2ConfigBomPage(params)
                .then(async configBomResult => {
                    await formContext.catchRunPluginHook("bom.reconfiguration.queryBomPrice.before", {
                        objApiName: object_describe_api_name,
                        dataIndex
                    });
                    return formContext.catchRunPluginHook("bom.reconfiguration.after", {
                        objApiName: object_describe_api_name,
                        dataIndex,
                        configBomResult
                    });
                })
                .then(bomChangedInfo => {
                    return formContext.catchRunPluginHook("bom.reconfiguration.end", {
                        objApiName: object_describe_api_name,
                        dataIndex,
                        bomChangedInfo
                    });
                });
        },

        onGroupExpandClick(event) {
            let groupInfo = event.currentTarget.dataset.groupinfo;
            let {groupId, rootDataIndex} = groupInfo;
            let groupData = this.newGroupData(rootDataIndex, groupId);
            let isExpand = !bomPicker.isExpand(groupData);
            bomPicker.expand(isExpand, groupData);
            this.refreshView(true);
        },

        onNormalExpandClick(event) {
            let {dataindex: dataIndex} = event.currentTarget.dataset;
            let childCom = this.getMdChildCom();
            let objectDataList = childCom._getObjectDataListRef();
            let currentData = objectDataList && objectDataList.length && objectDataList.find(it => it.dataIndex === dataIndex);
            if (currentData) {
                let isExpand = !bomPicker.isExpand(currentData);
                bomPicker.expand(isExpand, currentData);
                this.refreshView(true);
            }
        },

        refreshView(updateHiddenState) {
            let childCom = this.getMdChildCom();
            let objectDataList = childCom._getObjectDataListRef();
            if (updateHiddenState) {
                let hiddenDataIndexs = this.getHiddenDataIndexs(objectDataList);
                childCom.changeDataHiddenState(hiddenDataIndexs);
            }
            let showGroupRows = this.getShowGroupRows(objectDataList);
            let showHierarchyRows = this.getShowHierarchyRows(objectDataList);
            this.setData({
                dShowGroupRows: showGroupRows,
                dShowHierarchyRows: showHierarchyRows,
            });
        },

        findLevel(objectData, objectDataList, level, groupId) {
            if (!objectDataList || !objectDataList.length || isEmpty(objectData)) {
                return level;
            }
            let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, product_group_id} = defFieldMapping.getDetailFields();
            let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey, [product_group_id]: productGroupId, [`${product_group_id}__v`]: productGroupIdV} = objectData;
            let hasGroup = !isEmpty(productGroupId) && !isEmpty(productGroupIdV);
            if (hasGroup && groupId !== productGroupIdV) {//如果有分组，且不是当前分组，level加一层
                level++;
            }
            let parent = !isEmpty(parentProdPkgKey) && objectDataList.find(it => {
                let {[prod_pkg_key]: prodPkgKey, [root_prod_pkg_key]: _rootProdPkgKey} = it;
                return prodPkgKey === parentProdPkgKey && _rootProdPkgKey === rootProdPkgKey;
            });
            if (parent) {
                level++;//如果有父节点，level加一层，再递归的遍历父节点来取最终的level
                return this.findLevel(parent, objectDataList, level, groupId)
            }
            return level;
        },

        findChild(objectData, objectDataList, childList, rootProdPkgKey) {
            if (!objectDataList || !objectDataList.length || isEmpty(objectData)) {
                return;
            }
            let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
            let {[prod_pkg_key]: prodPkgKey} = objectData;
            let currentChild = objectDataList.filter(it => {
                let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: _rootProdPkgKey} = it;
                return parentProdPkgKey === prodPkgKey && _rootProdPkgKey === rootProdPkgKey;
            });
            //当前节点的child
            if (currentChild && currentChild.length) {
                childList.push(...currentChild);
                currentChild.forEach(it => {
                    //递归获取当前节点的child的child
                    this.findChild(it, objectDataList, childList, rootProdPkgKey)
                })
            }
        },

        expandChild(objectData, objectDataList, isExpand) {
            let {prod_pkg_key, root_prod_pkg_key, product_group_id} = defFieldMapping.getDetailFields();
            let childList = [];
            let rootProdPkgKey = objectData[root_prod_pkg_key];
            this.findChild(objectData, objectDataList, childList, rootProdPkgKey);
            if (childList && childList.length) {
                let root = objectDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                let rootDataIndex = root && root.dataIndex;
                childList.forEach(it => {
                    bomPicker.expand(isExpand, it);//展开child
                    let {[product_group_id]: productGroupId, [`${product_group_id}__v`]: productGroupIdV} = it;
                    let hasGroup = !isEmpty(productGroupId) && !isEmpty(productGroupIdV);
                    hasGroup && bomPicker.expand(isExpand, this.newGroupData(rootDataIndex, productGroupIdV));//如果child有分组，则将分组也展开
                });
            }
        },

        getShowGroupRows(objectDataList) {
            let usedGroups = {};
            let {prod_pkg_key, root_prod_pkg_key, product_group_id} = defFieldMapping.getDetailFields();
            return objectDataList && objectDataList.map((it, index) => {
                let groupInfo = {show: false, index};//默认不显示分组
                let {[root_prod_pkg_key]: rootProdPkgKey, [product_group_id]: productGroupId, [`${product_group_id}__v`]: productGroupIdV} = it;
                let hasGroup = !isEmpty(productGroupId) && !isEmpty(productGroupIdV);
                if (!hasGroup) {//无分组信息，不显示分组
                    return groupInfo;
                }
                let isParentExpand = this.checkParentExpand(it, objectDataList, productGroupIdV);//父节点是否是展开状态
                if (!isParentExpand) {//父节点未展开，不显示分组
                    return groupInfo;
                }
                let root = objectDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                let {dataIndex = ''} = root || {};
                let uniqueId = `${dataIndex}_${productGroupIdV}`;//存在相同的包的场景，添加母件dataIndex来区分
                let used = usedGroups[uniqueId];
                if (used) {//分组已经使用过，不显示分组
                    return groupInfo;
                }
                let level = this.findLevel(it, objectDataList, 0, productGroupIdV);
                let isExpand = bomPicker.isExpand(this.newGroupData(dataIndex, productGroupIdV));
                usedGroups[uniqueId] = true;
                Object.assign(groupInfo, {
                    show: true,//显示分组
                    groupName: productGroupId,
                    groupId: productGroupIdV,
                    level,//所处的层级
                    isExpand,
                    rootDataIndex: dataIndex,
                    uniqueId: uniqueId
                });
                return groupInfo;
            });
        },

        getShowHierarchyRows(objectDataList) {
            let showHierarchyRows = [];
            let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
            objectDataList && objectDataList.forEach((it, index) => {
                let {[prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey, dataIndex} = it;
                let isProductPackage = bomUtil.isProductPackage(it);
                let isSubLines = !isEmpty(parentProdPkgKey);
                if (isProductPackage || isSubLines) {
                    let level = this.findLevel(it, objectDataList, 0);
                    let hasChild = isSubLines && objectDataList.some(it => {
                        let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: _rootProdPkgKey} = it;
                        return parentProdPkgKey === prodPkgKey && _rootProdPkgKey === rootProdPkgKey;
                    });
                    let isExpand = bomPicker.isExpand(it);
                    showHierarchyRows.push({
                        index,
                        showExpandIcon: isProductPackage || (isSubLines && hasChild),
                        level,
                        dataIndex,
                        isExpand
                    })
                }
            });
            return showHierarchyRows;
        },

        getHiddenDataIndexs(objectDataList) {
            let hiddenDataIndexs = {};
            let {parent_prod_pkg_key} = defFieldMapping.getDetailFields();
            objectDataList && objectDataList.forEach(it => {
                let {[parent_prod_pkg_key]: parentProdPkgKey, dataIndex} = it;
                let isSubLines = !isEmpty(parentProdPkgKey);
                if (isSubLines) {
                    let isParentExpand = this.checkParentExpand(it, objectDataList);
                    hiddenDataIndexs[dataIndex] = isParentExpand ? 0 : 1;
                }
            });
            return hiddenDataIndexs;
        },

        handleCopyDataList(copyDataList) {
            if (copyDataList && copyDataList.length) {
                let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
                let newProdKeyObj = {};
                //先更新母件的虚拟key
                copyDataList.forEach(copyData => {
                    let isPackage = bomUtil.isProductPackage(copyData);
                    if (isPackage) {
                        let newProdPkgKey = uuid();
                        let orgProdPkgKey = copyData[prod_pkg_key];
                        copyData[prod_pkg_key] = newProdPkgKey;
                        copyData[root_prod_pkg_key] = newProdPkgKey;
                        newProdKeyObj[orgProdPkgKey] = newProdPkgKey;
                    }
                });
                //再更新子件的虚拟key
                copyDataList.forEach(copyData => {
                    let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = copyData;
                    if (!isEmpty(parentProdPkgKey)) {
                        let parentResult = newProdKeyObj[parentProdPkgKey];
                        if (!isEmpty(parentResult)) {
                            copyData[parent_prod_pkg_key] = newProdKeyObj[rootProdPkgKey];
                        }
                        copyData[root_prod_pkg_key] = newProdKeyObj[rootProdPkgKey];
                    }
                });
            }
            return copyDataList;
        },

        newGroupData(rootDataIndex, groupId) {
            return {
                [bomUtil.BomConst.isProductGroup]: true,
                [bomUtil.BomConst.productPackageDataIndex]: rootDataIndex,
                _id: groupId
            }
        },

        checkParentExpand(objectData, objectDataList, skipGroupId) {
            if (isEmpty(objectData) || !objectDataList) {
                return false;
            }
            let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, product_group_id} = defFieldMapping.getDetailFields();
            let {[prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey, [product_group_id]: productGroupId, [`${product_group_id}__v`]: productGroupIdV} = objectData;
            //当前节点的父节点
            let parent = !isEmpty(parentProdPkgKey) && objectDataList.find(it => {
                let {[prod_pkg_key]: prodPkgKey, [root_prod_pkg_key]: _rootProdPkgKey} = it;
                return prodPkgKey === parentProdPkgKey && _rootProdPkgKey === rootProdPkgKey;
            });
            let hasGroup = !isEmpty(productGroupId) && !isEmpty(productGroupIdV);
            let checkGroup = !(skipGroupId && productGroupIdV === skipGroupId);//是否需要校验当前分组展开状态
            if (hasGroup && checkGroup) {//当前节点有分组
                let root = objectDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                let groupData = this.newGroupData(root && root.dataIndex, productGroupIdV);
                let expand = bomPicker.isExpand(groupData);
                if (!expand) {//分组未展开
                    return false;
                }
            }
            if (parent) {//当前节点有父节点
                let expand = bomPicker.isExpand(parent);
                //父节点展开时，再判断父节点的父节点是否是展开状态
                return expand ? this.checkParentExpand(parent, objectDataList, skipGroupId) : false;
            }
            //判断根节点是否是展开状态
            let isRoot = prodPkgKey && prodPkgKey === rootProdPkgKey;
            return isRoot ? bomPicker.isExpand(objectData) : false;
        }
    },

    lifetimes: {
        attached() {
            let self = this;
            this.setData({
                dHooks: {
                    beforeResetPageData(updatePageData) {
                        return self.beforeResetPageData(updatePageData);
                    },

                    afterSimplePageListChanged() {
                        self.refreshView(false);
                    },

                    beforeCheckedDataIndexChanged(opt) {
                        return self.beforeCheckedDataIndexChanged(opt);
                    },

                    handleBatchButtons(buttons) {
                        return (buttons || []).concat({
                            label: i18n('ava.object_form.onsale.bom_config'),/*配置*/
                            action: "cpq-config",
                            onClick(opt) {
                                self.configBom(opt);
                            }
                        });
                    },

                    getPageSize() {
                        return 1000;
                    },

                    handleCopyDataList(copyDataList, removeFieldDataFunc) {
                        return self.handleCopyDataList(copyDataList, removeFieldDataFunc)
                    }
                }
            })
        }
    }
});
