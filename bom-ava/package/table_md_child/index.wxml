<tablemdchild id="com" index="{{index}}" refreshUI="{{refreshUI}}" bind:getDataDetail="_getDataDetail"
              hooks="{{dHooks}}">
    <view wx:for="{{dShowGroupRows||[]}}"
          slot="data_row_{{item.index}}_top"
          style="display: flex"
          data-groupInfo="{{item}}"
          data-index="{{item.index}}"
          bindtap="onGroupExpandClick">
        <view style="display: flex; position: sticky; left: 0;">
            <view style="display: flex; flex-direction: column; width: 100vw">
                <view class="group-container" hidden="{{!item.show}}">
                    <view wx:for="{{item.level}}" class="dot"></view>
                    <view class="fxui_all xialasanjiao arrow-down {{item.isExpand?'':'collapse'}}"></view>
                    <text class="group-text">{{item.groupName}}</text>
                </view>
                <view hidden="{{!item.show}}" style="height: 0.5px; background: #E6E7EA;"></view>
            </view>
        </view>
    </view>
    <view wx:for="{{dShowHierarchyRows||[]}}"
          slot="data_row_{{item.index}}_col_0_before"
          class="row-before"
          data-dataIndex="{{item.dataIndex}}"
          bindtap="onNormalExpandClick">
        <view wx:for="{{item.level}}" class="dot"></view>
        <view wx:if="{{item.showExpandIcon}}"
              class="fxui_all xialasanjiao arrow-down {{item.isExpand?'':'collapse'}}"></view>
    </view>
</tablemdchild>
