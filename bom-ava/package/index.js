import defFieldMapping from "./defFieldMapping";
import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import FieldMapping from "../../pluginbase-ava/package/FieldMapping";
import {FormRender} from "./FormRender";
import {FormSubmit} from "./FormSubmit";
import {MDRender} from "./MDRender";
import {MdBatchAdd} from './MdBatchAdd';
import {DetailFieldEdit} from './DetailFieldEdit'
import {MasterFieldEdit} from './MasterFieldEdit'
import {PriceService} from "./PriceService";
import BomServiceApi from './BomServiceApi';
import bomPriceQuerier from "./BomPriceQuerier";
import log from "../../pluginbase-ava/package/log";
import bomUtil from "./bomUtil";
import bomHookConfig from './hook/BomHookConfig'
import {isEmpty} from "../../pluginbase-ava/package/pluginutils";
import BomProdPkgKeyGenerator from "./BomProdPkgKeyGenerator";

export default class Bom {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        let fieldMapping = new FieldMapping(pluginParam.params, defFieldMapping.defFieldMapping);
        defFieldMapping.updateFieldMapping(fieldMapping);
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        let context = {
            fieldMapping: this.fieldMapping,
            bizStateConfig: this.bizStateConfig,
            pluginApi: new PluginApi(pluginService),
            requestApi: new BomServiceApi(pluginService.api.request),
        }
        this.context = context;
        this.formRender = new FormRender(context);
        this.formSubmit = new FormSubmit(context);
        this.mdRender = new MDRender(context);
        this.mdBatchAdd = new MdBatchAdd(context);
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.masterFieldEdit = new MasterFieldEdit(context);
        this.priceService = new PriceService(context);
        bomHookConfig.initContext(context);
    }

    formRenderBefore(pluginExecResult, options) {
        bomHookConfig.pluginServiceUseAfter(pluginExecResult, options);
        return this.formRender.formRenderBefore(pluginExecResult, options);
    }

    formRenderEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return this.formRender.formRenderEnd(pluginExecResult, Object.assign({}, options, {applicablePriceSystem: this.applicablePriceSystem}));
    }

    formSubmitBefore(pluginExecResult, options) {
        return this.formSubmit.formSubmitBefore(pluginExecResult, options);
    }

    formSubmitPostBefore(pluginExecResult, options) {
        return this.formSubmit.formSubmitPostBefore(pluginExecResult, options);
    }

    mdRenderBefore(pluginExecResult, options) {
        return this.mdRender.mdRenderBefore(pluginExecResult, options);
    }

    mdItemRenderBeforeSync(pluginExecResult, options) {
        return this.mdRender.mdItemRenderBeforeSync(pluginExecResult, options);
    }

    mdBatchAddBefore(pluginExecResult, options) {
        return this.mdBatchAdd.mdBatchAddBefore(pluginExecResult, options);
    }

    mdBatchAddAfter(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return this.mdBatchAdd.mdBatchAddAfter(pluginExecResult, options);
    }

    mdBatchAddEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return this.mdBatchAdd.mdBatchAddEnd(pluginExecResult, options);
    }

    mdDelAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdDelAfter(pluginExecResult, options);
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneAfter(pluginExecResult, options);
    }

    mdCloneEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return this.mdBatchAdd.mdCloneEnd(pluginExecResult, options);
    }

    fieldEditAfter(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        let {masterObjApiName, objApiName} = options;
        if (objApiName !== masterObjApiName) {
            return this.detailFieldEdit.fieldEditEnd(pluginExecResult, options);
        } else {
            return this.masterFieldEdit.fieldEditEnd(pluginExecResult, options);
        }
    }

    priceServiceBatchSelectSkuConfigSync(pluginExecResult, options) {
        return this.priceService.priceServiceBatchSelectSkuConfigSync(pluginExecResult, options);
    }

    priceServiceFormParseFullProductSync(pluginExecResult, options) {
        return this.priceService.priceServiceFormParseFullProductSync(pluginExecResult, options);
    }

    priceServiceGetBackFillsBeforeSync(pluginExecResult, options) {
        return this.priceService.priceServiceGetBackFillsBeforeSync(pluginExecResult, options);
    }

    priceServiceFilterProductBeforeSync(pluginExecResult, options) {
        return this.priceService.priceServiceFilterProductBeforeSync(pluginExecResult, options);
    }
    
    priceServiceDetailDataDoNotCalcPriceSync(pluginExecResult, options) {
        return this.priceService.priceServiceDetailDataDoNotCalcPriceSync(pluginExecResult, options);
    }

    priceServiceTriggerCalcEndSync(pluginExecResult, options) {
        let {calcResult, scene} = options || {};
        let applicablePriceSystem = calcResult && calcResult.getRealPriceResult
            && calcResult.getRealPriceResult.applicablePriceSystem;
        this.applicablePriceSystem = (scene === 'formRender') ? applicablePriceSystem : false;
    }

    priceServiceDeleteDetailAfterSync(pluginExecResult, options) {
        return this.priceService.priceServiceDeleteDetailAfterSync(pluginExecResult, options);
    }

    bomReconfigurationAfter(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return bomPriceQuerier.reconfigurationBomTrigger(this.context, options);
    }

    bomGetSubDetailsFromPkgSync(pluginExecResult, options) {
        let {objApiName, dataIndex, dataGetter} = options;
        let objectData = dataGetter.getData(objApiName, dataIndex);
        let isProductPkg = bomUtil.isProductPackage(objectData);
        if (!isProductPkg) {
            return [];
        }
        let detailDatas = dataGetter.getDetail(objApiName);
        let fieldMapping = this.context.fieldMapping;
        let detailFieldMap = fieldMapping.getDetailFields(objApiName);
        let {root_prod_pkg_key, prod_pkg_key} = detailFieldMap;
        return detailDatas && detailDatas.filter(it => {
            return it[root_prod_pkg_key] === objectData[prod_pkg_key] && it[root_prod_pkg_key] !== it[prod_pkg_key];
        }) || [];
    }

    bomIsSubProductSync(pluginExecResult, options) {
        let {objApiName, objectData} = options || {};
        let {parent_prod_pkg_key} = this.fieldMapping.getDetailFields(objApiName);
        let {[parent_prod_pkg_key]: parentProdPkgKey} = objectData || {};
        return parentProdPkgKey !== undefined && parentProdPkgKey !== null;
    }

    bomGetChildrenSync(pluginExecResult, options) {
        let {objApiName, dataGetter, objectData} = options;
        let fieldMapping = this.context.fieldMapping;
        let {prod_pkg_key, parent_prod_pkg_key} = fieldMapping.getDetailFields(objApiName);
        let prodPkgKey = objectData && objectData[prod_pkg_key]
        if (isEmpty(prodPkgKey)) {
            return;
        }
        let detailDatas = dataGetter.getDetail(objApiName);
        return detailDatas && detailDatas.filter(it => {
            let parentProdPkgKey = it && it[parent_prod_pkg_key];
            return parentProdPkgKey === prodPkgKey;
        });
    }

    bomDeleteBomAfter(pluginExecResult, options) {
        this.mdBatchAdd.mdDelAfter(pluginExecResult, options);
        let {dataUpdater, objApiName, delDatas, formApis} = options;
        let delDataIndexs = delDatas && delDatas.length && delDatas.map(it => it.dataIndex);
        dataUpdater.del(objApiName, delDataIndexs);
        return formApis.triggerCalAndUIEvent({objApiName, delDatas})
    }

    policyMatchEventExecEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        let {objectDataDiffMap, detailObjectApiName} = options;
        let modifiedDataList = objectDataDiffMap && objectDataDiffMap[detailObjectApiName] || {};
        let modifiedDataIndexList = Object.keys(modifiedDataList);
        return bomPriceQuerier.matchPricePolicyTrigger(modifiedDataIndexList, this.context, options);
    }

    pluginServiceUseAfter(pluginExecResult, options) {
        // return bomHookConfig.pluginServiceUseAfter(pluginExecResult, options);
    }

    updateForcePriority(pluginExecResult, options) {
        let {forcePriority} = options || {};
        if (!isEmpty(forcePriority)) {
            let result = forcePriority ? 1 : 0;
            this.bizStateConfig && this.bizStateConfig.updateBizStateConfig('enforce_priority', result);
        }
    }

    quoterExecuteAfter(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return bomPriceQuerier.quoterExecuteAfter(this.context, options);
    }

    quoterExecuteEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return bomPriceQuerier.quoterExecuteEnd(this.context, options);
    }

    bomRegenerationProdPkgKeySync(pluginExecResult, options) {
        let {objApiName, objectDataList} = options || {};
        if (!objApiName) {
            objApiName = this.fieldMapping.getFirstDetailObjApiName();
        }
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        BomProdPkgKeyGenerator.generatorProdPkgKeyWithRoot(objectDataList, detailFieldMapping);
    }

    periodProductPricingPeriodChangeEnd(pluginExecResult, options) {
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        return bomPriceQuerier.periodProductPricingPeriodChangeEnd(this.context, options);
    }

    getBizStateConfig() {
        return this.bizStateConfig;
    }

    apply() {
        let isOpenCPQ = this.bizStateConfig.isOpenCpq();
        let isOpenSimpleCPQ = this.bizStateConfig.isOpenSimpleCpq();
        if (!isOpenCPQ && !isOpenSimpleCPQ) {
            return [];
        }
        let isOpenBomDeleteRoot = this.bizStateConfig.isOpenBomDeleteRoot();
        let hooks = isOpenBomDeleteRoot ? [{
            event: "form.submit.before",
            functional: this.formSubmitBefore.bind(this)
        }, {
            event: "md.batchAdd.after",
            functional: this.mdBatchAddAfter.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.mdBatchAddEnd.bind(this)
        }, {
            event: "price-service.batchSelectSkuConfig.sync",
            functional: this.priceServiceBatchSelectSkuConfigSync.bind(this)
        }] : [{
            event: "form.render.before",
            functional: this.formRenderBefore.bind(this)
        }, {
            event: "form.render.end",
            functional: this.formRenderEnd.bind(this)
        }, {
            event: "form.submit.before",
            functional: this.formSubmitBefore.bind(this)
        },{
            event: "form.submit.post.before",
            functional: this.formSubmitPostBefore.bind(this)
        }, {
            event: "md.render.before",
            functional: this.mdRenderBefore.bind(this)
        }, {
            event: "md.item.render.before.sync",
            functional: this.mdItemRenderBeforeSync.bind(this)
        }, {
            event: "md.batchAdd.before",
            functional: this.mdBatchAddBefore.bind(this)
        }, {
            event: "md.batchAdd.after",
            functional: this.mdBatchAddAfter.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.mdBatchAddEnd.bind(this)
        }, {
            event: "md.del.after",
            functional: this.mdDelAfter.bind(this)
        }, {
            event: "md.clone.after",
            functional: this.mdCloneAfter.bind(this)
        }, {
            event: "md.clone.end",
            functional: this.mdCloneEnd.bind(this)
        }, {
            event: "field.edit.after",
            functional: this.fieldEditAfter.bind(this)
        }, {
            event: "field.edit.end",
            functional: this.fieldEditEnd.bind(this)
        }, {
            event: "price-service.batchSelectSkuConfig.sync",
            functional: this.priceServiceBatchSelectSkuConfigSync.bind(this)
        }, {
            event: "price-service.form.parseFullProduct.sync",
            functional: this.priceServiceFormParseFullProductSync.bind(this)
        }, {
            event: "price-service.getBackFills.before.sync",
            functional: this.priceServiceGetBackFillsBeforeSync.bind(this)
        }, {
            event: "price-service.filterProduct.before.sync",
            functional: this.priceServiceFilterProductBeforeSync.bind(this)
        }, {
            event: "bom.reconfiguration.after",
            functional: this.bomReconfigurationAfter.bind(this)
        }, {
            event: "bom.getSubDetailsFromPkg.sync",
            functional: this.bomGetSubDetailsFromPkgSync.bind(this)
        }, {
            event: "bom.deleteBom.after",
            functional: this.bomDeleteBomAfter.bind(this)
        }, {
            event: "policy.match.event.exec.end",
            functional: this.policyMatchEventExecEnd.bind(this)
        }, {
            event: "price-service.form.detailDataDoNotCalcPrice.sync",
            functional: this.priceServiceDetailDataDoNotCalcPriceSync.bind(this)
        }, {
            event: "dht.priceBookPriority.update.before",
            functional: this.updateForcePriority.bind(this)
        }, {
            event: "quoter.execute.after",
            functional: this.quoterExecuteAfter.bind(this)
        }, {
            event: "quoter.execute.end",
            functional: this.quoterExecuteEnd.bind(this)
        }, {
            event: 'price-service.triggerCalc.end.sync',
            functional: this.priceServiceTriggerCalcEndSync.bind(this)
        }, {
            event: "price-service.deleteDetail.after.sync",
            functional: this.priceServiceDeleteDetailAfterSync.bind(this)
        }, {
            event: "period-product.pricing_period.change.end",
            functional: this.periodProductPricingPeriodChangeEnd.bind(this)
        }, {
            event: "bom.isSubProduct.sync",
            functional: this.bomIsSubProductSync.bind(this)
        },{
            event: "bom.getChildren.sync",
            functional: this.bomGetChildrenSync.bind(this)
        }];
        hooks.push({
            event: "pluginService.use.after",
            functional: this.pluginServiceUseAfter.bind(this)
        },{
            event: "bom.regenerationProdPkgKey.sync",
            functional: this.bomRegenerationProdPkgKeySync.bind(this)
        }, {
            event: "bom.getBizStateConfig",
            functional: this.getBizStateConfig.bind(this)
        });
        return hooks;
    }
}
