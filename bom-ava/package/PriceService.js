import {divide, formatValueDecimalPlaces, isEmpty, multiply, uniq} from "../../pluginbase-ava/package/pluginutils";
import bomUtil from "./bomUtil";
import {MdBatchAdd} from "./MdBatchAdd";

export class PriceService {

    constructor(context) {
        let {fieldMapping, bizStateConfig} = context;
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.mdBatchAdd = new MdBatchAdd(context);
    }

    priceServiceBatchSelectSkuConfigSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let {objApiName, dataGetter, recordType} = options;
        let isOpenCPQ = this.bizStateConfig.isOpenCpq();
        let isOpenSimpleCPQ = this.bizStateConfig.isOpenSimpleCpq();
        let {product_price} = this.fieldMapping.getDetailFields(objApiName);
        let layoutFields = dataGetter.getLayoutFields(objApiName, recordType)
        let priceField = layoutFields && layoutFields[product_price];
        return Object.assign({}, preData, {
            isOpenCPQ,//是否开启了CPQ
            isOpenSimpleCPQ,//是否开启了固定组合
            enableConfigBom: true,
            isShowPackagePrice: !isEmpty(priceField)
        });
    }

    priceServiceFormParseFullProductSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let {objApiName, detailData, dataGetter, type} = options;
        let {root_prod_pkg_key, parent_prod_pkg_key, prod_pkg_key, bom_id, quantity, amount_any, product_price} = this.fieldMapping.getDetailFields(objApiName);
        let {
            [prod_pkg_key]: prodKey, [root_prod_pkg_key]: rootProdKey, [parent_prod_pkg_key]: parentProdKey, [bom_id]: bomId, [quantity]: quantityValue, [`${amount_any}__v`]: amountAny,
            [product_price]: productPrice
        } = detailData;
        let amount;
        let price;
        if (isEmpty(parentProdKey)) {//非子产品取本身数量
            amount = quantityValue;
            price = productPrice;
        } else {//子产品的数量需要特殊处理下：除以包的数量
            let detailDataList = dataGetter && dataGetter.getDetail(objApiName);
            const recentSubBom = bomUtil.getRecentSubBomData(detailData, detailDataList);
            let recentSubBomAmount = recentSubBom && recentSubBom[quantity] || 1;
            amount = amountAny ? quantityValue : recentSubBomAmount == 0 ? 1 : divide(quantityValue, recentSubBomAmount);
            price = ['masterFieldEdit', 'copy', 'convert'].includes(type) ? null : productPrice;
        }
        return Object.assign({}, preData, {rootProdKey, parentProdKey, prodKey, bomId, amount, price});
    }

    priceServiceGetBackFillsBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData || {};
        let {objApiName, detailData, getRealPriceResult, dataGetter} = options;
        if (isEmpty(detailData)) {
            return;
        }
        let {
            parent_prod_pkg_key, price_mode, discount, extra_discount, price_book_id, price_book_product_id,
            attribute_price_book_id, product_price
        } = this.fieldMapping.getDetailFields(objApiName);
        let {[parent_prod_pkg_key]: parentProdKey, [`${price_mode}__v`]: priceMode, [product_price]: productPrice} = detailData;
        if (!isEmpty(parentProdKey)) {//子产品的折扣、额外折扣设置为0
            let price;
            let isConfigPriceMode = priceMode == '1';
            if (isConfigPriceMode) {
                //子件是配置价格，取原价
                price = productPrice;
            } else {
                //子件是价目表价格，取selling_price/discount
                let objectDescribe = dataGetter.getDescribe(objApiName);
                let productPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields[product_price];
                let {decimal_places = 0} = productPriceField || {};
                let {selling_price, discount} = getRealPriceResult || {};
                let result = multiply(selling_price, divide(discount, 100));
                price = formatValueDecimalPlaces(result, decimal_places);
            }
            return Object.assign({}, preData, {
                [discount]: '0',
                [extra_discount]: '0',
                [product_price]: price
            }, isConfigPriceMode && {//子产品定价模式如果为配置价格，则不回填价目表、价目表明细、属性价目表字段
                [price_book_id]: null,
                [`${price_book_id}__r`]: null,
                [price_book_product_id]: null,
                [`${price_book_product_id}__r`]: null,
                [attribute_price_book_id]: null,
                [`${attribute_price_book_id}__r`]: null
            });
        }
    }

    priceServiceFilterProductBeforeSync(pluginExecResult, options) {
        let {objectData, objApiName} = options;
        let {parent_prod_pkg_key} = this.fieldMapping.getDetailFields(objApiName);
        let {[parent_prod_pkg_key]: parentProdPkgKey} = objectData || {};
        if (!isEmpty(parentProdPkgKey)) {
            return false;
        }
    }

    priceServiceDetailDataDoNotCalcPriceSync(pluginExecResult, options) {
        let { objApiName, detailData, action } = options;
        if (isEmpty(detailData)) {
            return;
        }
        let {
            parent_prod_pkg_key,
            price_mode
        } = this.fieldMapping.getDetailFields(objApiName);
        let { 
            [parent_prod_pkg_key]: parentProdPkgKey,
            [`${price_mode}__v`]: priceMode 
        } = detailData;

        // 问题：先手动修改价格，再修改数量，取价之后将价格重置覆盖问题
        // 解决：修改子产品数量后不重新取价
        if (!isEmpty(parentProdPkgKey) && action == 'fieldEditAfter') {
            return { cpq: true };
        }
        //子件如果是配置价格，复制映射草稿箱不参与取价
        let isConfigPriceMode = priceMode == '1';
        if (!isEmpty(parentProdPkgKey) && isConfigPriceMode && ['copy', 'convert'].includes(action)) {
            return { cpq: true };
        }
    }

    priceServiceDeleteDetailAfterSync(pluginExecResult, options) {
        let {dataGetter, objApiName, deleteDataIndexList} = options;
        if (!deleteDataIndexList || !deleteDataIndexList.length) {
            return;
        }
        let detailDataList = dataGetter && dataGetter.getDetail && dataGetter.getDetail(objApiName);
        if (!detailDataList || !detailDataList.length) {
            return;
        }
        let deleteDataIndexs = [];
        deleteDataIndexList.forEach(dataIndex => {
            let objectData = detailDataList.find(it => it.dataIndex === dataIndex)
            let childList = this.mdBatchAdd.findChild(objectData, detailDataList);
            if (childList && childList.length) {
                let childDataIndexList = childList.map(it => it.dataIndex);
                deleteDataIndexs.push(...childDataIndexList);
            }
        })
        let result = uniq(deleteDataIndexs);
        return {deleteDataIndexs: result};
    }
}