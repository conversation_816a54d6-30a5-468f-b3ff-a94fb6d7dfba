import {divide, formatDataDecimalPlaces, isEmpty, multiply, uuid, i18n, requireAsync} from "../../pluginbase-ava/package/pluginutils";
import fsapi from 'fs-hera-api'
import defFieldMapping from "./defFieldMapping";

const BomConst = {
    subProductSelectedInProduct: 'key_sub_products_selected_in_product',//产品下选择的子产品明细
    adjustTotalPrice: "adjust_total_price",//配置后的产品包价格
    isDefaultConfig: "key_is_default_config",//配置后的产品包是否是后台的默认配置
    subProductOriginalQuantity: "key_sub_product_original_quantity",//产品包为1时的子产品明细数量值
    subLines: "key_sub_lines",//xx明细下子xx明细数据
    enableConfigBom: "enable_config_bom",//是否支持配置产品包
    isProductGroup: "is_product_group",//是否是产品分组
    productPackageDataIndex: "product_package_data_index",
    amountOriginSubBom: "amount_origin_subBom", // 单个复用bom下数量比例
};

function isProductPackage(objectData) {
    if (!objectData) {
        return false;
    }
    let {parent_prod_pkg_key, is_package, rebate_coupon_id, parent_gift_key} = defFieldMapping.getDetailFields();
    if (['ProductObj', 'PriceBookProductObj'].includes(objectData.object_describe_api_name)) {
        is_package = 'is_package';
    }
    let {
        [parent_prod_pkg_key]: parentProdPkgKey, [is_package]: isPackage, [`${is_package}__v`]: isPackageV,
        [rebate_coupon_id]: rebateCouponId, [parent_gift_key]: parentGiftKey
    } = objectData;
    if (rebateCouponId || parentGiftKey) {
        return false;
    }
    if (parentProdPkgKey !== undefined && parentProdPkgKey !== null) {
        return false;
    }
    let isPackageObj = (isPackageV === undefined || isPackageV === null) ? isPackage : isPackageV;
    if (typeof isPackageObj === "boolean") {
        return isPackageObj;
    } else if (typeof isPackageObj === 'string') {
        return isPackageObj === i18n('ava.object_form.crm.yes')/*是*/ || isPackageObj === 'true';
    }
}

function isDecimalPlacesInvalid(value, decimal_places) {
    value = parseFloat(value) + '';
    const arr = value.split('.');
    if (arr && arr[1]) {
        return arr[1].length > decimal_places;
    }
    return false;
}

// 四舍五入
export function formatValueDecimalPlaces(value, decimalPlaces) {
    if (value === undefined || value === null || value === '') {
        return value;
    }

    const multiplier = Math.pow(10, decimalPlaces);
    return Math.round(value * multiplier) / multiplier;

}

function createBomPageParams(masterData, objectData, objectDataList, opt) {
    let {form_account_id, form_partner_id} = defFieldMapping.getMasterFields();
    let objApiName = objectData && objectData.object_describe_api_name;
    let {
        product_price, product_id, price_book_id, price_book_product_id, prod_pkg_key, root_prod_pkg_key,
        bom_core_id, bom_version, bom_type, bom_id
    } = defFieldMapping.getDetailFields();
    let {
        [product_price]: adjustTotalPrice, [product_id]: productId, [`${product_id}__r`]: productName, [price_book_id]: priceBookId,
        [price_book_product_id]: priceBookProductId, [prod_pkg_key]: prodPkgKey, dataIndex,
        [bom_core_id]: bomCoreId, [`${bom_core_id}__r`]: bomCoreName, [bom_version]: bomVersion, [`${bom_type}__v`]: bomTypeV, [bom_id]: bomId,
    } = objectData || {};
    let {isShowPackagePrice, bizStateConfig, parseDetailData2BomData} = opt || {};
    let {[form_account_id]: accountId, [form_partner_id]: partnerId, object_describe_api_name} = masterData || {};
    let openPriceBook = bizStateConfig && bizStateConfig.isOpenPriceBook();
    let enforcePriority = bizStateConfig && bizStateConfig.isOpenPriceBookPriority();
    let enablePeriodicProductPlugin = bizStateConfig && bizStateConfig.enablePeriodicProductPlugin(object_describe_api_name);
    let parseBomResult = parseDetailData2BomData && parseDetailData2BomData(objectData);
    let subLinesList = objectDataList && objectDataList.length && objectDataList.filter(it => {
        return it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key];
    });
    let subProducts = subLinesList2SubProducts(objectData, subLinesList, opt);
    let sortDetailDatas = [objectData];//当前操作的数据放到首位
    objectDataList && objectDataList.forEach(detailData => {
        if (detailData.dataIndex != dataIndex) {
            sortDetailDatas.push(detailData)
        }
    });
    let bomCoreData;
    if (!isEmpty(bomCoreId)) {
        bomCoreData = {
            _id: bomCoreId,
            name: bomCoreName,
            node_bom_core_version: bomVersion,
            node_bom_core_type: bomTypeV,
        }
    }
    return Object.assign({
        product_id: productId,
        product_id__r: productName,
        selected_list: subProducts,
        accountId: accountId,
        partnerId: partnerId,
        enableValidorder: false,//不出现禁用和过期的，bom配置页面跳转到选价目表产品页面会用到
        enforcePriority: enforcePriority,
        isOpenPriceBook: openPriceBook,
        price_book_product_id: priceBookProductId,
        price_book_id: priceBookId,
        isPriceBookDecoupling: true,
        adjust_total_price: adjustTotalPrice,
        isPackageAppointedPriceBook: false,
        masterData: masterData,
        isShowPackagePrice,
        details: {[objApiName]: sortDetailDatas},
        enableHugeData: true,
        bomCoreData,
        bom_id: bomId,
        sourceAction: "modifyConfigBtn",
        selectObjectConfig: {enablePeriodicProductPlugin}
    }, parseBomResult)
}

function go2ConfigBomPage(params) {
    return new Promise((resolve, reject) => {
        fsapi.page.utilOpen({
            name: "ava://object_list/object_list_sfa_o2c/pages/select_bomobj/index",
            params: params || {},
            responsive: true,
            onSuccess: (result) => {
                resolve(result);
            },
            onFail: (error) => {
                reject(error)
            }
        });
    })
}

function subLinesList2SubProducts(packageLines, subLinesList, opt) {
    let {
        product_price, quantity, product_id, price_book_id, price_book_product_id, node_type, node_no,
        prod_pkg_key, parent_prod_pkg_key, product_group_id, bom_id, unit, temp_node_group_id,
        bom_core_id, new_bom_path, bom_version, bom_type, amount_any
    } = defFieldMapping.getDetailFields();
    let {[quantity]: packageQuantity, [prod_pkg_key]: packageProdPkgKey, [bom_id]: packageBomId} = packageLines;
    let {parseDetailData2BomData} = opt || {};
    return subLinesList && subLinesList.map(it => {
        let {
            [product_price]: productPrice, [quantity]: quantityValue,
            [bom_id]: bomId, [`${bom_id}__r`]: bomName,
            [product_id]: productId, [`${product_id}__r`]: productName,
            [price_book_product_id]: priceBookProductId, [`${price_book_product_id}__r`]: priceBookProductName,
            [price_book_id]: priceBookId, [`${price_book_id}__r`]: priceBookName, [node_type]: nodeType, [node_no]: nodeNo,
            [prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [`${product_group_id}__v`]: productGroupIdV, [product_group_id]: productGroupId,
            [temp_node_group_id]: tempNodeGroupId, [`${temp_node_group_id}__r`]: tempNodeGroupName,
            [unit]: unitId, [bom_core_id]: bomCoreId, [new_bom_path]: newBomPath, [bom_version]: bomVersion, [bom_type]: bomType, [`${amount_any}__v`]: amountAny, amount_origin_subBom
        } = it;

        let parentBomId;
        if (parentProdPkgKey === packageProdPkgKey) {
            parentBomId = packageBomId;
        } else {
            let parentSubLines = subLinesList.find(it => it[prod_pkg_key] === parentProdPkgKey);
            parentBomId = parentSubLines && parentSubLines[bom_id]
        }
        let parseBomResult = parseDetailData2BomData && parseDetailData2BomData(it);
        return Object.assign({
            _id: bomId,
            name: bomName,
            // modified_adjust_amount: subQuantity,
            amount_origin_subBom,
            modified_adjust_price: productPrice,
            product_id: productId,
            product_id__r: productName,
            prod_pkg_key: prodPkgKey,//新增prod_pkg_key，二次配置时通过这个来匹配临时子件
            parent_bom_id: parentBomId,
            root_id: packageBomId,
            price_book_product_id: priceBookProductId,
            price_book_product_id__r: priceBookProductName,
            price_book_id: priceBookId,
            price_book_id__r: priceBookName,
            product_id__ro: {
                unit: unitId
            },
            node_type: nodeType,
            product_group_id: productGroupIdV || tempNodeGroupId,
            product_group_id__r: productGroupId || tempNodeGroupName,
            node_no: nodeNo,
            related_core_id: bomCoreId,
            new_bom_path: newBomPath,
            node_bom_core_version: bomVersion,
            node_bom_core_type: bomType
        }, parseBomResult)
    }) || [];
}

function subProduct2SubLines(packageLines, subProduct, opt) {
    if (!subProduct) {
        return;
    }
    let {objApiName, parseBomData2DetailData, getDescribe} = opt || {};
    let {
        bom_id, quantity, product_price, discount, sales_price, extra_discount, selling_price, subtotal, product_id, price_book_id, price_book_product_id, price_book_discount,
        prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, mc_currency, mc_exchange_rate, price_editable, amount_editable, node_price, node_discount, node_subtotal, share_rate,
        price_book_price, node_type, system_discount_amount, total_discount_amount, temp_node_group_id, product_group_id, node_no, actual_unit,
        bom_core_id, related_core_id, new_bom_path, amount_any
    } = defFieldMapping.getDetailFields();
    let {
        [bom_id]: pkgBomId, [prod_pkg_key]: pkgProdPkgKey, [quantity]: pkgQuantity, [price_book_discount]: priceBookDiscount,
        [mc_currency]: mcCurrency, [mc_exchange_rate]: mcExchangeRate, record_type, object_describe_api_name
    } = packageLines;
    if (pkgProdPkgKey === undefined || pkgProdPkgKey === null) {
        pkgProdPkgKey = uuid();
    }
    packageLines[prod_pkg_key] = pkgProdPkgKey;
    packageLines[root_prod_pkg_key] = pkgProdPkgKey;
    let {
        _id, name, parent_bom_id,
        modified_adjust_price, modified_adjust_amount,
        product_id: productId, product_id__r: productName,
        price_book_product_id: priceBookProductId, price_book_product_id__r: priceBookProductName,
        price_book_id: priceBookId, price_book_id__r: priceBookName,
        product_group_id: productGroupId, product_group_id__r: productGroupName,
        adjust_price,
        price_editable: priceEditable,
        amount,
        amount_editable: amountEditable,
        discount: discountValue,
        sales_price: salesPriceValue,
        node_price: nodePrice,//标准选配价格
        node_discount: nodeDiscount,//选配折扣
        node_subtotal: nodeSubtotal,//部件小计
        share_rate: shareRate,//分摊比例
        node_type: nodeType,
        prod_pkg_key: subProductProdPkgKey,
        node_no: nodeNo,
        unit_id,
        related_core_id: relatedCoreId,
        new_bom_path: newBomPath,
        amount_any: amountAny,
        amount_origin_subBom
    } = subProduct;
    let parentProdPkgKey = pkgBomId === parent_bom_id ? pkgProdPkgKey : parent_bom_id;
    let shareRateEmpty = shareRate === undefined || shareRate === null;
    let isTempSubProduct = nodeType && nodeType === 'temp';
    let subLines = {
        [discount]: (shareRateEmpty ? '0' : discountValue) || '0',
        [sales_price]: (shareRateEmpty ? '0' : salesPriceValue) || '0',
        [extra_discount]: '0',
        [selling_price]: '0',
        [mc_currency]: mcCurrency,
        [mc_exchange_rate]: mcExchangeRate,
        [quantity]: amount_origin_subBom || amount,
        [product_id]: productId,
        [`${product_id}__r`]: productName,
        [bom_id]: isTempSubProduct ? null : _id,
        [`${bom_id}__r`]: isTempSubProduct ? null : name,
        [prod_pkg_key]: isTempSubProduct ? (subProductProdPkgKey || uuid()) : _id,
        [parent_prod_pkg_key]: parentProdPkgKey,
        [root_prod_pkg_key]: pkgProdPkgKey,
        [`${price_editable}__v`]: priceEditable,
        [`${amount_editable}__v`]: amountEditable,
        [node_price]: isEmpty(nodePrice) ? null : nodePrice,
        [node_discount]: isEmpty(nodeDiscount) ? null : nodeDiscount,
        [node_subtotal]: shareRateEmpty ? null : nodeSubtotal,
        [share_rate]: shareRate,
        [subtotal]: '0',
        [product_price]: modified_adjust_price || adjust_price,
        [price_book_discount]: priceBookDiscount,
        [price_book_price]: null,
        // key_sub_product_original_quantity: quantityResult,
        [node_type]: isTempSubProduct ? 'temp' : 'standard',
        [system_discount_amount]: null,
        [total_discount_amount]: null,
        [`${amount_any}__v`]: amountAny,
        [amount_any]: String(amountAny) === 'true' ? fsapi.i18n.get("ava.object_form.crm.yes") : fsapi.i18n.get("ava.object_form.crm.no"),
        [BomConst.amountOriginSubBom]: amount_origin_subBom || amount,
    };
    if (priceBookProductId && priceBookProductName) {
        subLines[price_book_product_id] = priceBookProductId;
        subLines[`${price_book_product_id}__r`] = priceBookProductName;
    }
    if (priceBookId && priceBookName) {
        subLines[price_book_id] = priceBookId;
        subLines[`${price_book_id}__r`] = priceBookName;
    }
    if (productGroupId && productGroupName) {
        if (isTempSubProduct) {
            subLines[temp_node_group_id] = productGroupId;
            subLines[`${temp_node_group_id}__r`] = productGroupName;
        } else {
            subLines[product_group_id] = productGroupName;
            subLines[`${product_group_id}__v`] = productGroupId;
        }
    }
    if (!isEmpty(nodeNo)) {
        subLines[node_no] = nodeNo;
    }
    if (record_type) {
        subLines.record_type = record_type;
    }
    if (object_describe_api_name) {
        subLines.object_describe_api_name = object_describe_api_name;
    }
    if (!isEmpty(unit_id)) {
        subLines[actual_unit] = unit_id;
    }
    if (!isEmpty(relatedCoreId)) {
        subLines[bom_core_id] = relatedCoreId;
        subLines[related_core_id] = relatedCoreId;
    }
    if (!isEmpty(newBomPath)) {
        subLines[new_bom_path] = newBomPath;
    }
    let parseResult = parseBomData2DetailData && parseBomData2DetailData(objApiName, subProduct);
    let objectDescribe = getDescribe && getDescribe(objApiName);
    let subLinesData = Object.assign(subLines, parseResult);
    formatDataDecimalPlaces(subLinesData, objectDescribe)
    return subLinesData;
}

// 获取最近的上级复用bom产品
function getRecentSubBomData(product, subProductList) {
    let {prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, related_core_id} = defFieldMapping.getDetailFields();

    const parentSubBom = subProductList.find(sub => sub[prod_pkg_key] === product[parent_prod_pkg_key] && sub[root_prod_pkg_key] == product[root_prod_pkg_key]);
    if( isProductPackage(product) ){
        return product;
    } else if ( !parentSubBom ) {
        return null;
    } else if (parentSubBom[related_core_id] || isProductPackage(parentSubBom)) {
        return parentSubBom;
    } else {
        return getRecentSubBomData(parentSubBom, subProductList);
    }
}

// 包或复用bom下的子件数量成倍数
async function handleQuantityAmount(parentProduct, list, options, detailFieldMapping, pluginApi) {
    const { objApiName, dataGetter } = options;
    const {
        prod_pkg_key,
        parent_prod_pkg_key,
        related_core_id,
        quantity,
        product_id,
        amount_any
    } = detailFieldMapping;

    const objectDescribe = dataGetter.getDescribe(objApiName);
    const quantityDecimalPlaces = objectDescribe.fields[quantity].decimal_places || 0;    
    const quantityContext = await requireAsync('../../business-cmpt-paas-sub/business/crm/bom/quantity.js')
    const formatChildRes = quantityContext.formatChildQuantityAmount(parentProduct, list || [], quantityDecimalPlaces, {
        rowId: prod_pkg_key,
        parentRowId: parent_prod_pkg_key,
        relatedCoreId: related_core_id,
        quantity: quantity,
        amountAny: `${amount_any}__v`,
        amountOriginSubBom: 'amount_origin_subBom',
        name: `${product_id}__r`
    }, 'newEditPage');
    const { overstepProducts, modifiedDataList, modifiedDataIndexList } = formatChildRes || {};

    if (overstepProducts && overstepProducts.length) {
        const productStr = overstepProducts.join(',');
        const toastString = i18n('ava.object_form.cpq.amount_auto_round', [productStr]); /** 【{0}】数量的小数位已四舍五入 */
        pluginApi && pluginApi.showToast(toastString);
    }
    return { modifiedDataList, modifiedDataIndexList };
}

export default {
    BomConst,
    isProductPackage,
    isDecimalPlacesInvalid,
    formatValueDecimalPlaces,
    createBomPageParams,
    go2ConfigBomPage,
    subProduct2SubLines,
    getRecentSubBomData,
    handleQuantityAmount
}