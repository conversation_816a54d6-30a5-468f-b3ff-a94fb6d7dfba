import {request} from "../../pluginbase-ava/package/pluginutils";

export default class BomServiceApi {

    constructor(http) {
        this.http = http;
    }

    requestBomDescribe() {
        return request(this.http, {
            url: '/FHH/EM1HNCRM/API/v1/object/BOMObj/controller/DescribeLayout',
            data: {
                include_detail_describe: false,
                include_layout: false,
                apiname: 'BOMObj',
                layout_type: 'add',
                recordType_apiName: 'default__c',
                layoutAgentType: "mobile"
            },
            cacheRule: {type: "app"}
        }).then(result => {
            return result.objectDescribe;
        });
    }

    queryBomPrice(params) {
        return request(this.http, {
            url: 'FHH/EM1HNCRM/API/v1/object/bom/service/query_bom_price',
            data: params
        }).then(result => {
            return result;
        });
    }

    batchQueryBomPrice(params) {
        return request(this.http, {
            url: 'FHH/EM1HNCRM/API/v1/object/bom/service/batch_query_bom_price',
            data: params
        }).then(result => {
            return result;
        });
    }
}