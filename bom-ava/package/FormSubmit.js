import bomUtil from "./bomUtil";
import BomProdPkgKeyGenerator from "./BomProdPkgKeyGenerator";
import bomPriceQuerier from './BomPriceQuerier';
import {each, i18n, isEmpty, requireAsync, uniq} from "../../pluginbase-ava/package/pluginutils";
import bomHookConfig from './hook/BomHookConfig'
import log from "../../pluginbase-ava/package/log";

export class FormSubmit {

    constructor(context) {
        let {fieldMapping, bizStateConfig, pluginApi} = context || {};
        this.context = context;
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
    }

    async formSubmitBefore(pluginExecResult, options) {
        let valid = await this.checkValid(options);
        if (!valid) {
            return {consumed: true}
        }
        if (bomHookConfig.disableQueryBomPrice()) {
            return;
        }
        let {details} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {bom_id, prod_pkg_key, root_prod_pkg_key, parent_prod_pkg_key, node_type} = detailFieldMapping;
        let openPricePolicy = this.bizStateConfig.isOpenPricePolicy();
        let isOpenBomDeleteRoot = this.bizStateConfig.isOpenBomDeleteRoot();
        let detailDataList = details[objApiName];
        detailDataList && detailDataList.length && detailDataList.forEach(it => {
            if (!openPricePolicy) {
                let isPackage = bomUtil.isProductPackage(it);
                if (!isPackage) {
                    let {[parent_prod_pkg_key]: parentProdPkgKey} = it;
                    if (!parentProdPkgKey) {
                        delete it[prod_pkg_key];
                        delete it[root_prod_pkg_key];
                    }
                }
            }
            if (it[node_type] === 'temp') {//删除临时子件中的bom_id
                delete it[bom_id];
                delete it[`${bom_id}__r`];
            }
        });
        if (!isOpenBomDeleteRoot) {
            BomProdPkgKeyGenerator.generatorProdPkgKey(detailDataList, detailFieldMapping);
            await bomPriceQuerier.batchCheckPkgPrice(this.context, options);
        }
    }

    formSubmitPostBefore(pluginExecResult, options) {
        let isOpenCPQ = this.bizStateConfig.isOpenCpq();
        let isOpenSimpleCPQ = this.bizStateConfig.isOpenSimpleCpq();
        let isOpenBomDeleteRoot = this.bizStateConfig.isOpenBomDeleteRoot();
        let check = isOpenBomDeleteRoot ? false : (isOpenCPQ || isOpenSimpleCPQ)
        if (!check) {
            return;
        }
        let data = options && options.postParams && options.postParams.data;
        if (isEmpty(data)) {
            return;
        }
        let detailObjApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {object_data, details} = data || {};
        let detailDataList = details && details[detailObjApiName];
        this.dataCheckError(object_data, detailDataList);
    }

    /**
     * 检查数据是否正确：必填、格式
     * @param options
     * @return {boolean} true:检查通过
     */
    async checkValid(options) {
        let result = await requireAsync('../../objformpkgbase/base/fields/valuechecker');
        let valuechecker = result && result.default || result;
        let checkValidFunc = valuechecker && valuechecker.checkValid;
        if (typeof checkValidFunc !== 'function') {
            return true;
        }
        let {details, dataGetter, dataUpdater} = options;
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let {product_id, parent_prod_pkg_key} = this.fieldMapping.getDetailFields(objApiName);
        let detailDataList = details[objApiName];
        let subLinesDataList = detailDataList && detailDataList.length && detailDataList.filter(it => {
            return !isEmpty(it[parent_prod_pkg_key]);
        });
        if (!subLinesDataList || !subLinesDataList.length) {
            return true;
        }
        let errObj = {};
        let objectDescribe = dataGetter.getDescribe(objApiName);
        let fields = objectDescribe && objectDescribe.fields;
        subLinesDataList.forEach(it => {
            let errors = [];
            let {record_type, dataIndex} = it;
            let layoutFields = dataGetter.getLayoutFields(objApiName, record_type);
            each(layoutFields, (value, key) => {
                let fieldDesc = fields && fields[key];
                if (fieldDesc) {
                    let field = layoutFields[key];
                    let msg = checkValidFunc({objectData: it, field, objectDescribe});
                    if (msg) {
                        errors.push({msg, label: fieldDesc.label, api_name: key})
                    }
                }
            });
            if (errors && errors.length) {
                errObj[dataIndex] = errors;
            }
        });
        if (isEmpty(errObj)) {
            return true;
        }
        let errProductNames = [];
        subLinesDataList.forEach(it => {
            let {[`${product_id}__r`]: productName, dataIndex} = it;
            let errors = errObj[dataIndex];
            if (errors && errors.length) {
                errProductNames.push(productName);
            }
            dataUpdater.updateDetail(objApiName, dataIndex, {
                validateResult: errors
            });
        });
        let productNames = errProductNames.join(',');
        this.pluginApi.showToast(i18n('ava.object_form.cpq.require_field_empty_tip1', [productNames])/*产品{0}有必填字段未填写，请填写*/);
        return false;
    }

    /**
     * 校验数据错误
     * 1、产品包的prod_pkg_key、root_prod_pkg_key不一致
     * 2、子件的根节点不存在
     * 3、产品包、子件的prod_pkg_key重复
     */
    dataCheckError(masterData, detailDataList) {
        if (isEmpty(masterData) || !detailDataList || !detailDataList.length) {
            return;
        }
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let detailFieldMapping = this.fieldMapping.getDetailFields(objApiName);
        let {prod_pkg_key, root_prod_pkg_key, parent_prod_pkg_key} = detailFieldMapping || {};
        let {_id, object_describe_api_name} = masterData;
        let pkgDataList = detailDataList.filter(it => {
            return bomUtil.isProductPackage(it);
        });
        //产品包虚拟key不一致校验
        if (pkgDataList && pkgDataList.length) {
            let notEqual = pkgDataList.some(it => {
                let {[prod_pkg_key]: prodPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = it;
                return prodPkgKey !== rootProdPkgKey;
            });
            if (notEqual) {
                let content = JSON.stringify({
                    _id,
                    object_describe_api_name,
                    error_info: 'pkg virtually key unequal'
                });
                console.log("sfa", 'pkgKeyUnequal', content)
                log.kLog("sfa", 'pkgKeyUnequal', content);
            }
        }
        //子件的根节点不存在校验
        let subProducts = detailDataList.filter(it => {
            return !isEmpty(it[parent_prod_pkg_key]);
        });
        if (subProducts && subProducts.length) {
            let noRootNode = subProducts.some(it => {
                let {[root_prod_pkg_key]: rootProdPkgKey} = it;
                let rootNode = detailDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                return !rootNode;
            })
            if (noRootNode) {
                let content = JSON.stringify({
                    _id,
                    object_describe_api_name,
                    error_info: 'sub product no root node'
                });
                console.log("sfa", 'noRootNode', content)
                log.kLog("sfa", 'noRootNode', content);
            }
        }
        //prod_pkg_key重复
        let bomDataList = [];
        if (pkgDataList && pkgDataList.length) {
            bomDataList.push(...pkgDataList);
        }
        if (subProducts && subProducts.length) {
            bomDataList.push(...subProducts);
        }
        if (bomDataList && bomDataList.length) {
            let prodPkgKeys = bomDataList.map(it => it[prod_pkg_key]) || [];
            let uniqResult = uniq(prodPkgKeys) || [];
            let repeatedProdPkgKey = (prodPkgKeys.length !== uniqResult.length);
            if (repeatedProdPkgKey) {
                let content = JSON.stringify({
                    _id,
                    object_describe_api_name,
                    error_info: 'bom data prod_pkg_key repeat'
                });
                console.log("sfa", 'prodPkgKeyRepeat', content)
                log.kLog("sfa", 'prodPkgKeyRepeat', content);
            }
        }
    }
}