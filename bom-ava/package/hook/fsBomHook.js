import {isEmpty, multiply} from "../../../pluginbase-ava/package/pluginutils";

const unitQuantity = 'field_AX9vN__c';

class FsBomHook {

    //fs客开逻辑：如果修改的是复用bom的数量，子件需要乘以倍数
    hookQuantityChanged(changeField, dataIndex, context, options) {
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        let fieldMapping = context.fieldMapping.getDetailFields(objApiName);
        let {quantity, prod_pkg_key, parent_prod_pkg_key, related_core_id} = fieldMapping;
        let {dataGetter} = options;
        let detailDataList = dataGetter.getDetail(objApiName);
        if (!detailDataList || !detailDataList.length || changeField !== quantity) {
            return;
        }
        let changedData = detailDataList.find(it => it.dataIndex === dataIndex);
        if (!changedData) {
            return;
        }
        let {[prod_pkg_key]: prodPkgKey, [quantity]: quantityValue, [related_core_id]: relatedCoreId} = changedData;
        if (isEmpty(relatedCoreId)) {
            return;
        }
        let children = detailDataList.filter(it => {
            return it[parent_prod_pkg_key] === prodPkgKey;
        });
        this.updateChildQuantity(quantityValue, children, detailDataList, fieldMapping, options);
    }

    updateChildQuantity(parentQuantity, children, detailDataList, fieldMapping, options) {
        let {quantity, max_amount, prod_pkg_key, parent_prod_pkg_key} = fieldMapping;
        let {dataUpdater} = options;
        children && children.length && children.forEach(child => {
            let {[unitQuantity]: childQuantity, [max_amount]: maxAmount, [prod_pkg_key]: prodPkgKey, object_describe_api_name, dataIndex} = child;
            childQuantity = childQuantity || 1;
            let newQuantity = multiply(parentQuantity, childQuantity);
            if (!isEmpty(maxAmount)) {
                if (newQuantity > Number(maxAmount)) {
                    newQuantity = maxAmount;
                }
            }
            dataUpdater.updateDetail(object_describe_api_name, dataIndex, {[quantity]: newQuantity});
            let currentChildren = detailDataList && detailDataList.filter(it => it[parent_prod_pkg_key] === prodPkgKey);
            if (currentChildren && currentChildren.length) {
                this.updateChildQuantity(newQuantity, currentChildren, detailDataList, fieldMapping, options);
            }
        });
    }
}

export default new FsBomHook();