import bomUtil from "../bomUtil";

class BomHookConfig {

    initContext(context) {
        let {pluginApi, fieldMapping} = context;
        this.pluginApi = pluginApi;
        this.fieldMapping = fieldMapping;
        this.bomUseAfterResult = {
            disableQueryBomPrice: false,//是否禁用bom取价逻辑
            configBomBtnText: undefined,//"配置"按钮显示的文案,
            bomInfoRenderType: undefined,//bom组件中bomInfo组件渲染的类型
            hiddenConfigBomBtn(objectData, formContext) {//是否隐藏当前数据的配置按钮
                return false;
            },
            getBomDisplayName(objectData) {//获取bom显示的内容
                return null
            }
        };
    }

    disableQueryBomPrice() {
        return this.bomUseAfterResult.disableQueryBomPrice
    }

    getBomInfoRenderType() {
        return this.bomUseAfterResult.bomInfoRenderType
    }

    getConfigBomBtnText() {
        return this.bomUseAfterResult.configBomBtnText
    }

    hiddenConfigBomBtn(objectData, formContext) {
        if (this.bomUseAfterResult.hiddenConfigBomBtn) {
            return this.bomUseAfterResult.hiddenConfigBomBtn(objectData, formContext);
        }
        return false;
    }

    getBomDisplayName(objectData) {
        if (this.bomUseAfterResult.getBomDisplayName) {
            return this.bomUseAfterResult.getBomDisplayName(objectData);
        }
    }

    pluginServiceUseAfter(pluginExecResult, options) {
        let objApiName = this.fieldMapping.getFirstDetailObjApiName();
        let result = this.pluginApi.runPluginSync('bom.use.after.sync', {objApiName: objApiName});
        Object.assign(this.bomUseAfterResult, result);
    }

    async configBom(formContext, objectData) {
        let result;
        if (formContext && formContext.catchRunPluginHook) {
            result = await formContext.catchRunPluginHook("bom.configBom.before", {objectData});
        } else {
            result = await this.pluginApi.runPlugin('bom.configBom.before', {objectData});
        }
        return Object.assign({
            consumed: false,//为true时，不再继续执行后续逻辑
        }, result);
    }

    async go2ConfigBomPage(formContext, params) {
        let result;
        if (formContext && formContext.catchRunPluginHook) {
            result = await formContext.catchRunPluginHook("bom.go2ConfigBomPage.before", {params});
        } else {
            result = await this.pluginApi.runPlugin('bom.go2ConfigBomPage.before', {params});
        }
        if (result && result.consumed) {
            return result;
        }
        return bomUtil.go2ConfigBomPage(params);
    }
}

export default new BomHookConfig();