Component({

    properties: {
        bomData: {
            type: Object,
            value: {}
        },
    },

    data: {
        renderData: undefined,
    },

    observers: {
        bomData(bomData) {
            let bomDetailData = bomData.bomApis.getDataDetail(bomData.dataIndex);
            let {product_id__r, quantity, product_group_id, product_life_status, product_status} = bomDetailData || {}
            this.setData({
                renderData: {product_id__r, quantity, product_group_id, product_life_status, product_status}
            })
        }
    },

    methods: {
        deleteData() {
            let bomData = this.data.bomData;
            bomData.bomApis.deleteBom(bomData.dataIndex);
        }
    }
});
