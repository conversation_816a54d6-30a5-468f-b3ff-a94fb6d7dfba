<view style="display: flex; flex-direction: row">
    <view style="display: flex; flex-direction: column; flex: 1">
        <text style="font-size: 26rpx;color: #181C25;">{{renderData.product_id__r}}</text>
        <text style="font-size: 26rpx;color: #181C25;">{{renderData.quantity}}</text>
        <text style="font-size: 26rpx;color: #181C25;">{{renderData.product_group_id}}</text>
        <text style="font-size: 26rpx;color: #181C25;">{{renderData.product_life_status}}</text>
        <text style="font-size: 26rpx;color: #181C25;">{{renderData.product_status }}</text>
    </view>
    <view bindtap="deleteData">delete</view>
</view>
