import bom_b from "../bom_b";
import {i18n, isEmpty} from "../../../../pluginbase-ava/package/pluginutils";
import bomHookConfig from '../../hook/BomHookConfig';

Component({
    options: {
        addGlobalClass: true,
    },

    behaviors: [bom_b],

    data: {
        dBomInfoRenderType: undefined,
        dShowError: false,
        dErrorInfo: undefined,
    },

    methods: {

        afterInitData(bomData) {
            let {validateResult} = bomData || {};
            let renderType = bomHookConfig.getBomInfoRenderType();
            return Object.assign({
                dShowError: !!(validateResult && validateResult.length)
            }, !(isEmpty(renderType)) && {
                dBomInfoRenderType: renderType
            })
        },

        editBom() {
            let bomData = this.data.bomData;
            bomData.bomApis.editBom(bomData.dataIndex);
        },

        deleteBom() {
            let bomData = this.data.bomData;
            bomData.bomApis.deleteBom(bomData.dataIndex);
        }
    },

    lifetimes: {
        attached() {
            this.setData({
                dErrorInfo: i18n('ava.object_form.cpq.require_field_empty_tip')//该产品有字段为必填，请填写后提交
            })
        }
    }
});
