import defFieldMapping from '../../../defFieldMapping';

Component({
  properties: {
    bomData: {
      type: Object,
      value: null,
    },
  },

  observers: {
    /*
      bomData 拿到的数据格式：
      {
        dataIndex, // 当前这一条明细行的索引
        expand: true/false, // 是否展开
        bomApis: {
          deleteBom(),
          editBom(),
          getDataDetail(),
        }
      }

      如果想展示 quantity 这个映射字段的话，需要在这里手动做映射。
      */
    bomData(val) {
      console.log('stock_bom bomData', val);
      if (val) {
        let bomDetailData = val.bomApis.getDataDetail(val.dataIndex);
        console.log('stock_bom bomDetailData');
        let { quantity } = defFieldMapping.getDetailFields();
        this.setData({
          quantity: bomDetailData.delivery_num,
          bomDetailData,
        });
      }
    },
  },
  data: {
    quantity: undefined,
    // 删除按钮 cdn 图片资源
    deleteBtnImg: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/delete-icon.svg',
    bomDetailData: {},
  },

  methods: {
    editBom(e) {
      console.log('stock_bom editBom');
      let bomData = this.data.bomData;
      bomData.bomApis.editBom(bomData.dataIndex);

    },
    deleteBom() {
      console.log('stock_bom deleteData');
      let bomData = this.data.bomData;
      bomData.bomApis.deleteBom(bomData.dataIndex);
    },
  },
});
