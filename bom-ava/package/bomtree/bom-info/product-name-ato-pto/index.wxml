<view class="bom-data-wrap">
  <view class="field-name-wrap" data-bomDetailData="{{bomDetailData}}" bindtap="editBom">
    <!-- 产品名称字段 -->
    <view class="field-name product-name">{{bomDetailData.product_id__r}}</view>
    <block wx:if="{{bomDetailData.virtual_ato_pto_tag == 'pto_child'}}">
      <!-- 订单产品数量 -->
      <view class="field-name" wx:if="{{bomDetailData.order_product_amount}}">
        订单产品数量：{{bomDetailData.order_product_amount}}
      </view>
      <!-- 批次字段 -->
      <view class="field-name" wx:if="{{bomDetailData.batch_sn__v == 2}}">
        批次：{{bomDetailData.batch_id__r || '未选择'}}
      </view>
      <!-- 序列号字段 -->
      <view class="field-name" wx:if="{{bomDetailData.batch_sn__v == 3}}">
        序列号：{{bomDetailData.serial_number_id__r || '未选择'}}
      </view>
    </block>
  </view>
  <!-- 发货单：本次发货数； -->
  <view class="field-name quantity" wx:if="{{bomDetailData.virtual_ato_pto_tag == 'pto_child'}}">
    x{{quantity}}
  </view>
  <!-- PTO子节点，展示删除按钮；如果没开ATO&PTO开关的话，BOM产品也展示删除按钮 -->
  <image class="delete-btn" src="{{deleteBtnImg}}" bindtap="deleteBom" wx:if="{{bomDetailData.virtual_ato_pto_tag == 'pto_child' || !bomDetailData.virtual_ato_pto_tag}}"></image>
</view>