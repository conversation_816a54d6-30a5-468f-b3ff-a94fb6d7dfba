<view style="display: flex; flex-direction: column;">
    <view class="name-container" style="margin-left: {{level*20}}rpx;">
        <!-- demo（勿动） -->
        <product-name-test wx:if="{{dBomInfoRenderType==='test'}}" bomData="{{bomData}}"></product-name-test>
        <!-- 发货单&退货单：ATO&PTO产品子件的样式定制 -->
        <product-name-ato-pto wx:elif="{{dBomInfoRenderType==='ATO_PTO'}}" bomData="{{bomData}}"></product-name-ato-pto>
        <!-- 默认子件 -->
        <product-name wx:else bomData="{{bomData}}" bindexpand="expand"></product-name>
        <text wx:if="{{dShowError}}" class="error-text" bindtap="editBom">{{dErrorInfo}}</text>
    </view>
    <view style="height: 0.5px; background: #dee1e8"></view>
    <view wx:if="{{bomData.key_child_bom_list&&bomData.key_child_bom_list.length}}" hidden="{{!bomData.expand}}">
        <block wx:for="{{bomData.key_child_bom_list}}">
            <bom-group wx:if="{{item.is_product_group}}" bomData="{{item}}" level="{{level+1}}"></bom-group>
            <bom-info wx:else bomData="{{item}}" level="{{level+1}}"></bom-info>
        </block>
    </view>
</view>
