import bomPicker from "../../bomPicker";
import defFieldMapping from "../../../defFieldMapping";
import bomHookConfig from "../../../hook/BomHookConfig";
import {isEmpty} from "../../../../../pluginbase-ava/package/pluginutils";


Component({
    options: {
        addGlobalClass: true,
    },

    properties: {
        bomData: {
            type: Object,
            value: null
        },
    },

    observers: {
        bomData(val) {
            if (val) {
                let bomDetailData = val.bomApis.getDataDetail(val.dataIndex);
                let expand = bomPicker.isExpand(bomDetailData);
                let {product_id, quantity} = defFieldMapping.getDetailFields();
                let {[`${product_id}__r`]: productName, [quantity]: quantityValue} = bomDetailData || {};
                let displayName = bomHookConfig.getBomDisplayName(bomDetailData);
                if (isEmpty(displayName)) {
                    displayName = productName;
                }
                this.setData(Object.assign({'bomData.expand': expand}, {
                    productName: displayName,
                    quantity: quantityValue
                }));
            }
        }
    },

    data: {
        productName: undefined,
        quantity: undefined,
    },

    methods: {

        expand() {
            this.triggerEvent('expand', {});
        },

        editBom(e) {
            let bomData = this.data.bomData;
            bomData.bomApis.editBom(bomData.dataIndex);
        }
    },
});
