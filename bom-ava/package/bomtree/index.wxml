<view wx:if="{{dCascadeBom&&dCascadeBom.length}}" style="display: flex; flex-direction: column">
    <view class="bom-container">
        <block wx:for="{{dCascadeBom}}">
            <bom-group wx:if="{{item.is_product_group}}" bomData="{{item}}" level="0"></bom-group>
            <bom-info wx:else bomData="{{item}}" level="0" ></bom-info>
        </block>
    </view>
    <text wx:if="{{dShowViewAllAction}}" style="font-size: 28rpx; color: #91959e; text-align: center; margin-top: 20rpx" bindtap="viewAllSubLines">{{dViewAllSubProducts}}</text>
</view>