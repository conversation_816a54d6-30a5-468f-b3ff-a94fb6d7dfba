import bomUtil from "../bomUtil";
import defFieldMapping from "../defFieldMapping";
import {cloneDeep, i18n, isEmpty, uuid} from "../../../pluginbase-ava/package/pluginutils";
import LoadingManager from "ava-ui/fxui/loading-manager";
import perfTick from "../../../objformmain/libs/perfTick";

const childBomList = 'key_child_bom_list';
const groupBomList = "key_group_bom_list";
const RENDER_COUNT = 50;//渲染条数

Component({

    properties: {

        refresh: {
            type: Object,
            value: null
        }
    },

    observers: {

        'refresh': function () {
            this.triggerEvent("getBomData", rst => {
                let showViewAllAction = this.data.dShowViewAllAction;
                this.refreshUI(rst, showViewAllAction);
            })
        }
    },

    data: {
        dCascadeBom: undefined,//页面展示的Bom级联数据
        dShowViewAllAction: undefined,//是否显示查看全部子产品按钮
        dViewAllSubProducts: undefined,//查看全部子产品文案
    },

    methods: {

        refreshUI(bomData, showViewAllAction, callback) {
            this.bomData = bomData;
            let {objectData/*产品包数据*/, subLinesList/*拍平的子XX明细数据*/} = bomData || {};
            if (!isEmpty(objectData) && subLinesList && subLinesList.length) {
                let cascadeBom = this.buildCascadeBom(objectData, cloneDeep(subLinesList));
                this.simplifyCascadeBom(cascadeBom);
                if (isEmpty(showViewAllAction)) {
                    showViewAllAction = cascadeBom && cascadeBom.length > RENDER_COUNT;
                }
                let updateCascadeBom = showViewAllAction ? cascadeBom.slice(0, RENDER_COUNT) : cascadeBom;
                let ticker = perfTick.startTicker({operationId: 'renderCascadeBomTree'});
                this.setData({
                    dCascadeBom: updateCascadeBom,
                    dShowViewAllAction: showViewAllAction
                }, () => {
                    let formContext = bomData && bomData.formContext;
                    let pageId = formContext && formContext.getPageId && formContext.getPageId();
                    let apiName = formContext && formContext.getMasterApiName && formContext.getMasterApiName();
                    let count = showViewAllAction ? RENDER_COUNT : subLinesList.length;
                    ticker.end({
                        pageId,
                        apiName,
                        biz: "ava_object_form",
                        module: 'sfa',
                        subModule: "bom",
                        subLinesCount: count
                    });
                    callback && callback();
                });
            } else {
                this.setData({
                    dCascadeBom: null,
                    dShowViewAllAction: false
                }, callback);
            }
        },

        buildCascadeBom(packageData, subLinesList) {
            if (!subLinesList || !subLinesList.length) {
                return;
            }
            let {prod_pkg_key, parent_prod_pkg_key} = defFieldMapping.getDetailFields();
            let tempSubLinesList = [...subLinesList];
            let {dataIndex, [prod_pkg_key]: rootProdPkgKey} = packageData;
            let bomListObj = {};
            let i = tempSubLinesList.length;
            while (i--) {
                let subLines = tempSubLinesList[i];
                let parentProdPkgKey = subLines[parent_prod_pkg_key];
                if (parentProdPkgKey !== rootProdPkgKey) {
                    let bomList = bomListObj[parentProdPkgKey];
                    if (!bomList) {
                        bomList = [];
                        bomListObj[parentProdPkgKey] = bomList;
                    }
                    bomList.push(subLines);
                    tempSubLinesList.splice(i, 1);
                }
            }
            //由于是倒序放入的数据，结束后再将数据反转下，保证顺序正确
            Object.keys(bomListObj).forEach(key => {
                let bomList = bomListObj[key];
                if (bomList && bomList.length) {
                    bomListObj[key] = bomList.reverse();
                }
            });
            this.fillChildBomList(tempSubLinesList, bomListObj);
            this.buildBomGroupData(tempSubLinesList, dataIndex);
            return tempSubLinesList;
        },

        fillChildBomList(subLinesList, bomListObj) {
            if (subLinesList && subLinesList.length) {
                let {prod_pkg_key} = defFieldMapping.getDetailFields();
                subLinesList.forEach(subLines => {
                    let bomId = subLines[prod_pkg_key];
                    let bomList = bomListObj[bomId];
                    if (bomList && bomList.length) {
                        subLines[childBomList] = bomList;
                        this.fillChildBomList(bomList, bomListObj);
                    } else {
                        delete subLines[childBomList];
                    }
                })
            }
        },

        buildBomGroupData(subLinesList, dataIndex) {
            if (!subLinesList || !subLinesList.length) {
                return;
            }
            let {product_group_id, temp_node_group_id, node_type} = defFieldMapping.getDetailFields() || {};
            let usedGroupIds = [];
            let groupMap = {};
            let i = subLinesList.length;
            while (i--) {
                let subLines = subLinesList[i];
                const { [node_type]: nodeType } = subLines;
                // 兼容临时子件关于分组的处理
                const group_id = nodeType === 'temp' ? temp_node_group_id : `${product_group_id}__v`;
                const group_id_name = nodeType === 'temp' ? `${temp_node_group_id}__r` : product_group_id;

                const { [group_id]: productGroupId, [group_id_name]: productGroupName } = subLines;

                if (productGroupId === undefined || productGroupId === null) {
                    subLines[bomUtil.BomConst.isProductGroup] = false;
                    subLines[bomUtil.BomConst.productPackageDataIndex] = dataIndex;
                } else {
                    let listInGroup = groupMap[productGroupId];
                    if (!listInGroup) {
                        listInGroup = [];
                        groupMap[productGroupId] = listInGroup;
                    }
                    listInGroup.push(subLines);
                    let used = usedGroupIds.includes(productGroupId);
                    if (!used) {
                        let group = {
                            _id: productGroupId,
                            name: productGroupName
                        };
                        group[bomUtil.BomConst.isProductGroup] = true;
                        group[bomUtil.BomConst.productPackageDataIndex] = dataIndex;
                        usedGroupIds.push(productGroupId);
                        subLinesList.splice(i, 1, group);
                    } else {
                        subLinesList.splice(i, 1);
                    }
                }
            }
            //由于是倒序放入的数据，结束后再将数据反转下，保证顺序正确
            Object.keys(groupMap).forEach(key => {
                let bomList = groupMap[key];
                if (bomList && bomList.length) {
                    groupMap[key] = bomList.reverse();
                }
            });
            subLinesList.forEach(subLines => {
                let isGroup = subLines[bomUtil.BomConst.isProductGroup];
                if (isGroup) {
                    let groupId = subLines._id;
                    let listInGroup = groupMap[groupId];
                    subLines[groupBomList] = listInGroup;
                    if (listInGroup && listInGroup.length) {
                        listInGroup.forEach(group => {
                            let _childBomList = group[childBomList];
                            this.buildBomGroupData(_childBomList, dataIndex);
                            group[childBomList] = _childBomList;
                        })
                    }
                } else {
                    let _childBomList = subLines[childBomList];
                    this.buildBomGroupData(_childBomList, dataIndex);
                    subLines[childBomList] = _childBomList;
                }
            })
        },

        simplifyCascadeBom(cascadeBom) {
            if (cascadeBom && cascadeBom.length) {
                cascadeBom.forEach(bom => {
                    let isGroup = bom[bomUtil.BomConst.isProductGroup];
                    let child = bom[isGroup ? groupBomList : childBomList];
                    if (!isGroup) {
                        this.simplifyAndFormatBom(bom);
                    }
                    this.simplifyCascadeBom(child);
                })
            }
        },

        simplifyAndFormatBom(bomData) {
            if (isEmpty(bomData)) {
                return;
            }
            let retainedFields = ['dataIndex', childBomList];//只保留dataIndex和子件
            Object.keys(bomData).forEach(it => {
                if (!retainedFields.includes(it)) {
                    delete bomData[it]
                }
            });
            if (typeof bomData[childBomList] === 'undefined') {
                delete bomData[childBomList]
            }
            let self = this;
            bomData.bomApis = {
                getDataDetail(dataIndex) {
                    let {subLinesList} = self.bomData || {};
                    return subLinesList && subLinesList.length && subLinesList.find(it => it.dataIndex === dataIndex) || {};
                },

                editBom(dataIndex) {
                    self.triggerEvent('triggerEditBom', {dataIndex});
                },

                deleteBom(dataIndex) {
                    self.triggerEvent('triggerDeleteBom', {dataIndex});
                }
            }
        },

        viewAllSubLines() {
            let formContext = this.bomData && this.bomData.formContext;
            let pageId = formContext && formContext.getPageId && formContext.getPageId();
            let token = 'bom_' + uuid();
            LoadingManager().showLoading(token, {title: i18n("cml.crm.loading")/*加载中*/}, pageId);
            this.refreshUI(this.bomData, false, () => {
                LoadingManager().hideLoading(token, pageId);
            })
        }
    },

    lifetimes: {
        attached() {
            this.setData({
                dViewAllSubProducts: i18n('ava.object_form.onsale.bom.view_all_sub_products')//查看全部子产品
            })
        }
    }
});