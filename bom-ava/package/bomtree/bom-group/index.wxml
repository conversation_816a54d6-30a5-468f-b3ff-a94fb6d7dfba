<view style="display: flex; flex-direction: column;">
    <view class="name-container" style="margin-left: {{level*20}}rpx;">
        <view wx:if="{{bomData.key_group_bom_list&&bomData.key_group_bom_list.length}}"
              class="fxui_all expand-img {{bomData.expand?'fenleizhankai':'fenleishouqi'}}"
              catchtap="expand">
        </view>
        <view class="group-tag">{{groupText}}</view>
        <text class="name" style="font-weight: 700">{{bomData.name}}</text>
    </view>
    <view style="height: 0.5px; background: #dee1e8"></view>
    <view wx:if="{{bomData.key_group_bom_list&&bomData.key_group_bom_list.length}}" hidden="{{!bomData.expand}}">
        <block wx:for="{{bomData.key_group_bom_list}}">
            <bom-group wx:if="{{item.is_product_group}}" bomData="{{item}}" level="{{level+1}}"></bom-group>
            <bom-info wx:else bomData="{{item}}" level="{{level+1}}"></bom-info>
        </block>
    </view>
</view>