import bomUtil from "../bomUtil";
import {isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import fsapi from "fs-hera-api";
import defFieldMapping from "../defFieldMapping";

let key_config_bom_expand_value = "key_config_bom_expand_value";//是否展开多级bom结构
const picker = {};

function expand(expand, bomData) {
    let unique = uniqueValueByData(bomData);
    picker[unique] = expand;
}

function expandAll(expand, dataList) {
    if (!dataList || !dataList.length) {
        return;
    }
    let newList = [];
    let subProductsObj = {};
    let {
        bom_id, root_prod_pkg_key, parent_prod_pkg_key, prod_pkg_key, product_group_id
    } = defFieldMapping.getDetailFields();
    dataList.forEach(objectData => {
        let {record_type, [root_prod_pkg_key]: rootProdKey, [parent_prod_pkg_key]: parentProdKey} = objectData;
        if (!isEmpty(parentProdKey)) {//子产品
            let key = `${record_type}-${rootProdKey}`;
            let subProducts = subProductsObj[key];
            if (!subProducts) {
                subProducts = [];
                subProductsObj[key] = subProducts;
            }
            subProducts.push(objectData);
        } else {
            newList.push(objectData);
        }
    });
    newList.forEach(objectData => {
        let {record_type, [prod_pkg_key]: prodPkgKey, dataIndex, [bom_id]: bomId} = objectData;
        let uni = uniqueValue(dataIndex, bomId);
        picker[uni] = expand;
        if (!isEmpty(prodPkgKey)) {
            let key = `${record_type}-${prodPkgKey}`;
            let subProducts = subProductsObj[key];
            subProducts && subProducts.length && subProducts.forEach(subProduct => {
                let {[bom_id]: bomId, [`${product_group_id}__v`]: productGroupId} = subProduct;
                let uni = uniqueValue(dataIndex, bomId);//子产品
                picker[uni] = expand;
                if (productGroupId) {
                    let uni = uniqueValue(dataIndex, productGroupId);//分组
                    picker[uni] = expand;
                }
            });
        }
    });
}

function isExpand(bomData) {
    let unique = uniqueValueByData(bomData);
    let expand = picker[unique];
    if (isEmpty(expand)) {
        return isBomExpand();
    }
    return expand;
}

function uniqueValueByData(bomData) {
    let isGroup = bomData[bomUtil.BomConst.isProductGroup];
    let packageDataIndex = bomData[bomUtil.BomConst.productPackageDataIndex];
    let {bom_id} = defFieldMapping.getDetailFields();
    let {[bom_id]: bomId, _id} = bomData;
    let value = isGroup ? _id : bomId;
    return uniqueValue(packageDataIndex, value)
}

function uniqueValue(packageDataIndex, value) {
    return `${packageDataIndex}_${value}`;
}

function setBomExpand(expand) {
    let key = fsapi.config.getAccountKey(key_config_bom_expand_value);
    wx.setStorageSync(key, expand ? 'true' : 'false');
}

function isBomExpand() {
    let expandKey = fsapi.config.getAccountKey(key_config_bom_expand_value);
    let expand = wx.getStorageSync(expandKey);
    if (isEmpty(expand)) {
        return true;//未设置时默认展开
    }
    return expand === true || expand === 'true';
}

export default {
    expand,
    expandAll,
    isExpand,
    setBomExpand,
    isBomExpand
}