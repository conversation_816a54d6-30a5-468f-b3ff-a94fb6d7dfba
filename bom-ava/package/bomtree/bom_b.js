import bomPicker from "./bomPicker";
import bomUtil from "../bomUtil";

export default Behavior({
    properties: {
        bomData: {
            type: Object,
            value: null
        },

        level: {
            type: Number,
            value: 0
        }
    },

    observers: {

        bomData(val) {
            let bomDetailData = this.getBomDetailData(val);
            let expand = bomPicker.isExpand(bomDetailData);
            let updateData = this.afterInitData(bomDetailData);
            this.setData(Object.assign({'bomData.expand': expand}, updateData));
        }
    },

    methods: {

        afterInitData(bomData) {
            return {}
        },

        expand() {
            let bomData = this.data.bomData;
            let bomDetailData = this.getBomDetailData(bomData);
            let expand = bomPicker.isExpand(bomDetailData);
            let _expand = !expand;
            bomPicker.expand(_expand, bomDetailData);
            this.setData({'bomData.expand': _expand});
        },

        getBomDetailData(bomData) {
            let isGroup = bomData[bomUtil.BomConst.isProductGroup];
            if (isGroup) {
                return bomData;
            } else {
                let dataIndex = bomData.dataIndex;
                return bomData.bomApis.getDataDetail(dataIndex);
            }
        }
    }
})