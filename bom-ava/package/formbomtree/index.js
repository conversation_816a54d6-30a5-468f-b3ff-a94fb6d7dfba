import bomUtil from "../bomUtil";
import dialogset from "../../../objformmain/dialogset/index";
import {convertOneFieldData} from "ava-metadata-lib/formatDefObj";
import {each, i18n, isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import defFieldMapping from "../defFieldMapping";
import {emitEvent} from "../events";
import bomHookConfig from '../hook/BomHookConfig';
import perfTick from "../../../objformmain/libs/perfTick";

Component({

    properties: {

        refresh: {
            type: Object,
            value: null
        }
    },

    observers: {
        refresh() {
            this.triggerEvent("getDataDetail", rst => {
                this._updateComponentData(rst);
            })
        }
    },

    data: {
        dEnableConfigBom: false,
        dBomConfigText: undefined,
        dShowBomTree: false,
        dRefreshUI: undefined,

        itemInfo: undefined,
        formContext: undefined,
    },

    methods: {

        _updateComponentData(rst) {
            let {objectData, formContext} = rst || {};
            let configBomBtnText = bomHookConfig.getConfigBomBtnText();
            this.data.itemInfo = rst;
            this.data.formContext = rst.formContext;
            let isPkg = bomUtil.isProductPackage(objectData);
            if (isPkg) {
                let pageOptions = formContext && formContext.getPageOptions && formContext.getPageOptions();
                let entrySource = pageOptions && pageOptions.entrySource;
                let canConfigBom = !(['BpmEditForm', 'ApproveEditForm', 'StageEditForm'].includes(entrySource));
                let _enableConfigBom = this.enableConfigBom(objectData);
                let {prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
                let {[prod_pkg_key]: prodPkgKey, object_describe_api_name} = objectData;
                let objectDataList = formContext && formContext.getDetailData(object_describe_api_name);
                let subLinesList = objectDataList && objectDataList.filter(it => it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key]);
                let hiddenConfigBtn = bomHookConfig.hiddenConfigBomBtn(objectData, {
                    getMasterData() {
                        return formContext.getMasterData();
                    }
                });
                let showBomTree = subLinesList && subLinesList.length;
                let ticker = perfTick.startTicker({operationId: 'renderBomTree'});
                this.setData(Object.assign({
                    dEnableConfigBom: hiddenConfigBtn ? false : (canConfigBom && _enableConfigBom),
                    dShowBomTree: showBomTree,
                    dRefreshUI: {},
                    dBomConfigText: i18n("ava.object_form.onsale.bom_config"/*配置*/)
                }, !(isEmpty(configBomBtnText)) && {
                    dBomConfigText: configBomBtnText
                }), () => {
                    let pageId = formContext.getPageId();
                    let apiName = formContext.getMasterApiName();
                    ticker.end({pageId, apiName, biz: "ava_object_form", module: 'sfa', subModule: "bom"});
                });
            }
        },

        async configBom(e) {
            let {formContext, itemInfo} = this.data;
            let {objectData: productPkgData} = itemInfo || {};
            let result = await bomHookConfig.configBom(formContext, productPkgData);
            if (result && result.consumed) {
                return;
            }
            if (formContext) {
                let masterData = formContext.getMasterData();
                let {dataIndex, object_describe_api_name, record_type} = productPkgData;
                let detailDataList = formContext.getDetailData(object_describe_api_name);
                let layoutFields = formContext.getDetailLayoutFields(object_describe_api_name, record_type);
                let {product_price, quantity} = defFieldMapping.getDetailFields();
                let priceField = layoutFields && layoutFields[product_price];
                let bizStateConfig = formContext.catchRunPluginHookSync('bom.getBizStateConfig', {});
                let params = bomUtil.createBomPageParams(masterData, productPkgData, detailDataList, {
                    isShowPackagePrice: !isEmpty(priceField),
                    bizStateConfig,
                    parseDetailData2BomData: this.parseDetailData2BomData.bind(this)
                });
                bomHookConfig.go2ConfigBomPage(formContext, params)
                    .then(async configBomResult => {
                        await formContext.catchRunPluginHook("bom.reconfiguration.queryBomPrice.before", {
                            objApiName: object_describe_api_name,
                            dataIndex
                        });
                        return formContext.catchRunPluginHook("bom.reconfiguration.after", {
                            objApiName: object_describe_api_name,
                            dataIndex,
                            configBomResult
                        });
                    })
                    .then(async (bomChangedInfo) => {
                        //触发价格、数量字段变更的UI事件、新增明细的UI事件
                        // let masterInfo = formContext.getMasterInfo();
                        // let masterLayout = masterInfo && masterInfo.layout;
                        // let events = masterLayout && masterLayout.events;
                        // let fieldUIEvent = {};
                        // let addNewEvent;
                        // each(events, (event) => {
                        //     let {trigger_field_api_names, trigger_describe_api_name, triggers} = event;
                        //     if (trigger_describe_api_name === object_describe_api_name) {
                        //         if (triggers[0] == 1 || triggers[0] == 3) {
                        //             each(trigger_field_api_names, (apiname) => {
                        //                 fieldUIEvent[apiname] = event
                        //             })
                        //         } else if (triggers[0] == 2) {
                        //             addNewEvent = event;
                        //         }
                        //     }
                        // });
                        // let priceUiEventId = fieldUIEvent[product_price] && fieldUIEvent[product_price]._id;
                        // if (priceUiEventId) {
                        //     await formContext.triggerUIEvent(priceUiEventId);
                        // }
                        // let quantityUiEventId = fieldUIEvent[quantity] && fieldUIEvent[quantity]._id;
                        // let quantityTrigger = quantityUiEventId && (quantityUiEventId !== priceUiEventId);
                        // if (quantityTrigger) {
                        //     await formContext.triggerUIEvent(quantityUiEventId);
                        // }
                        // let addNewEventId = addNewEvent && addNewEvent._id;
                        // let {addNewDataIndexList} = bomChangedInfo || {};
                        // if (addNewEventId && addNewDataIndexList && addNewDataIndexList.length) {
                        //     await formContext.triggerUIEvent(addNewEventId);
                        // }
                        return bomChangedInfo;
                    })
                    .then(bomChangedInfo => {
                        return formContext.catchRunPluginHook("bom.reconfiguration.end", {
                            objApiName: object_describe_api_name,
                            dataIndex,
                            bomChangedInfo
                        });
                    });
            }
        },

        triggerEditBom(e) {
            let dataIndex = e.detail.dataIndex;
            this.showFormPopup(dataIndex, true);
        },

        async triggerDeleteBom(e) {
            let dataIndex = e.detail.dataIndex;
            let {objectData: productPkgData} = this.data.itemInfo || {};
            let detailDataList = this.data.formContext.getDetailData(productPkgData.object_describe_api_name);
            let objectData = detailDataList && detailDataList.length && detailDataList.find(it => {
                return it.dataIndex === dataIndex;
            });
            if (objectData) {
                let delDatas = [objectData];
                let {object_describe_api_name, recordType, record_type} = objectData;
                await this.data.formContext.catchRunPluginHook("bom.deleteBom.before", {
                    objApiName: object_describe_api_name,
                    recordType: recordType || record_type,
                    delDatas
                });
                if (delDatas && delDatas.length) {
                    await this.data.formContext.catchRunPluginHook("bom.deleteBom.after", {
                        objApiName: object_describe_api_name,
                        recordType: recordType || record_type,
                        delDatas
                    });
                }
            }
        },

        showFormPopup(dataIndex, triggerRule) {
            let {objectData: productPkgData} = this.data.itemInfo || {};
            let detailDataList = this.data.formContext.getDetailData(productPkgData.object_describe_api_name);
            let objectData = detailDataList && detailDataList.length && detailDataList.find(it => {
                return it.dataIndex === dataIndex;
            });
            if (!objectData) {
                return;
            }
            let {validateResult} = objectData;
            let focusField = validateResult && validateResult.length && validateResult[0] && validateResult[0].api_name;
            let allErrorMsgs = this.data.itemInfo.getAllErrorMsgs();
            let errorMsgs = allErrorMsgs[objectData.dataIndex];
            let formAttr = this.data.itemInfo.getFormAttr(objectData, errorMsgs);
            let self = this;
            dialogset.formpopup.show({
                objectDataList: detailDataList,
                getAllErrorMsgs: () => {
                    return this.data.itemInfo.getAllErrorMsgs();
                },
                triggerRule,
                focusField,
                formAttr,
                onClose() {
                    objectData.validateResult = self.checkValid(objectData);
                    self.setData({//刷新bom组件
                        refresh: {}
                    });
                },
                getTitle(objData) {
                    return self._getTitle(objData)
                },
                getTitlePlaceHolder(objData) {
                    let field = self._getTitleField(objData)
                    return self._getTitleFieldLabel(field)
                },
            })
        },

        enableConfigBom(objectData) {
            let isPackage = bomUtil.isProductPackage(objectData);
            let {product_life_status, product_status, is_saleable} = defFieldMapping.getDetailFields();
            let {[`${product_life_status}__v`]: product_life_status__v, [`${product_status}__v`]: product_status__v, [`${is_saleable}__v`]: is_saleable__v} = objectData || {};
            if ((product_life_status__v === undefined || product_life_status__v === null)
                && (product_status__v === undefined || product_status__v === null)
                && (is_saleable__v === undefined || is_saleable__v === null)) {
                return isPackage;
            } else {
                return isPackage && product_life_status__v === 'normal' && product_status__v === '1' && is_saleable__v;
            }
        },

        parseDetailData2BomData(detailData) {
            return this.data.formContext.catchRunPluginHookSync(emitEvent.bom_parseDetailData2BomData_sync, {objectData: detailData});
        },

        checkValid(objectData) {
            let formContext = this.data.formContext;
            if (isEmpty(objectData) || !formContext) {
                return;
            }
            let valuechecker = formContext.apis && formContext.apis.valuechecker && formContext.apis.valuechecker;
            let checkValidFunc = valuechecker && valuechecker.checkValid;
            if (typeof checkValidFunc !== 'function') {
                return;
            }
            let {record_type, object_describe_api_name} = objectData;
            let layoutFields = formContext.getDetailLayoutFields(object_describe_api_name, record_type);
            let objectDescribe = formContext.getDetailDescribe(object_describe_api_name);
            let errors = [];
            each(layoutFields, (value, key) => {
                let fieldDesc = objectDescribe.fields[key];
                if (fieldDesc) {
                    let field = layoutFields[key];
                    let msg = checkValidFunc({objectData, field, objectDescribe});
                    if (msg) {
                        errors.push({msg, label: fieldDesc.label, api_name: key})
                    }
                }
            });
            return errors;
        },

        _getTitleField() {
            let {objectDescribe, title_field, layoutFields} = this.data.itemInfo;
            let titleFieldApiName = layoutFields[title_field] ? title_field : "name";
            return objectDescribe.fields[titleFieldApiName];
        },

        _getTitle(objectData) {
            let field = this._getTitleField(objectData);
            if (!field) {
                return ''
            }
            let {layoutFields, objectDescribe} = this.data.itemInfo;
            if (layoutFields[field.api_name]) {
                field = layoutFields[field.api_name]
            }
            let fieldDesc = objectDescribe.fields[field.api_name];
            return convertOneFieldData(objectData, fieldDesc);
        },

        _getTitleFieldLabel(field) {
            if (field) {
                let {layoutFields} = this.data.itemInfo;
                return (layoutFields[field.api_name] && layoutFields[field.api_name].label) || field.label
            }
        },

        getBomData(e) {
            let {itemInfo, formContext} = this.data;
            let {objectData} = itemInfo || {};
            let {prod_pkg_key, root_prod_pkg_key} = defFieldMapping.getDetailFields();
            let {[prod_pkg_key]: prodPkgKey, object_describe_api_name} = objectData;
            let objectDataList = formContext && formContext.getDetailData(object_describe_api_name);
            let subLinesList = objectDataList && objectDataList.filter(it => it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key]);
            e.detail({objectData, subLinesList, formContext});
        }
    }
});