<view wx:if="{{dEnableConfigBom || dShowBomTree}}" style="display: flex; flex-direction: column; padding: 12rpx 24rpx 12rpx 16rpx">
    <view wx:if="{{dEnableConfigBom}}"
          style="display: flex; flex-direction: row; margin-bottom: 20rpx">
        <view style="flex: 1"></view>
        <text class="bom-config" bindtap="configBom">{{dBomConfigText}}</text>
    </view>
    <bom-tree wx:if="{{dShowBomTree}}" style="margin-bottom: 20rpx"
              refresh="{{dRefreshUI}}"
              bindtriggerEditBom="triggerEditBom"
              bindtriggerDeleteBom="triggerDeleteBom"
              bindgetBomData="getBomData"></bom-tree>
</view>