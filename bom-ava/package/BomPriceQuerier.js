/**
 * bom取价逻辑
 * 触发bom取价条件：新增明细、修改母件字段，修改子件字段，二次配置母件，匹配价格政策后，复制映射草稿。
 * bom取价执行顺序：调用取价接口、更新母件、更新子件、计算母件、计算子件、计算描述级别的字段。
 * */
import {
    divide,
    each,
    equals,
    i18n,
    isArray,
    isEmpty,
    multiply,
    simplifyDetails,
    uniq,
    uuid
} from "../../pluginbase-ava/package/pluginutils";
import bomUtil from "./bomUtil";
import {getReadonlyFields, timeAnalysis} from "./utils/formutils";
import {emitEvent} from "./events";
import log from '../../pluginbase-ava/package/log'
import perfTick from "../../objformmain/libs/perfTick";
import fieldChangedRecorder from "./utils/fieldChangedRecorder";

const CALC_STEP_COUNT = 5;//一次计算5个包

export const TriggerType = Object.freeze({
    ADD: 'batch_add',//新建
    MODIFY_PACKAGE: 'modifyPackage',//修改包
    MODIFY_SUB_PRODUCT: 'modifySubProduct',//修改子产品
    RECONFIGURATION: 'reconfiguration',//重新配置
    MATCH_PRICE_POLICY: 'match_price_policy',//匹配价格政策
    COPY_MAPPING: 'copy_mapping',//复制映射
    CHANGE_MASTER_PRICE_BOOK: "change_master_price_book",//切换主对象价目表
    CHANGE_MASTER_AVAILABLE_RANGE_FIELD: "change_master_available_range_field",//切换主对象可售范围相关字段（客户、合作伙伴等字段）
    QUOTER_EXECUTE_AFTER: "quoter_execute_after",//报价器执行后
});

class BomPriceQuerier {

    constructor() {
        this.reconfigurationDataIndexs = [];//二次配置的产品包的dataIndex
    }

    init(context, options) {
        let {objApiName} = options;
        let {fieldMapping, bizStateConfig, pluginApi, requestApi} = context || {};
        this.options = options;
        this.fieldMapping = fieldMapping;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
        this.masterFieldMapping = fieldMapping.getMasterFields() || {};
        this.detailFieldMapping = fieldMapping.getDetailFields(objApiName) || {};

        return this;
    }

    /**
     * 新建数据触发
     */
    addNewDataTrigger(newDatas, context, options) {
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let triggerQueryBomPrice = this.isTriggerQueryBomPriceAfterBatchAdd();
        if (!triggerQueryBomPrice) {
            return Promise.resolve();
        }
        timeAnalysis.start('addNewDataTrigger');
        let modifiedDataIndexList = newDatas && newDatas.length && newDatas.map(it => it.dataIndex);
        let batchMode = this.enableBatchQueryBomPrice(context);
        let triggerType = TriggerType.ADD;
        return (batchMode ? this.batchTriggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList)
            : this.triggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList))
            .then(isTrigger => {
                timeAnalysis.end('addNewDataTrigger')
                if (isTrigger) {//触发新增明细UI事件
                    return this.triggerUIEvent({triggerType: triggerType, addNewDataIndexs: modifiedDataIndexList});
                }
            });
    }

    /**
     * 二次配置Bom触发
     */
    reconfigurationBomTrigger(context, options) {
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let {dataIndex, configBomResult} = options;
        if (!this.reconfigurationDataIndexs.includes(dataIndex)) {
            this.reconfigurationDataIndexs.push(dataIndex);
        }
        this.updateDetailData = null;
        this.addNewDataIndexList = null;
        let triggerType = TriggerType.RECONFIGURATION;
        let ticker = this.startTicker(triggerType);
        let pageId = this.options.dataGetter.getPageId();
        let token = 'bom_' + uuid();
        this.pluginApi.showSingletonLoading(token, {title: this.getCalculatingTip()}, pageId);
        return this.updatePkgDataAfterReconfiguration(dataIndex, configBomResult)
            .then(() => {
                return this.updateSubLinesAfterReconfiguration(dataIndex, configBomResult)
            })
            .then(({addNewDataIndexList}) => {
                this.addNewDataIndexList = addNewDataIndexList;
                let {prod_pkg_key, root_prod_pkg_key} = this.detailFieldMapping;
                let {objApiName, dataGetter} = this.options;
                let detailDataList = dataGetter.getDetail(objApiName);
                let productPkgData = detailDataList && detailDataList.length && detailDataList.find(it => {
                    return it.dataIndex === dataIndex;
                });
                let batchMode = this.enableBatchQueryBomPrice(context);
                if (batchMode) {
                    let bomData = this.getNewBomData(productPkgData, detailDataList);
                    let params = this.batchBuildRequestParams([productPkgData], triggerType, undefined, {[dataIndex]: bomData});
                    return this.batchQueryBomPrice(triggerType, [params], [productPkgData]);
                } else {
                    let subLinesList = detailDataList && detailDataList.length && detailDataList.filter(it => {
                        return it[root_prod_pkg_key] === productPkgData[prod_pkg_key] && it[root_prod_pkg_key] !== it[prod_pkg_key];
                    });
                    let bomList = this.buildBomListBySubLinesList(productPkgData, subLinesList);
                    let params = this.buildRequestParams(productPkgData, bomList, triggerType, undefined, dataIndex);
                    return this.triggerQueryBomPriceAndCalc(productPkgData, params, triggerType, true);
                }
            }).then(() => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                ticker.end(this.tickExtendOpt());
                return {
                    updateDetailData: this.updateDetailData,
                    addNewDataIndexList: this.addNewDataIndexList
                }
            }).then(async result => {
                let {quantity, product_price} = this.detailFieldMapping;
                //触发数量字段变更的UI事件
                await this.triggerUIEvent({
                    triggerType: TriggerType.MODIFY_PACKAGE,
                    changeDataIndex: dataIndex,
                    changeFieldApiName: quantity
                });
                //触发价格字段变更的UI事件
                await this.triggerUIEvent({
                    triggerType: TriggerType.MODIFY_PACKAGE,
                    changeDataIndex: dataIndex,
                    changeFieldApiName: product_price
                });
                return result;
            }).catch(() => {
                this.pluginApi.hideSingletonLoading(token, pageId);
            });
    }

    /**
     * 变更字段触发
     */
    changeFieldTrigger(changeFieldApiName, modifiedDataIndex, context, options) {
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        return this.changeFieldBeforeTrigger(changeFieldApiName, modifiedDataIndex, false)
            .then(() => {
                let {objApiName, dataGetter} = this.options;
                let detailDataList = dataGetter.getDetail(objApiName);
                let modifiedData = detailDataList && detailDataList.length && detailDataList.find(it => it.dataIndex === modifiedDataIndex);
                if (isEmpty(modifiedData)) {
                    return;
                }
                let {quantity, product_price, sales_price, discount, subtotal, dynamic_amount, node_discount, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = this.detailFieldMapping;
                let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = modifiedData;
                let isPkg = bomUtil.isProductPackage(modifiedData);
                let isSubLines = parentProdPkgKey !== undefined && parentProdPkgKey !== null;
                if (!isPkg && !isSubLines) {//非包、非子不需要取价
                    return;
                }
                //包：数量，销售单价，折扣，小计；子产品：价格 数量 选配折扣
                let triggerFields = isPkg ? [quantity, sales_price, discount, subtotal, dynamic_amount]
                    : (isSubLines ? [product_price, quantity, node_discount] : undefined);
                let triggerType = isPkg ? TriggerType.MODIFY_PACKAGE : TriggerType.MODIFY_SUB_PRODUCT;
                let result = this.pluginApi.runPluginSync(emitEvent.bom_queryBomPrice_before, Object.assign({}, this.options, {triggerType}));
                let { forceQueryBomPrice, updateField: forceUpdateField } = result || {};
                if (triggerFields && triggerFields.length && triggerFields.includes(changeFieldApiName) || forceQueryBomPrice) {
                    let pageId = dataGetter.getPageId();
                    let token = 'bom_' + uuid();
                    this.pluginApi.showSingletonLoading(token, {title: this.getCalculatingTip()}, pageId);
                    let productPkgData = isPkg ? modifiedData : detailDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                    let ticker = this.startTicker(triggerType);
                    let batchMode = this.enableBatchQueryBomPrice(context);
                    let calcFunction;
                    let updateField;
                    if (forceQueryBomPrice) {
                        updateField = forceUpdateField;
                    } else {
                        updateField = [quantity, product_price].includes(changeFieldApiName) ? changeFieldApiName : undefined;
                    }
                    if (batchMode) {
                        let bomData = this.getNewBomData(productPkgData, detailDataList, [modifiedDataIndex], updateField);
                        let params = this.batchBuildRequestParams([productPkgData], triggerType, undefined, {[productPkgData.dataIndex]: bomData});
                        calcFunction = this.batchQueryBomPrice(triggerType, [params], [productPkgData]);
                    } else {
                        let subLinesList = detailDataList.filter(it => {
                            return it[root_prod_pkg_key] === productPkgData[prod_pkg_key] && it[root_prod_pkg_key] !== it[prod_pkg_key];
                        });
                        let bomList = this.buildBomListBySubLinesList(productPkgData, subLinesList, [modifiedDataIndex], updateField);
                        let params = this.buildRequestParams(productPkgData, bomList, triggerType, changeFieldApiName, modifiedDataIndex);
                        calcFunction = this.triggerQueryBomPriceAndCalc(productPkgData, params, triggerType, true, {changeFieldApiName});
                    }
                    return calcFunction.then(() => {
                        this.pluginApi.hideSingletonLoading(token, pageId);
                        ticker.end(this.tickExtendOpt());
                        return true;//触发了bom取价
                    }).catch(() => {
                        this.pluginApi.hideSingletonLoading(token, pageId);
                    });
                }
            }).then(isTrigger => {
                if (isTrigger) {//触发了bom取价，执行一次字段变更的UI事件
                    return this.triggerUIEvent({
                        triggerType: TriggerType.MODIFY_PACKAGE,
                        changeDataIndex: modifiedDataIndex,
                        changeFieldApiName
                    });
                }
            });
    }

    /**
     * 匹配价格政策后触发
     */
    matchPricePolicyTrigger(modifiedDataIndexList, context, options) {
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let batchMode = this.enableBatchQueryBomPrice(context);
        let triggerType = TriggerType.MATCH_PRICE_POLICY;
        return (batchMode ? this.batchTriggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList)
            : this.triggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList))
            .then(isTrigger => {
                if (isTrigger) {//触发价格政策后UI事件
                    let {uiEventType} = options || {};
                    return this.triggerUIEvent({triggerType: triggerType, uiEventType});
                }
            });
    }

    /**
     * 复制映射触发
     */
    copyMappingTrigger(context, options) {
        let {dataGetter} = options;
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let detailDataList = dataGetter.getDetail(objApiName);
        let modifiedDataIndexList = detailDataList && detailDataList.length && detailDataList.map(it => it.dataIndex);
        let batchMode = this.enableBatchQueryBomPrice(context);
        let triggerType = TriggerType.COPY_MAPPING;
        return (batchMode ? this.batchTriggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList)
            : this.triggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList))
            .then(isTrigger => {
                if (isTrigger) {//触发页面加载UI事件
                    return this.triggerUIEvent({triggerType: triggerType});
                }
            });
    }

    /**
     * 修改主对象价目表触发
     */
    changeMasterPriceBookTrigger(context, options) {
        let {dataGetter, changeData} = options;
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let {price_book_id} = this.detailFieldMapping;
        let {form_price_book_id} = this.masterFieldMapping;
        let detailDataList = dataGetter.getDetail(objApiName);
        detailDataList && detailDataList.length && detailDataList.forEach(it => {
            it[price_book_id] = changeData && changeData[form_price_book_id];//变更从对象的价目表字段，不要用dataUpdater更新数据
        })
        let modifiedDataIndexList = detailDataList && detailDataList.length && detailDataList.map(it => it.dataIndex);
        let batchMode = this.enableBatchQueryBomPrice(context);
        let triggerType = TriggerType.CHANGE_MASTER_PRICE_BOOK;
        return (batchMode ? this.batchTriggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList, detailDataList)
            : this.triggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList, detailDataList))
            .then(isTrigger => {
                if (isTrigger) {//触发了bom取价，执行一次字段变更的UI事件
                    return this.triggerUIEvent({
                        triggerType: triggerType,
                        changeFieldApiName: form_price_book_id
                    });
                }
            });
    }

    /**
     * 修改主对象可售范围相关字段触发
     */
    changeMasterAvailableRangeFieldTrigger(context, options) {
        let {dataGetter, fieldName} = options;
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let detailDataList = dataGetter.getDetail(objApiName);
        let modifiedDataIndexList = detailDataList && detailDataList.length && detailDataList.map(it => it.dataIndex);
        let batchMode = this.enableBatchQueryBomPrice(context);
        let triggerType = TriggerType.CHANGE_MASTER_AVAILABLE_RANGE_FIELD;
        return (batchMode ? this.batchTriggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList, detailDataList)
            : this.triggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList, detailDataList))
            .then(isTrigger => {
                if (isTrigger) {//触发了bom取价，执行一次字段变更的UI事件
                    return this.triggerUIEvent({
                        triggerType: triggerType,
                        changeFieldApiName: fieldName
                    });
                }
            });
    }

    async quoterExecuteAfter(context, options){
        let {quoterResult} = options;
        if (!quoterResult || !quoterResult.length) {
            return;
        }
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let {dataGetter, dataUpdater} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName);
        if (!detailDataList || !detailDataList.length) {
            return;
        }
        let filterDataIndexList=[];
        let {quantity, product_price, parent_prod_pkg_key, root_prod_pkg_key, related_core_id} = this.fieldMapping.getDetailFields(objApiName);
        //先更新子件单位数量
        quoterResult.forEach(it => {
            let {data_index, [quantity]: quantityV, [product_price]: productPrice} = it;
            let changedData = detailDataList.find(it => it.dataIndex === data_index);
            let {[parent_prod_pkg_key]: parentProdPkgKey, [related_core_id]: relatedCoreId} = changedData || {};
            if (parentProdPkgKey !== undefined && parentProdPkgKey !== null && !relatedCoreId) {//修改子件的数量，更新单位子件的数量
                if (!isEmpty(quantityV)) {
                    const recentSubBom = bomUtil.getRecentSubBomData(changedData, detailDataList);
                    let {[quantity]: parentQuantity = 1} = recentSubBom || {};
                    let quantityResult = multiply(quantityV, parentQuantity);
                    let updateData = Object.assign(it, {
                        [bomUtil.BomConst.amountOriginSubBom]: quantityV,
                        [quantity]: quantityResult,//更新子件数量
                    });
                    Object.assign(changedData, updateData);
                    dataUpdater.updateDetail(objApiName, data_index, updateData);
                    filterDataIndexList.push(data_index);
                }
                if (!isEmpty(productPrice)) {
                    dataUpdater.updateDetail(objApiName, data_index, it);
                    filterDataIndexList.push(data_index);
                }
            }
        });
        //再更新母件数量，同时子件数量加倍
        for (const it of quoterResult) {
            let {data_index, [quantity]: quantityV, [product_price]: productPrice} = it;
            let changedData = detailDataList.find(objectData => objectData.dataIndex === data_index);
            let {[related_core_id]: relatedCoreId} = changedData || {};
            let isPkg = bomUtil.isProductPackage(changedData);
            if (isPkg || relatedCoreId) {//修改包的数量，子件的数量需要乘以包的数量
                if (!isEmpty(quantityV)) {
                    if (relatedCoreId){
                        const recentSubBom = bomUtil.getRecentSubBomData(changedData, detailDataList);
                        let {[quantity]: parentQuantity = 1} = recentSubBom || {};
                        let quantityResult = multiply(quantityV, parentQuantity);
                        let updateData = Object.assign(it, {
                            [bomUtil.BomConst.amountOriginSubBom]: quantityV,
                            [quantity]: quantityResult,//更新子件数量
                        })
                        Object.assign(changedData, updateData);
                        dataUpdater.updateDetail(objApiName, data_index, updateData);
                    }
                    const dataList = detailDataList.filter(detailData => detailData[root_prod_pkg_key] === changedData[root_prod_pkg_key]) || [];
                    await bomUtil.handleQuantityAmount(changedData, dataList, this.options, this.detailFieldMapping, this.pluginApi);
                    dataUpdater.updateDetailByApiName(objApiName, detailDataList);
                    filterDataIndexList.push(data_index);
                }
                if (!isEmpty(productPrice)) {
                    dataUpdater.updateDetail(objApiName, data_index, it);
                    filterDataIndexList.push(data_index);
                }
            }
        }
        return uniq(filterDataIndexList);
    }

    async quoterExecuteEnd(context, options) {
        let {quoterResult, param} = options;
        if (!quoterResult || !quoterResult.length) {
            return;
        }
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let {dataGetter} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName);
        if (!detailDataList || !detailDataList.length) {
            return;
        }
        let modifiedPkgDataIndexs = [];//修改包的dataIndex
        let {changeFieldApiName, changeDataIndex} = param || {};
        let calcFilterFields = {};
        let {quantity, product_price, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key} = this.fieldMapping.getDetailFields(objApiName);
        quoterResult.forEach(it => {
            let {data_index, [quantity]: quantityV, [product_price]: productPrice, pricing_period} = it;
            if (isEmpty(quantityV) && isEmpty(productPrice) && isEmpty(pricing_period)) {
                return;
            }
            calcFilterFields[data_index] = Object.keys(it);
            if (data_index === changeDataIndex && changeFieldApiName) {
                (calcFilterFields[data_index] || (calcFilterFields[data_index] = [])).push(changeFieldApiName);
            }
            let changedData = detailDataList.find(it => it.dataIndex === data_index);
            let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = changedData || {};
            let pkgDataIndex;
            let isPkg = bomUtil.isProductPackage(changedData);
            let isSubBom = !isEmpty(parentProdPkgKey);
            if (isPkg) {
                pkgDataIndex = data_index;
            } else if (isSubBom) {
                let prodPkgData = detailDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                pkgDataIndex = prodPkgData && prodPkgData.dataIndex;
            }
            pkgDataIndex && (modifiedPkgDataIndexs.push(pkgDataIndex));//母件的dataIndex
        });
        modifiedPkgDataIndexs = uniq(modifiedPkgDataIndexs);
        let batchMode = this.enableBatchQueryBomPrice(context);
        let triggerType = TriggerType.QUOTER_EXECUTE_AFTER;
        if (batchMode) {
            await this.batchTriggerWithModifiedDataIndexList(triggerType, modifiedPkgDataIndexs, detailDataList, {calcFilterFields});
        } else {
            await this.triggerWithModifiedDataIndexList(triggerType, modifiedPkgDataIndexs, detailDataList, {calcFilterFields});
        }
    }

    async periodProductPricingPeriodChangeEnd(context, options) {
        // let {fieldName,dataIndex} = options;
        // return this.changeFieldTrigger(fieldName, dataIndex, context, options);
        // let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        // this.init(context, Object.assign({}, options, {objApiName}));
        //
        // let batchMode = this.enableBatchQueryBomPrice(context);
        // let triggerType = TriggerType.MODIFY_PACKAGE;
        // return (batchMode ? this.batchTriggerWithModifiedDataIndexList(triggerType, [dataIndex])
        //     : this.triggerWithModifiedDataIndexList(triggerType, [dataIndex]))
        //     .then(isTrigger => {
        //         if (isTrigger) {
        //             return this.triggerUIEvent({triggerType: triggerType});
        //         }
        //     });
    }

    async triggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList, detailDataList, extraInfo) {
        if (!modifiedDataIndexList || !modifiedDataIndexList.length) {
            return;
        }
        timeAnalysis.start('findPkg')
        if (!detailDataList || !detailDataList.length) {
            let {objApiName, dataGetter} = this.options;
            detailDataList = dataGetter.getDetail(objApiName);
        }
        let pkgDataList = [];
        detailDataList && detailDataList.length && detailDataList.forEach(it => {
            if (modifiedDataIndexList.includes(it.dataIndex)) {
                let isPackage = bomUtil.isProductPackage(it);
                isPackage && pkgDataList.push(it);
            }
        });
        timeAnalysis.end('findPkg');
        if (!pkgDataList.length) {
            return;
        }
        let pageId = this.options.dataGetter.getPageId();
        let token = 'bom_' + uuid();
        this.pluginApi.showSingletonLoading(token, {title: this.getCalculatingTip()}, pageId);
        let ticker = this.startTicker(triggerType);
        try {
            let {calcFilterFields} = extraInfo || {};
            let {prod_pkg_key, root_prod_pkg_key} = this.detailFieldMapping;
            let length = pkgDataList.length;
            for (let i = 0; i < length; i++) {
                timeAnalysis.start('buildParams')
                let pkgData = pkgDataList[i];
                let subLinesList = detailDataList.filter(it => {
                    return it[root_prod_pkg_key] === pkgData[prod_pkg_key] && it[root_prod_pkg_key] !== it[prod_pkg_key];
                });
                let bomList = this.buildBomListBySubLinesList(pkgData, subLinesList, (triggerType === TriggerType.ADD) ? null : modifiedDataIndexList);
                let params = this.buildRequestParams(pkgData, bomList, triggerType, undefined, pkgData.dataIndex);
                let isCalcDescribeFields = (i === (length - 1));
                timeAnalysis.end('buildParams')
                await this.triggerQueryBomPriceAndCalc(pkgData, params, triggerType, isCalcDescribeFields, {calcFilterFields});
            }
            this.pluginApi.hideSingletonLoading(token, pageId);
            ticker.end(this.tickExtendOpt({pkgCount: length}));
            return true;
        } catch (e) {
            this.pluginApi.hideSingletonLoading(token, pageId);
        }
    }

    /**
     * 字段变更触发bom取价前的处理
     */
    changeFieldBeforeTrigger(changeFieldApiName, modifiedDataIndex) {
        let {objApiName, dataGetter, dataUpdater, formApis} = this.options;
        let {quantity, product_price, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, node_discount, node_price, amount_any, related_core_id} = this.detailFieldMapping;
        let detailDataList = dataGetter.getDetail(objApiName);
        if (changeFieldApiName === quantity) {
            return new Promise(async resolve => {
                let changedData = detailDataList && detailDataList.length && detailDataList.find(it => it.dataIndex === modifiedDataIndex);
                let {[parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey} = changedData;
                let isPkg = bomUtil.isProductPackage(changedData);
                let isSubLines = parentProdPkgKey !== undefined && parentProdPkgKey !== null;
                if (!isPkg && !isSubLines) {
                    resolve({isPkg: false, modifiedDataIndexList: []});
                    return;
                }
                let productPkgData = isPkg ? changedData : detailDataList.find(it => it[prod_pkg_key] === rootProdPkgKey);
                if (!productPkgData) {
                    resolve({isPkg: false, modifiedDataIndexList: []});
                    return;
                }
                if (parentProdPkgKey !== undefined && parentProdPkgKey !== null) {//如果变更的是子件，更新子件单位数量
                    let amountOriginSubBom = changedData[quantity];
                    if (!changedData[`${amount_any}__v`]) {
                        const recentSubBom = bomUtil.getRecentSubBomData(changedData, detailDataList);
                        amountOriginSubBom = recentSubBom[quantity] == 0 ? 0 : divide(changedData[quantity], recentSubBom[quantity]);
                    }
                    changedData[bomUtil.BomConst.amountOriginSubBom] = amountOriginSubBom;
                    dataUpdater.updateDetail(objApiName, modifiedDataIndex, {
                        [bomUtil.BomConst.amountOriginSubBom]: amountOriginSubBom,
                    });
                }
                // 产品包 || 复用bom，则需要更新其下所有子件的数量加倍
                if (isPkg || changedData[related_core_id]) {
                    const dataList = detailDataList && detailDataList.filter(bom => bom[root_prod_pkg_key] == changedData[root_prod_pkg_key]) || [];
                    const { modifiedDataIndexList } = await bomUtil.handleQuantityAmount(changedData, dataList, this.options, this.detailFieldMapping, this.pluginApi) || [];
                    dataUpdater.updateDetailByApiName(objApiName, detailDataList);
                    resolve({isPkg, modifiedDataIndexList});
                }
                resolve({isPkg, modifiedDataIndexList: []});
            }).then(async result => {
                let {isPkg, modifiedDataIndexList} = result;
                if (isPkg) {//如果是变更的是产品包的数量字段，则计算子件数量相关的计算字段
                    await this.fillOtherFieldInfo(modifiedDataIndexList);//如果修改的是产品包的数量，子件的数量变化后，要更新多单位相关字段
                    return await formApis.triggerCalAndUIEvent({
                        objApiName: objApiName,
                        modifiedDataIndexs: modifiedDataIndexList,
                        changeFields: [quantity],
                    });
                }
            });
        } else if (changeFieldApiName === node_discount) {
            return new Promise(resolve => {
                let changedData = detailDataList && detailDataList.length && detailDataList.find(it => it.dataIndex === modifiedDataIndex);
                let {[parent_prod_pkg_key]: parentProdPkgKey, [node_discount]: nodeDiscount = 0, [node_price]: nodePrice = 0} = changedData;
                if (!isEmpty(parentProdPkgKey)) {//修改子件的选配折扣，需要计算下价格，价格=选配价格*选配折扣
                    let price = multiply(divide(nodePrice, 100), nodeDiscount);
                    dataUpdater.updateDetail(objApiName, modifiedDataIndex, {[product_price]: price});
                }
                resolve();
            });
        } else {
            return Promise.resolve();
        }
    }

    /**
     * 二次配置Bom后更新母件信息
     */
    updatePkgDataAfterReconfiguration(pkgDataIndex, configBomResult) {
        let {objApiName, dataGetter, dataUpdater, formApis} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName);
        let productPkgData = detailDataList && detailDataList.length && detailDataList.find(it => {
            return it.dataIndex === pkgDataIndex;
        });
        if (!productPkgData) {
            return Promise.resolve();
        }
        let {bom_id, price_book_id, price_book_product_id} = this.detailFieldMapping;
        let updateData = {};
        let fieldApiNames = [];
        let {[price_book_id]: orgPriceBookId, [price_book_product_id]: orgPriceBookProductId} = productPkgData;
        let {
            price_book_id: priceBookId, price_book_id__r: priceBookName,
            price_book_product_id: priceBookProductId, price_book_product_id__r: priceBookProductName,
            root_id,
            attributes, nonstandardAttributes, selectedAttributeValues, selectedNonstandardAttributes
        } = configBomResult || {};
        if (orgPriceBookId !== priceBookId) {
            Object.assign(updateData, {
                [price_book_id]: priceBookId,
                [`${price_book_id}__r`]: priceBookName
            });
            fieldApiNames.push(price_book_id);
        }
        if (orgPriceBookProductId !== priceBookProductId) {
            Object.assign(updateData, {
                [price_book_product_id]: priceBookProductId,
                [`${price_book_product_id}__r`]: priceBookProductName
            });
            fieldApiNames.push(price_book_product_id);
        }
        if (root_id) {
            Object.assign(updateData, {[bom_id]: root_id});
        }
        let _selectedAttributeValues = selectedAttributeValues || attributes;//attributes客开返回的
        let _selectedNonstandardAttributes = selectedNonstandardAttributes || nonstandardAttributes;//nonstandardAttributes客开返回的
        let pkgSubProduct = Object.assign({}, configBomResult, (_selectedAttributeValues || _selectedNonstandardAttributes) && {
            selectedAttributeValues: _selectedAttributeValues,
            nonstandardAttribute: _selectedNonstandardAttributes
        });
        let attributeData = this.parseBomData2DetailData(objApiName, pkgSubProduct);
        if (!isEmpty(attributeData)) {
            Object.assign(updateData, attributeData);
            fieldApiNames.push(...Object.keys(attributeData));
        }
        if (!fieldApiNames.length) {
            return Promise.resolve();
        }
        dataUpdater.updateDetail(objApiName, pkgDataIndex, updateData);
        return formApis.triggerCalAndUIEvent({
            objApiName: objApiName,
            modifiedDataIndexs: [pkgDataIndex],
            changeFields: fieldApiNames,
        });
    }

    async updateSubLinesAfterReconfiguration(pkgDataIndex, configBomResult) {
        let {objApiName, dataGetter, dataUpdater} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName);
        let productPkgData = detailDataList && detailDataList.length && detailDataList.find(it => {
            return it.dataIndex === pkgDataIndex;
        });
        if (!productPkgData) {
            return;
        }
        let {bom_id, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, node_type} = this.detailFieldMapping;
        let {selected_list = []} = configBomResult;
        let i = detailDataList.length;
        let dataList = [];
        while (i--) {
            let detailData = detailDataList[i];
            let {dataIndex, [bom_id]: bomId, [prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey, [node_type]: nodeType} = detailData;
            if (dataIndex === pkgDataIndex) {//产品包不用处理
                continue
            }
            if (!isEmpty(parentProdPkgKey) && rootProdPkgKey === productPkgData[prod_pkg_key]) {
                let index = selected_list.findIndex(it => {
                    let {prod_pkg_key: subProdPkgKey, _id} = it;
                    return nodeType === 'temp' ? subProdPkgKey === prodPkgKey : _id === bomId;
                });
                if (index === -1) {//未找到，删除当前数据
                    dataUpdater.del(objApiName, detailData.dataIndex);
                } else {//找到了，更新当前数据
                    let subBomData = selected_list.splice(index, 1);
                    let subLinesData = bomUtil.subProduct2SubLines(productPkgData, {
                        ...(subBomData[0]),
                        node_type: nodeType
                    }, {
                        objApiName,
                        getDescribe: dataGetter.getDescribe.bind(this),
                        parseBomData2DetailData: this.parseBomData2DetailData.bind(this)
                    });
                    dataList.push(Object.assign(subLinesData, { dataIndex }));
                }
            }
        }
        await bomUtil.handleQuantityAmount(productPkgData, dataList, this.options, this.detailFieldMapping, this.pluginApi) || [];
        dataList.forEach(detailData => {
            dataUpdater.updateDetail(objApiName, detailData.dataIndex, detailData);
        })
        let addNewDataIndexList = [];
        let addNewSubLinesList = await this.getAddNewSubLinesList(productPkgData, selected_list);
        if (addNewSubLinesList && addNewSubLinesList.length) {
            addNewDataIndexList = addNewSubLinesList.map(it => it.dataIndex);
            let objectDescribe = dataGetter.getDescribe(objApiName);
            dataUpdater.add(addNewSubLinesList);
            let isOpenCpq = this.bizStateConfig.isOpenCpq();
            let self = this;
            addNewSubLinesList.forEach(it => {
                let readonlyFields = getReadonlyFields(objApiName, it, objectDescribe, this.fieldMapping, isOpenCpq, (readonlyFields) => {
                    self.pluginApi.runPluginSync(emitEvent.bom_updateReadonlyFields_before_sync, Object.assign({}, self.options, {
                        objectData: it,
                        readonlyFields
                    }));
                });
                dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                    objApiName,
                    dataIndex: it.dataIndex,
                    fieldName: readonlyFields,
                    status: true,
                    biz: 'bom',
                    priority: 11
                });
            });
            await this.calcAddNewSubLinesFields(addNewSubLinesList);
        }
        return {addNewDataIndexList}
    }

    /**
     * 触发bom取价及计算
     * @param productPkgData 产品包数据
     * @param params 调用接口入参
     * @param triggerType 触发类型
     * @param calcDescribeFields 是否需要计算描述级别的字段
     */
    triggerQueryBomPriceAndCalc(productPkgData, params, triggerType, calcDescribeFields, opt) {
        if(!params.accountId) return Promise.resolve();
        let {masterObjApiName, objApiName} = this.options;
        let packageDataIndex = productPkgData.dataIndex;
        timeAnalysis.start('triggerQueryBomPriceAndCalc')
        timeAnalysis.start('queryBomPrice')
        return this.requestApi.queryBomPrice(params)
            .then(result => {
                timeAnalysis.end('queryBomPrice')
                timeAnalysis.start('handleQueryBomResult')
                return this.handleQueryBomResult(productPkgData, result);
            })
            .then((updatePackageData) => {//计算母件
                timeAnalysis.end('handleQueryBomResult')
                timeAnalysis.start('triggerCalcPackageData')
                let changedFields = updatePackageData ? Object.keys(updatePackageData) : [];//记录更新母件的字段
                fieldChangedRecorder.record(objApiName, packageDataIndex, changedFields);
                return this.triggerCalcPackageData(updatePackageData, productPkgData, triggerType, opt);
            })
            .then((calcResult) => {//计算子件
                timeAnalysis.end('triggerCalcPackageData')
                timeAnalysis.start('triggerCalcSubLinesData')
                fieldChangedRecorder.recordByCalcResult(masterObjApiName, objApiName, calcResult, [packageDataIndex]);
                return this.triggerCalcSubLinesData(productPkgData, calcDescribeFields, triggerType, opt);
            })
            .then((calcResult) => {
                timeAnalysis.end('triggerCalcSubLinesData')
                timeAnalysis.start('refreshMDUI')
                fieldChangedRecorder.recordByCalcResult(masterObjApiName, objApiName, calcResult, [packageDataIndex]);
                let changedFieldsMap = fieldChangedRecorder.getChangedFieldMap();
                fieldChangedRecorder.clear();
                return this.runQueryBomPriceEndEvent(triggerType, changedFieldsMap);
            })
            .then(() => {
                timeAnalysis.end('refreshMDUI')
                timeAnalysis.end('triggerQueryBomPriceAndCalc');
            })
            .catch(err => {
                this.pluginApi.showToast(err);
            });
    }

    /**
     * 处理bom返回的数据
     * @param productPkgData 产品包数据
     * @param bomPriceResult Bom取价结果数据
     * @param triggerCalc 是否触发计算
     */
    async handleQueryBomResult(productPkgData, bomPriceResult = [], triggerCalc = true) {
        let {objApiName, dataGetter, dataUpdater} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName) || [];
        let updatePackageData;
        let i = detailDataList.length;
        let {product_price, bom_id, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, node_type, price_book_id, price_book_product_id} = this.detailFieldMapping;
        this.updateDetailData = {};
        let dataList = [];
        while (i--) {
            let objectData = detailDataList[i];
            let {dataIndex, [bom_id]: bomId, [prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey, [node_type]: nodeType} = objectData;
            if (dataIndex === productPkgData.dataIndex) {//更新母件的价格、折扣
                let _index = bomPriceResult.findIndex(it => {
                    return it._id === bomId;
                });
                if (_index !== -1) {
                    let pkgBomData = bomPriceResult.splice(_index, 1);
                    let {
                        root_id, adjust_price, price_book_id: priceBookId, price_book_id__r: priceBookName,
                        price_book_product_id: priceBookProductId, price_book_product_id__r: priceBookProductName
                    } = pkgBomData[0];
                    let packageData = {
                        [bom_id]: root_id,
                        [product_price]: adjust_price,
                        [price_book_id]: priceBookId,
                        [`${price_book_id}__r`]: priceBookName,
                        [price_book_product_id]: priceBookProductId,
                        [`${price_book_product_id}__r`]: priceBookProductName
                    };
                    let backFillData = this.parseBomData2DetailData(objApiName, pkgBomData[0]);
                    if (!isEmpty(backFillData)) {
                        Object.assign(packageData, backFillData);
                    }
                    dataUpdater.updateDetail(objApiName, dataIndex, packageData);
                    updatePackageData = packageData;
                    this.updateDetailData[dataIndex] = packageData;
                }
            } else {//更新子件
                if (!isEmpty(parentProdPkgKey) && rootProdPkgKey === productPkgData[prod_pkg_key]) {
                    let _index = bomPriceResult.findIndex(it => {
                        let {prod_pkg_key: subProdPkgKey, _id} = it;
                        return nodeType === 'temp' ? subProdPkgKey === prodPkgKey : _id === bomId;
                    });
                    if (_index === -1) {//未找到，删除当前数据
                        dataUpdater.del(objApiName, dataIndex);
                    } else {//找到了，更新当前数据
                        let subBomData = bomPriceResult.splice(_index, 1);
                        let subLinesData = bomUtil.subProduct2SubLines(productPkgData, {
                            ...(subBomData[0]),
                            node_type: nodeType
                        }, {
                            objApiName,
                            getDescribe: dataGetter.getDescribe.bind(this),
                            parseBomData2DetailData: this.parseBomData2DetailData.bind(this)
                        });
                        dataList.push(Object.assign(subLinesData, { dataIndex }));
                        if (!(this.addNewDataIndexList && this.addNewDataIndexList.includes(dataIndex))) {
                            this.updateDetailData[dataIndex] = subLinesData;
                        }
                    }
                }
            }
        }
        await bomUtil.handleQuantityAmount(productPkgData, dataList, this.options, this.detailFieldMapping, this.pluginApi) || [];
        dataList.forEach(detailData => {
            dataUpdater.updateDetail(objApiName, detailData.dataIndex, detailData);
        })
        let addNewSubLinesList = await this.getAddNewSubLinesList(productPkgData, bomPriceResult);
        if (addNewSubLinesList && addNewSubLinesList.length) {
            let objectDescribe = dataGetter.getDescribe(objApiName);
            dataUpdater.add(addNewSubLinesList);
            let isOpenCpq = this.bizStateConfig.isOpenCpq();
            let self = this;
            addNewSubLinesList.forEach(it => {
                let readonlyFields = getReadonlyFields(objApiName, it, objectDescribe, this.fieldMapping, isOpenCpq, (readonlyFields) => {
                    self.pluginApi.runPluginSync(emitEvent.bom_updateReadonlyFields_before_sync, Object.assign({}, self.options, {
                        objectData: it,
                        readonlyFields
                    }));
                }) || [];
                dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                    objApiName,
                    dataIndex: it.dataIndex,
                    fieldName: readonlyFields,
                    status: true,
                    biz: 'bom',
                    priority: 11
                });
            });
            if (triggerCalc) {
                await this.calcAddNewSubLinesFields(addNewSubLinesList);
            }
        }
        return updatePackageData;
    }

    /**
     * 触发计算包的价格、折扣、销售单价相关的计算字段
     * @returns {Promise<{name: string}>}
     */
    triggerCalcPackageData(updatePackageData, productPkgData, triggerType, opt) {
        let productPkgDataList = [];
        if (isArray(productPkgData)) {
            productPkgDataList.push(...productPkgData)
        } else {
            productPkgDataList.push(productPkgData)
        }
        let {product_price, discount, sales_price} = this.detailFieldMapping;
        let {objApiName, formApis} = this.options;
        let filterFields = [product_price, discount, 'price_per_set'];
        if ([TriggerType.MODIFY_PACKAGE, TriggerType.MODIFY_SUB_PRODUCT, TriggerType.QUOTER_EXECUTE_AFTER].includes(triggerType)) {
            let {changeFieldApiName} = opt || {};
            if (changeFieldApiName) {
                filterFields.push(changeFieldApiName)
            }
        }
        let {calcFilterFields} = opt || {};
        let preFilterFields = calcFilterFields && calcFilterFields[productPkgData.dataIndex];
        if (preFilterFields && preFilterFields.length) {
            filterFields.push(...preFilterFields);
        }
        let filterCalcFields = {[objApiName]: filterFields};//母件的价格、折扣不参与计算
        let modifiedDataIndexList = productPkgDataList.map(it => it.dataIndex);//modifiedDataIndexList只传入包的
        return formApis.triggerCalAndUIEvent({
            objApiName: objApiName,
            modifiedDataIndexs: modifiedDataIndexList,
            changeFields: [product_price, discount, sales_price],
            filterFields: filterCalcFields,
        }).then(rst => rst && rst.calRst);
    }

    /**
     * 触发当前包下的子件的数量、价格、折扣、标准选配价格、选配折扣、部件小计、分摊比例相关的计算字段
     */
    triggerCalcSubLinesData(productPkgData, calcDescribeFields, triggerType, opt) {
        let productPkgDataList = [];
        if (isArray(productPkgData)) {
            productPkgDataList.push(...productPkgData)
        } else {
            productPkgDataList.push(productPkgData)
        }
        let {
            quantity, product_price, discount, sales_price, subtotal, node_price, node_discount, node_subtotal, share_rate, price_book_product_id, price_book_id,
            price_book_price, price_book_discount, system_discount_amount, total_discount_amount, prod_pkg_key, root_prod_pkg_key
        } = this.detailFieldMapping;
        let {objApiName, dataGetter, formApis} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName);
        let modifiedDataIndexList = [];
        productPkgDataList.forEach(productPkgData => {
            let {[prod_pkg_key]: prodPkgKey} = productPkgData;
            let tempModifiedDataIndexList = detailDataList && detailDataList.length && detailDataList//modifiedDataIndexList只传入子的
                .filter(it => it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key])
                .map(it => it.dataIndex);
            if (tempModifiedDataIndexList && tempModifiedDataIndexList.length) {
                modifiedDataIndexList.push(...tempModifiedDataIndexList);
            }
        })
        let fieldApiNames = [quantity, product_price, discount, node_price, node_discount, node_subtotal, share_rate, price_book_product_id, price_book_id];
        //子件的价格、折扣、销售单价、小计、价目表价格、价目表折扣不参与计算
        let filterCalcFields = {[objApiName]: [product_price, discount, sales_price, subtotal, price_book_price, price_book_discount, system_discount_amount, total_discount_amount, quantity, 'price_per_set']};
        let extraFields = {};
        if (calcDescribeFields) {
            let detailDescribe = dataGetter.getDescribe(objApiName);
            let relate_fields = detailDescribe.calculate_relation && detailDescribe.calculate_relation.relate_fields;//计算描述级别的字段
            if (!isEmpty(relate_fields)) {
                Object.keys(relate_fields).forEach(objApiName => {
                    let fields = relate_fields[objApiName];
                    if (fields && fields.length) {
                        let filterFields = filterCalcFields[objApiName];
                        if (filterFields && filterFields.length) {
                            fields = fields.filter(it => {
                                return !filterFields.includes(it.fieldName)
                            })
                        }
                        extraFields[objApiName] = fields && fields.length && fields.map(it => it.fieldName) || [];
                    }
                })
            }
        }
        let {calcFilterFields} = opt || {}
        let calcParam = {
            objApiName: objApiName,
            modifiedDataIndexs: modifiedDataIndexList,
            changeFields: fieldApiNames,
            filterFields: filterCalcFields,
            extraFields,
        }
        this.pluginApi.runPluginSync(emitEvent.bom_calcSubProduct_before_sync, {calcParam, triggerType});
        return formApis.triggerCalAndUIEvent({
            ...calcParam,
            beforeCalPost(p) {
                if (p && p.data && !isEmpty(calcFilterFields)) {
                    let excludedDetailCalculateFields = {};
                    each(calcFilterFields, (value, key) => {
                        if (value && value.length) {
                            excludedDetailCalculateFields[key] = value.map(it => {
                                return {fieldName: it, order: 1}
                            })
                        }
                    })
                    p.data.excludedDetailCalculateFields = {[objApiName]: excludedDetailCalculateFields};//报价器修改的字段不计算
                }
            }
        }).then(async rst => {
            let calcSubResult = rst && rst.calRst;
            await this.pluginApi.runPlugin(emitEvent.bom_calcSubProduct_end, Object.assign({}, this.options, {
                objApiName,
                modifiedDataIndexList
            }));
            return calcSubResult;
        });
    }

    async getAddNewSubLinesList(productPkgData, subProducts) {//添加新增的子产品数据
        let {objApiName, dataGetter, formApis} = this.options;
        let defaultData = dataGetter.getDetailDefaultData(objApiName, productPkgData.record_type);

        const dataList  = subProducts && subProducts.length && subProducts.map(it => {
            let subLinesData = bomUtil.subProduct2SubLines(productPkgData, it, {
                objApiName,
                getDescribe: dataGetter.getDescribe.bind(this),
                parseBomData2DetailData: this.parseBomData2DetailData.bind(this)
            });
            if (isEmpty(subLinesData.dataIndex)) {
                subLinesData.dataIndex = formApis.createNewDataIndex();
            }
            return Object.assign({}, defaultData, subLinesData)
        });
        await bomUtil.handleQuantityAmount(productPkgData, dataList, this.options, this.detailFieldMapping, this.pluginApi) || [];
        return dataList;

    }

    calcAddNewSubLinesFields(addNewSubLinesList) {
        let addNewDataIndexList = addNewSubLinesList && addNewSubLinesList.length && addNewSubLinesList.map(it => it.dataIndex);
        if (addNewDataIndexList && addNewDataIndexList.length) {//有新增的子件，要计算自定义字段的默认值
            let {quantity, product_price, product_group_id, amount_any} = this.detailFieldMapping;
            let {objApiName, formApis} = this.options;
            let filterCalcFields = [quantity, product_price, product_group_id, amount_any];
            return formApis.triggerCalAndUIEvent({
                objApiName: objApiName,
                newDataIndexs: addNewDataIndexList,
                filterFields: {[objApiName]: filterCalcFields}
            });
        }
        return Promise.resolve();
    }

    buildRequestParams(productPkgData = {}, bomList, triggerType, triggerFieldName, modifiedDataIndex) {
        let {form_account_id, form_partner_id, mc_currency} = this.masterFieldMapping;
        let {price_book_id, quantity, bom_id, subtotal, bom_core_id, bom_type, bom_version, new_bom_path} = this.detailFieldMapping;
        let {masterObjApiName, objApiName, dataGetter, seriesId} = this.options;
        let masterData = dataGetter.getMasterData();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId, [mc_currency]: mcCurrency} = masterData;
        let detailDataList = dataGetter.getDetail(objApiName);
        let describe = dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let priceBookField = describe && describe.fields && describe.fields[price_book_id];
        let hasFilters = !!(priceBookField && priceBookField.wheres && priceBookField.wheres.length);
        let sortDetailDatas = undefined;
        if (modifiedDataIndex) {
            sortDetailDatas = [];
            let modifiedData = detailDataList && detailDataList.find(it => it.dataIndex == modifiedDataIndex);
            if (modifiedData) {
                sortDetailDatas.push(modifiedData);//当前操作的数据放到首位
            }
            detailDataList && detailDataList.forEach(detailData => {
                if (detailData.dataIndex != modifiedDataIndex) {
                    sortDetailDatas.push(detailData)
                }
            });
        } else {
            sortDetailDatas = detailDataList
        }
        let calculateArg = this.newCalculateArg(masterData, [productPkgData]);
        let {
            [quantity]: quantityValue, [bom_id]: bomId, [subtotal]: productPkgSubtotal,
            [bom_core_id]: bomCoreId, [bom_type]: bomType, [`${bom_type}__v`]: bomTypeV, [bom_version]: bomVersion, [new_bom_path]: newBomPath
        } = productPkgData;
        return {
            coreId: bomCoreId,
            nodeBomCoreType: bomTypeV || bomType,
            nodeBomCoreVersion: bomVersion,
            newBomPath: newBomPath,
            apiName: masterObjApiName, //主对象名称
            detailApiName: objApiName,//从对象的apiName
            rootSubtotal: productPkgSubtotal,//包的小计—修改包传，开启价格政策传，其他情况不传
            rootAmount: quantityValue, //包的数量
            rootBomId: bomId,//包的id
            accountId: accountId, //客户id
            mcCurrency: mcCurrency,
            partnerId: partnerId,//合作伙伴id
            bomList,
            //计算母件小计传给计算接口的参数（修改子件传此参数，其他情况不传）
            calculateArg: (triggerFieldName === subtotal || triggerType === TriggerType.MATCH_PRICE_POLICY) ? undefined : calculateArg,
            objectData: masterData,
            seriesId,
            details: simplifyDetails(hasFilters, {[objApiName]: sortDetailDatas}),
            switchMasterPriceBook: triggerType === TriggerType.CHANGE_MASTER_PRICE_BOOK
        }
    }

    buildBomListBySubLinesList(productPkgData = {}, subLinesList, modifiedDataIndexList, updateField) {
        let {
            product_price, quantity, bom_id, product_id, price_book_id, attribute_price_book_id, prod_pkg_key, parent_prod_pkg_key, root_prod_pkg_key, node_type,
            bom_core_id, bom_type, bom_version, new_bom_path, amount_any
        } = this.detailFieldMapping;
        let {
            dataIndex, [product_price]: productPkgPrice, [quantity]: productPkgQuantity,
            [bom_id]: bomId, [product_id]: productId, [price_book_id]: priceBookId, [attribute_price_book_id]: attributePriceBookId,
            [prod_pkg_key]: pkgProdPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey,
            [bom_core_id]: pkgBomCoreId, [bom_type]: bomType, [`${bom_type}__v`]: bomTypeV, [bom_version]: bomVersion, [new_bom_path]: pkgNewBomPath
        } = productPkgData;
        let updateFlag = modifiedDataIndexList && modifiedDataIndexList.length && modifiedDataIndexList.includes(dataIndex) || false;
        let packageBomInfo = this.buildBomInfo(bomId, productId, priceBookId, productPkgPrice, 1,
            attributePriceBookId, updateFlag, 0, null, pkgProdPkgKey, parentProdPkgKey, rootProdPkgKey,
            pkgBomCoreId, null, (bomTypeV || bomType), bomVersion, pkgNewBomPath, false, updateField);
        let parseResult = this.parseBomParamsBefore(productPkgData);
        Object.assign(packageBomInfo, parseResult);
        let subBomList = subLinesList && subLinesList.map((it, index) => {
            let {
                dataIndex, [product_price]: price, [quantity]: subQuantity, [bom_id]: bomId, [product_id]: productId, [price_book_id]: priceBookId, [attribute_price_book_id]: attributePriceBookId, [node_type]: nodeType,
                [prod_pkg_key]: prodPkgKey, [parent_prod_pkg_key]: parentProdPkgKey, [root_prod_pkg_key]: rootProdPkgKey,
                [bom_core_id]: bomCoreId, [bom_type]: bomType, [`${bom_type}__v`]: bomTypeV, [bom_version]: bomVersion, [new_bom_path]: newBomPath, [`${amount_any}__v`]: amountAny
            } = it;
            let amount;
            amount = it[bomUtil.BomConst.amountOriginSubBom];
            if (isEmpty(amount)) {
                amount = productPkgQuantity == 0 ? 0 : divide(subQuantity, productPkgQuantity);
            }
            if (nodeType === 'temp') {//临时子件的new_bom_path需要传它上层节点的new_bom_path
                if (parentProdPkgKey === pkgProdPkgKey) {
                    newBomPath = pkgNewBomPath;
                } else {
                    let parentData = subLinesList && subLinesList.find(it => it[prod_pkg_key] === parentProdPkgKey);
                    newBomPath = parentData && parentData[new_bom_path] || '';
                }
                newBomPath = newBomPath + '.' + uuid();
            }
            let updateFlag = modifiedDataIndexList && modifiedDataIndexList.length && modifiedDataIndexList.includes(dataIndex) || false;
            let subBomInfo = this.buildBomInfo(bomId, productId, priceBookId, price, amount, attributePriceBookId, updateFlag, (index + 1),
                nodeType, prodPkgKey, parentProdPkgKey, rootProdPkgKey, pkgBomCoreId, bomCoreId, (bomTypeV || bomType), bomVersion, newBomPath, false, updateField);
            let parseResult = this.parseBomParamsBefore(it);
            Object.assign(subBomInfo, parseResult);
            return subBomInfo;
        });
        return [packageBomInfo, ...(subBomList || [])];
    }

    buildBomInfo(bomId, productId, priceBookId, price, amount, attrPriceBookId, updateFlag, orderField, nodeType,
                 prodKey, parentProdKey, rootProdKey, coreId, relatedCoreId, nodeBomCoreType, nodeBomCoreVersion, newBomPath, advancePriceFlag, updateField) {
        return {
            bomId,
            productId,
            priceBookId,
            price,
            amount,
            attrPriceBookId,
            updateFlag,
            orderField,
            nodeType,
            prodKey,
            parentProdKey,
            rootProdKey,
            coreId,
            relatedCoreId,
            nodeBomCoreType,
            nodeBomCoreVersion,
            newBomPath,
            advancePriceFlag,
            updateField: updateFlag ? updateField : undefined /*用于区分当前更改的那个字段，字段值quantity 、price 、pricingPeriod 代表数量、价格、期数*/
        };
    }

    newCalculateArg(masterData, productPkgDataList) {
        let {product_price, sales_price} = this.detailFieldMapping;
        let {masterObjApiName, objApiName, dataGetter} = this.options;
        let dataIndexList = productPkgDataList && productPkgDataList.map(it => it.dataIndex);
        let detailDescribe = dataGetter.getDescribe(objApiName);
        let calculateFields = this.getCalculateFields([product_price, sales_price], detailDescribe);
        let detailDataObj = {};
        productPkgDataList && productPkgDataList.forEach(it => {
            detailDataObj[it.dataIndex] = it;
        });
        return {
            masterObjectApiName: masterObjApiName,
            masterData: masterData,
            detailDataMap: {//只传包的数据
                [objApiName]: detailDataObj
            },
            modifiedObjectApiName: objApiName,
            modifiedDataIndexList: dataIndexList,//写死为包的dataIndex
            calculateFields: calculateFields
        }
    }

    getCalculatingTip() {
        let calculating = i18n("ava.object_form.calculating");//'计算中'
        return `CPQ${calculating}`;
    }

    async fillOtherFieldInfo(modifiedDataIndexList) {
        let isOpenMultiUnit = this.bizStateConfig.isOpenMultipleUnit();
        if (!isOpenMultiUnit || !modifiedDataIndexList || !modifiedDataIndexList.length) {
            return;
        }
        let {objApiName} = this.options;
        await this.pluginApi.runPlugin('multi-unit.triggerBatchCalc', Object.assign({}, this.options, {
            objApiName,
            triggerDataIndexList: modifiedDataIndexList
        }));
    }

    /**
     * 批量检查产品包的价格是否正确
     * @return {Promise<>}
     */
    async batchCheckPkgPrice(context, options) {
        let objApiName = context.fieldMapping.getFirstDetailObjApiName();
        this.init(context, Object.assign({}, options, {objApiName}));
        let jsonString = this.pluginApi.getCloudCtrl("enableCheckBomPrice");
        let result = jsonString && JSON.parse(jsonString);
        let checkPrice = result && result.enable;
        if (!checkPrice) {
            return Promise.resolve();
        }
        let reconfigurationDataIndexs = this.reconfigurationDataIndexs;
        if (!reconfigurationDataIndexs || !reconfigurationDataIndexs.length) {
            return Promise.resolve();
        }
        this.reconfigurationDataIndexs = [];//清除记录的dataIndex
        let promises = reconfigurationDataIndexs.map(dataIndex => {
            return this.checkPkgPrice(dataIndex);
        });
        return Promise.all(promises);
    }

    async checkPkgPrice(dataIndex) {
        let {objApiName, dataGetter} = this.options;
        let detailDataList = dataGetter.getDetail(objApiName);
        if (!detailDataList || !detailDataList.length) {
            return Promise.resolve();
        }
        let productPkgData = detailDataList.find(it => {
            return it.dataIndex === dataIndex;
        });
        if (!productPkgData) {
            return Promise.resolve();
        }
        let {prod_pkg_key, root_prod_pkg_key, product_price, bom_id} = this.detailFieldMapping;
        let subLinesList = detailDataList.filter(it => {
            return it[root_prod_pkg_key] === productPkgData[prod_pkg_key] && it[root_prod_pkg_key] !== it[prod_pkg_key];
        });
        let bomList = this.buildBomListBySubLinesList(productPkgData, subLinesList);
        let params = this.buildRequestParams(productPkgData, bomList, TriggerType.RECONFIGURATION, undefined, dataIndex);
        if(!params.accountId) return Promise.resolve();
        return this.requestApi.queryBomPrice(params)
            .then(bomPriceResult => {
                let {[bom_id]: bomId, [product_price]: pkgPrice} = productPkgData;
                let pkgBomResult = bomPriceResult && bomPriceResult.find(it => {
                    return it._id === bomId;
                });
                if (pkgBomResult) {
                    let orgPkgPrice = pkgPrice ? Number(pkgPrice) : 0;
                    let adjustPrice = pkgBomResult.adjust_price ? Number(pkgBomResult.adjust_price) : 0;
                    if (!equals(orgPkgPrice, adjustPrice)) {
                        let content = `objApiName: ${objApiName}, orgPkgPrice: ${orgPkgPrice}, adjustPrice: ${adjustPrice}, bomList: ${JSON.stringify(bomList)}`;
                        console.log('BomPriceQuerierPriceChanged:' + content);
                        log.kLog("sfa", 'BomPriceQuerierPriceChanged', content);
                    }
                }
            }).catch(err => {
                console.log('BomPriceQuerier checkPkgPrice err:' + err);
            })
    }

    getCalculateFields(fieldApiNames, objectDescribe = {}, filterCalcFields, toCalcFields) {
        let uniqField = function (fields) {
            let used = {};
            let arr = [];
            fields && fields.length && fields.forEach(it => {
                let fieldName = it.fieldName;
                let isUsed = used[fieldName];
                if (!isUsed) {
                    used[fieldName] = true;
                    arr.push(it);
                }
            });
            return arr;
        };
        let fields = fieldApiNames && fieldApiNames.length && fieldApiNames.map(it => {
            return objectDescribe.fields[it];
        });
        let calculateFields = Object.assign({}, toCalcFields || {});//要计算的字段
        //字段相关的计算字段
        each(fields, (field) => {
            if (!field || !field.calculate_relation) {
                return
            }
            each(field.calculate_relation.relate_fields, (calcFields, objectApiName) => {
                if (calcFields && calcFields.length) {
                    if (!calculateFields[objectApiName]) {
                        calculateFields[objectApiName] = [];
                    }
                    calculateFields[objectApiName].push(...calcFields);
                }
            });
        });
        //要过滤的字段及去重
        each(calculateFields, (calcFields, objectApiName) => {
            let filterFields = filterCalcFields && filterCalcFields[objectApiName];
            if (filterFields && filterFields.length) {
                calcFields = calcFields && calcFields.length && calcFields.filter(it => {
                    return !filterFields.includes(it.fieldName)
                })
            }
            calculateFields[objectApiName] = uniqField(calcFields);
        });
        return calculateFields;
    }

    parseBomData2DetailData(objApiName, subProduct) {
        return this.pluginApi.runPluginSync(emitEvent.bom_parseBomData2DetailData_sync, {
            objApiName,
            subProduct: Object.assign({}, subProduct, {
                key_select_sku_attribute_value: subProduct.selectedAttributeValues,
                key_select_sku_nonstandard_attribute_value: subProduct.nonstandardAttribute
            }),
        });
    }

    isTriggerQueryBomPriceAfterBatchAdd() {
        // let {share_rate} = this.detailFieldMapping;
        // let {objApiName, dataGetter} = this.options;
        // let detailDescribe = dataGetter.getDescribe(objApiName);
        // let shareRateField = detailDescribe && detailDescribe.fields && detailDescribe.fields[share_rate];
        // return !!shareRateField;//未开启分摊，不走query_bom_price
        //https://www.tapd.cn/20019471/bugtrace/bugs/view?bug_id=1120019471001359054
        let jsonString = this.pluginApi.getCloudCtrl("disableQueryBomPriceAfterBatchAdd");
        let result = jsonString && JSON.parse(jsonString);
        return !(result && result.disable);
    }

    parseBomParamsBefore(objectData) {
        let {objApiName} = this.options;
        return this.pluginApi.runPluginSync(emitEvent.bom_queryBomPrice_parseParams_before, {objApiName, objectData});
    }

    runQueryBomPriceEndEvent(triggerType, changedFieldsMap) {
        let param = Object.assign(this.options, {
            triggerType,
            changedFieldsMap,
        });
        return this.pluginApi.runPlugin(emitEvent.bom_queryBomPrice_end, param);
    }

    startTicker(triggerType) {
        return perfTick.startTicker({operationId: triggerType});
    }

    tickExtendOpt(extraInfo) {
        let {masterObjApiName, dataGetter} = this.options;
        let pageId = dataGetter.getPageId();
        return Object.assign({pageId, apiName: masterObjApiName, biz: "ava_object_form", module: 'sfa', subModule: "bom"}, extraInfo)
    }

    async triggerUIEvent(params, forceTrigger = false) {
        let jsonString = this.pluginApi.getCloudCtrl("triggerUIEventAfterQueryBomPrice");
        let triggerResult = jsonString && JSON.parse(jsonString);
        let enable = triggerResult && triggerResult.enable;
        if (!forceTrigger && !enable) {
            return;
        }
        if (isEmpty(params)) {
            return;
        }
        let isTriggerUIEvent = false;
        let {triggerType, addNewDataIndexs, changeDataIndex, changeFieldApiName, uiEventType} = params;
        let {masterObjApiName, objApiName, formApis} = this.options;
        let isMasterType = [TriggerType.COPY_MAPPING,
            TriggerType.CHANGE_MASTER_PRICE_BOOK,
            TriggerType.MATCH_PRICE_POLICY,
            TriggerType.CHANGE_MASTER_AVAILABLE_RANGE_FIELD].includes(triggerType);
        let objectApiName = isMasterType ? masterObjApiName : objApiName;
        let param = {objApiName: objectApiName, disableCal: true};
        if (triggerType === TriggerType.ADD) {
            isTriggerUIEvent = true;
            Object.assign(param, {newDataIndexs: addNewDataIndexs});//新增明细UI事件
        } else if ([TriggerType.CHANGE_MASTER_PRICE_BOOK,
            TriggerType.MODIFY_PACKAGE,
            TriggerType.MODIFY_SUB_PRODUCT,
            TriggerType.CHANGE_MASTER_AVAILABLE_RANGE_FIELD].includes(triggerType)) {
            isTriggerUIEvent = true;
            let isModifyDetail = [TriggerType.MODIFY_PACKAGE, TriggerType.MODIFY_SUB_PRODUCT].includes(triggerType)
            Object.assign(param, {//字段变更UI事件
                changeFields: [changeFieldApiName],
                triggerUiField: changeFieldApiName,
                uiChangedFields: [changeFieldApiName]
            }, isModifyDetail && {
                modifiedDataIndexs: [changeDataIndex],
            });
        } else if (triggerType === TriggerType.COPY_MAPPING) {
            isTriggerUIEvent = true;
            Object.assign(param, {uiEventType: 5})//页面加载UI事件
        } else if (triggerType === TriggerType.MATCH_PRICE_POLICY) {
            if (uiEventType === 'after') {
                isTriggerUIEvent = true;
                Object.assign(param, {uiEventType: 6})//价格政策执行后UI事件
            } else if (uiEventType === 'cancel') {
                isTriggerUIEvent = true;
                Object.assign(param, {uiEventType: 7})//价格政策取消后UI事件
            }
        }
        if (isTriggerUIEvent) {
            return formApis.triggerCalAndUIEvent(param);
        }
    }

    /*****************bom批量计算****************************/

    async batchTriggerWithModifiedDataIndexList(triggerType, modifiedDataIndexList, detailDataList, extraInfo) {
        if (!modifiedDataIndexList || !modifiedDataIndexList.length) {
            return;
        }
        if (!detailDataList || !detailDataList.length) {
            let {objApiName, dataGetter} = this.options;
            detailDataList = dataGetter.getDetail(objApiName);
        }
        let pkgDataList = detailDataList && detailDataList.length && detailDataList.filter(it => {
            let isPackage = bomUtil.isProductPackage(it);
            return !!(isPackage && modifiedDataIndexList.includes(it.dataIndex));
        })
        if (!pkgDataList || !pkgDataList.length) {
            return;
        }
        let pageId = this.options.dataGetter.getPageId();
        let token = 'bom_' + uuid();
        this.pluginApi.showSingletonLoading(token, {title: this.getCalculatingTip()}, pageId);
        let ticker = this.startTicker(triggerType);
        try {
            let {calcFilterFields} = extraInfo || {}
            let packageList = [];
            let length = pkgDataList.length;
            for (let i = 0; i < length; i += CALC_STEP_COUNT) {
                packageList.push(pkgDataList.slice(i, i + CALC_STEP_COUNT))
            }
            let batchCalcParams = [];
            packageList.forEach(packageArr => {
                let newBomMap = {};
                for (let i = 0, length = packageArr.length; i < length; i++) {
                    let pkgData = packageArr[i];
                    newBomMap[pkgData.dataIndex] = this.getNewBomData(pkgData, detailDataList, (triggerType === TriggerType.ADD) ? null : modifiedDataIndexList);
                }
                let params = this.batchBuildRequestParams(packageArr, triggerType, undefined, newBomMap);
                batchCalcParams.push(params);
            })
            await this.batchQueryBomPrice(triggerType, batchCalcParams, pkgDataList, {calcFilterFields})
            this.pluginApi.hideSingletonLoading(token, pageId);
            ticker.end(this.tickExtendOpt({pkgCount: length}));
            return true;
        } catch (err) {
            this.pluginApi.hideSingletonLoading(token, pageId);
        }
    }

    getNewBomData(pkgData, detailDataList, modifiedDataIndexList, updateField) {
        let {prod_pkg_key, root_prod_pkg_key, quantity, bom_id, subtotal, bom_core_id, bom_type, bom_version, price_book_discount} = this.detailFieldMapping;
        let {
            [bom_core_id]: coreId, [bom_type]: bomType, [`${bom_type}__v`]: bomTypeV, [bom_version]: nodeBomCoreVersion,
            [subtotal]: rootSubtotal, [quantity]: rootAmount, [bom_id]: rootBomId, [prod_pkg_key]: prodPkgKey, [price_book_discount]: priceBookDiscount
        } = pkgData;
        let subLinesList = detailDataList && detailDataList.filter(it => {
            return it[root_prod_pkg_key] === prodPkgKey && it[root_prod_pkg_key] !== it[prod_pkg_key];
        });
        let nodeBomCoreType = (bomTypeV || bomType);
        let bomList = this.buildBomListBySubLinesList(pkgData, subLinesList, modifiedDataIndexList, updateField);
        return {coreId, nodeBomCoreType, nodeBomCoreVersion, rootSubtotal, rootAmount, rootBomId, priceBookDiscount, bomList};
    }

    async batchQueryBomPrice(triggerType, batchCalcParams, batchCalcPkgDataList, opt) {
        if (!batchCalcParams || !batchCalcParams.length || !batchCalcPkgDataList || !batchCalcPkgDataList.length) {
            return;
        }
        let promiseList = batchCalcParams.map(param => {
            return this.requestApi.batchQueryBomPrice(param);
        })
        let {masterObjApiName, objApiName} = this.options;
        return Promise.all(promiseList).then(resultList => {
            let resultMap = {};
            resultList && resultList.length && resultList.forEach(result => {
                Object.assign(resultMap, result)
            })
            //逐个更新产品包、子产品，不触发计算
            batchCalcPkgDataList.forEach(async productPkgData => {
                let dataIndex = productPkgData.dataIndex;
                let result = resultMap[dataIndex];
                let updatePackageData = await this.handleQueryBomResult(productPkgData, result, false);
                let changedFields = updatePackageData ? Object.keys(updatePackageData) : [];
                fieldChangedRecorder.record(objApiName, dataIndex, changedFields);
            })
        }).then(async () => {
            let pkgDataIndexList = batchCalcPkgDataList.map(it => it.dataIndex);
            //触发计算包
            let calcPkgResult = await this.triggerCalcPackageData(null, batchCalcPkgDataList, triggerType, opt);
            fieldChangedRecorder.recordByCalcResult(masterObjApiName, objApiName, calcPkgResult, pkgDataIndexList);
            //触发计算子
            let calcSubResult = await this.triggerCalcSubLinesData(batchCalcPkgDataList, true, triggerType, opt);
            fieldChangedRecorder.recordByCalcResult(masterObjApiName, objApiName, calcSubResult, pkgDataIndexList);
            let changedFieldsMap = fieldChangedRecorder.getChangedFieldMap();
            fieldChangedRecorder.clear();
            return this.runQueryBomPriceEndEvent(triggerType, changedFieldsMap);
        }).catch(err => {
            this.pluginApi.showToast(err);
        })
    }

    batchBuildRequestParams(productPkgDataList = [], triggerType, triggerFieldName, newBomMap) {
        let {form_account_id, form_partner_id, mc_currency} = this.masterFieldMapping;
        let {price_book_id, subtotal} = this.detailFieldMapping;
        let {masterObjApiName, objApiName, dataGetter, seriesId} = this.options;
        let masterData = dataGetter.getMasterData();
        let {[form_account_id]: accountId, [form_partner_id]: partnerId, [mc_currency]: mcCurrency} = masterData;
        let detailDataList = dataGetter.getDetail(objApiName);
        let describe = dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let priceBookField = describe && describe.fields && describe.fields[price_book_id];
        let hasFilters = !!(priceBookField && priceBookField.wheres && priceBookField.wheres.length);
        let calculateArg = this.newCalculateArg(masterData, productPkgDataList);
        return {
            apiName: masterObjApiName, //主对象名称
            detailApiName: objApiName,//从对象的apiName
            accountId: accountId, //客户id
            mcCurrency: mcCurrency,
            partnerId: partnerId,//合作伙伴id
            //计算母件小计传给计算接口的参数（修改子件传此参数，其他情况不传）
            calculateArg: (triggerFieldName === subtotal || triggerType === TriggerType.MATCH_PRICE_POLICY) ? undefined : calculateArg,
            objectData: masterData,
            seriesId,
            details: simplifyDetails(hasFilters, {[objApiName]: detailDataList}),
            switchMasterPriceBook: triggerType === TriggerType.CHANGE_MASTER_PRICE_BOOK,
            noCalPrice: false,//不计算价格
            newBomMap
        }
    }

    enableBatchQueryBomPrice(context) {
        let jsonString = context && context.pluginApi && context.pluginApi.getCloudCtrl("enableBatchQueryBomPrice");
        let cloudResult = jsonString && JSON.parse(jsonString);
        let enable = cloudResult && cloudResult.enable;
        return !!enable;
    }
}

export default new BomPriceQuerier();