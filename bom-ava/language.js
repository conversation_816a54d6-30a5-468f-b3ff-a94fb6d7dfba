const info = {
    "ava.object_form.calculating": "计算中",
    "ava.object_form.salesOrderProduct.decimal_invalid": "产品的数量小数位越界，请重新修改",
    "ava.object_form.bom.expand_tip": "展开多级产品结构",
    "ava.object_form.bom.collapse_tip": "收起多级产品结构",
    "ava.object_form.onsale.bom_group": "分组",
    "ava.object_form.onsale.bom_config": "配置",
    "ava.object_form.onsale.bom.collapse": "收起多级",
    "ava.object_form.onsale.bom.expand": "展开多级",
    "ava.object_form.cpq.require_field_empty_tip": "该产品数据有误，请检查后重新提交",
    "ava.object_form.cpq.require_field_empty_tip1": "产品{0}数据有误，请检查后重新提交",
    "ava.object_form.crm.yes": "是",
    "ava.object_form.cpq.select_sub_product_tip": "子产品不允许单独选择",
    "ava.object_form.cpq.select_package_tip":"请选择一条产品包数据",
    "ava.object_form.cpq.amount_origin_subBom_decimal_invalid":"单包产品数量为{0}，【 {1} 】产品的数量小数位越界，请重新修改",
    "ava.object_form.cpq.amount_origin_subBom_maxAmount_invalid":"一个【{0}】内子件【{1}】最大数量为{2}，请重新修改",
    "ava.object_form.cpq.amount_origin_subBom_minAmount_invalid":"一个【{0}】内子件【{1}】最小数量为{2}，请重新修改",
    "ava.object_form.cpq.amount_auto_round":"【{0}】数量的小数位已四舍五入",
    "ava.object_form.cpq.amount_increment_multiple": "数量应该为增减数量幅度({0}）的整数倍，当前一个产品组合内子件数量为{1}，校验不通过",
    "ava.object_form.onsale.bom.view_all_sub_products": "查看全部子产品",
    "cml.crm.loading": "加载中"
}

//固定格式，fssub依赖 -start
module.exports = {
    info
}
//固定格式，fssub依赖 -end