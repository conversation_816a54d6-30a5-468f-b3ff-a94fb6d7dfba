import sass from 'rollup-plugin-sass';
import babel, { getBabelOutputPlugin } from '@rollup/plugin-babel';

export default {
    input: 'src/index.js',
    output: {
        name: 'Resource',
        file: 'dist/index.js',
        format: 'cjs',
        plugins: [getBabelOutputPlugin({ presets: ['@babel/preset-env'] })]
    },
    plugins: [
        babel({
            presets: ['@babel/preset-env']
        }),
        sass({
            insert: true
        })
    ]
}
