import date_utils from './date_utils';
import {
    $,
    createSVG,
    animateSVG,
} from './svg_utils';

export default class Bar {
    constructor(gantt, task) {
        this.gantt = gantt;
        this.task = task;
        this.prepare();
        this.draw();
        this.bind();
    }

    prepare () {
        this.prepare_values();
        this.prepare_helpers();
    }

    prepare_values () {
        this.height = this.gantt.options.bar_height;
        this.x = this.compute_x();
        this.y = this.compute_y();
        this.corner_radius = this.gantt.options.bar_corner_radius;
        this.corner_work_radius = this.gantt.options.bar_work_corner;
        this.duration =
            date_utils.diff(this.task._end, this.task._start, 'hour') /
            this.gantt.options.step;
        this.width = this.gantt.options.column_width * this.duration;
        this.progress_width =
            this.gantt.options.column_width *
            this.duration *
            (this.task.progress / 100) || 0;
        this.sum_working_hours = this.gantt.resource.selectWorking === 'actual_resource' ? 'sum_actual_working_hours' : 'sum_working_hours';
        this.isTaskMode = this.gantt.resource.selectDimension === 'task_mode' ? true : false;
        this.group = createSVG('g', {
            class: 'bar-wrapper ' + (this.task.custom_class || ''),
            'data-id': this.task.id
        });
        this.bar_group = createSVG('g', {
            class: 'bar-group',
            append_to: this.group
        });
        this.otherBarProgress = '';
        this.otherBar = '';
        switch (this.task.tasktype) {
            case 'son':
                this.otherBar = !this.task._is_actual_bar ? 'bar-line' : 'bar-fill';
                break;
            case 'roottask':
                this.otherBar = !this.task._is_actual_bar ? 'bar-line' : 'bar-fill';
                break;
            case 'stage':
                this.otherBarProgress = 'stageBar-progress';
                this.otherBar = 'stageBar';
                this.height = this.gantt.options.stageBarHeight
                break;
            default:
                break;
        }
    }

    prepare_helpers () {
        SVGElement.prototype.getX = function () {
            return +this.getAttribute('x');
        };
        SVGElement.prototype.getY = function () {
            return +this.getAttribute('y');
        };
        SVGElement.prototype.getWidth = function () {
            return +this.getAttribute('width');
        };
        SVGElement.prototype.getHeight = function () {
            return +this.getAttribute('height');
        };
        SVGElement.prototype.getEndX = function () {
            return this.getX() + this.getWidth();
        };
    }
    draw () {
        //绘制项目/人员rect
        if (this.task.type && this.task.type !== 128 || this.isTaskMode) {
            this.draw_project_rect();
        } else if(this.task.isWorkHour) {
            this.draw_work_hour_rect();
        } else {
            this.draw_bar();
        }
    }

    draw_bar () {
        const _this = this;
        const work_hours = this.calcule_x(this.task.working_time_aggregate);
        let row_width = this.gantt.options.column_width - 0.6;
        let rx = this.corner_radius;
        let ry = this.corner_radius;
        let minDate = this.task.startDate;
        let maxDate = this.task.endDate;
        let rx_work = this.corner_work_radius;
        let ry_work = this.corner_work_radius;
        if(this.gantt.resource.selectWorking === 'planed_and_actual') return this.draw_planed_actual_rect(work_hours, 'task');
        for (let [index, task] of new Map(work_hours.map((task, i) => [i, task]))) {
            let text_left = 0;
            let scale = Number(task[_this.sum_working_hours] / this.task.working_hours_threshold);
            let max_len = CRM.util.calculateTimeBetween(task.working_time_start, maxDate, 'Days');
            let min_len = CRM.util.calculateTimeBetween(minDate, task.working_time_end, 'Days');
            const work_hour_labels = _this.slice_work_hour_width(task[_this.sum_working_hours]);
            const row_col_width = _this.compute_work_hours_width(task, row_width);
            let current_col_width = row_width;
            let min_width = this.gantt.options.column_width * min_len + row_width;
            let max_width = this.gantt.options.column_width * max_len;
            let distance_left = row_col_width - min_width;
            let text_len = work_hour_labels[0].label_len;
            if(row_col_width > row_width) {
                if(task.x < 0) {
                    if(task.working_time_end <= maxDate) {
                        current_col_width = min_width;
                        text_left = distance_left + min_width/2 - String(text_len).length/2;
                    }else {
                        const distance_middle = CRM.util.calculateTimeBetween(minDate, maxDate, 'Days');
                        const distance_width =  this.gantt.options.column_width * distance_middle;
                        current_col_width = distance_width;
                        text_left = distance_left + distance_width/2 - String(text_len).length/2;
                    }
                }else if(task.working_time_end > maxDate){
                    current_col_width = max_width;
                    text_left = max_width / 2 - String(text_len).length/2;
                }else {
                    distance_left = 0;
                }
            }else {
                text_left = 0;
                distance_left = 0;
            }
            const dark_red_rect_width = text_left ? current_col_width : row_col_width;
            const className = scale >= 1 ? 'bar-def-red-group' : '';
            this[`$bar_group_${index}`] = createSVG('g', {
                class: `bar-def-group ${className}`,
                append_to: this.bar_group
            });
            createSVG('rect', {
                x: task.x,
                y: this.y,
                width: row_col_width,
                height: this.height,
                rx: rx,
                ry: ry,
                fill: scale >= 1 ? this.gantt.isDefault ? "#ffeeea" : '#FFF5F0' : "#eaf7ed",
                // class: 'bar',
                class: 'bar-resource',
                append_to: this[`$bar_group_${index}`]
            });
            this[`$bar_work_hour_${index}`] = createSVG('g', {
                class: 'bar-def-work-group',
                append_to: this[`$bar_group_${index}`]
            });
            createSVG('rect', {
                x: task.x + distance_left + dark_red_rect_width / 3,
                y: this.y + 2,
                width: dark_red_rect_width - dark_red_rect_width / 3 * 2,
                height: this.height - 4,
                rx: rx_work,
                ry: ry_work,
                class: `bar bar-hour ${!this.gantt.isDefault ? 'dark-red' : ''}`,
                append_to: this[`$bar_work_hour_${index}`]
            });
            createSVG('text', {
                x: Math.ceil(task.x + (text_left || row_col_width / 2 - work_hour_labels[0].str.length / 2)),
                y: this.y + 2 + 2 + 14 / 2,
                innerHTML: work_hour_labels[0].label_len || 0,
                fill: this.gantt.isDefault ? '#2A304D' : scale >= 1 ? '#fff' : '#2A304D',
                fullText: work_hour_labels[0].str,
                class: 'bar-text-label work',
                append_to: this[`$bar_work_hour_${index}`]
            });
            animateSVG(this[`$bar_group_${index}`], 'width', 0, this.width);
        }

    }
    //绘制项目/人员甘特视图
    draw_project_rect () {
        const _this = this;
        const _index = this.task._index;
        let minDate = this.task.startDate;
        let maxDate = this.task.endDate;
        const work_hours = this.calcule_x(this.task['working_time_aggregate']);
        const row_height = this.gantt.options.bar_height + this.gantt.options.padding;
        let row_y = (this.gantt.options.header_height + this.gantt.options.padding / 2 + 0.4) + _index * row_height;
        let row_width = this.gantt.options.column_width - 1;
        if(this.gantt.resource.selectWorking === 'planed_and_actual') return this.draw_planed_actual_rect(work_hours);
        // let id = `id${_.uniqueId('linearId')}`;
        for (let [index, task] of new Map(work_hours.map((task, i) => [i, task]))) {
            let text_left = 0;
            let id = `${this.task.id}${index}_work`;
            let scale = Number(task[_this.sum_working_hours] / this.task.working_hours_threshold);
            let lignt_green_precent = (1 - scale) * 100;
            let max_len = CRM.util.calculateTimeBetween(task.working_time_start, maxDate, 'Days');
            let min_len = CRM.util.calculateTimeBetween(minDate, task.working_time_end, 'Days');
            const work_hour_labels = _this.slice_work_hour_width(task[_this.sum_working_hours]);
            const row_col_width = _this.compute_work_hours_width(task, row_width);
            let min_width = this.gantt.options.column_width * min_len + row_width;
            let max_width = this.gantt.options.column_width * max_len;
            let distance_left = row_col_width - min_width;
            let text_len = work_hour_labels[0].label_len;
            if(row_col_width > row_width) {
                if(task.x < 0) {
                    if(task.working_time_end <= maxDate) {
                        text_left = distance_left + min_width/2 - String(text_len).length/2;
                    }else {
                        const distance_middle = CRM.util.calculateTimeBetween(minDate, maxDate, 'Days');
                        const distance_width =  this.gantt.options.column_width * distance_middle;
                        text_left = distance_left + distance_width/2 - String(text_len).length/2;
                    }
                }else if(task.working_time_end > maxDate){
                    text_left = max_width / 2 - String(text_len).length/2;
                }
            }else {
                text_left = 0;
            }
            this[`$bar_group_${index}`] = createSVG('g', {
                class: 'bar-def-group',
                append_to: this.bar_group
            });
            this[`$defs_${index}`] = createSVG('linearGradient', {
                id: id,
                x1: "0%",
                y1: "0%",
                x2: "0%",
                y2: "100%",
                class: `linearGradient`,
                append_to: this[`$bar_group_${index}`]
            });
            createSVG('stop', {
                offset: scale >= 1 ? "100%" : `${lignt_green_precent}%`,
                'stop-color': scale >= 1 ? this.gantt.isDefault ? "#FDA795" : '#FFF5F0' : "#EAF7ED",
                class: 'stop1',
                append_to: this[`$defs_${index}`]
            });
            createSVG('stop', {
                offset: scale >= 1 ? "100%" : "0%",
                'stop-color': "#98D9A4",
                class: 'stop2',
                append_to: this[`$defs_${index}`]
            });
            createSVG('rect', {
                width: row_col_width -  (this.isTaskMode ? 1.2 : 0),
                x: this.isTaskMode ? task.x + 0.8 : task.x,
                y: row_y,
                fill: this.isTaskMode ? '#fff' : `url(#${id})`,
                height: row_height - 1,
                class: 'linear-rect',
                append_to: this[`$bar_group_${index}`]
            });
            createSVG('text', {
                x: task.x + (text_left || row_col_width / 2 - work_hour_labels[0].str.length / 2),
                y: row_y + row_height / 2 + 2,
                innerHTML: work_hour_labels[0].label_len || 0,
                fullText: work_hour_labels[0].str,
                fill: scale >= 1 ? this.gantt.isDefault ? '#2A304D' : '#FF522A' : '#2A304D',
                class: 'bar-text-label',
                append_to: this[`$bar_group_${index}`]
            });
        }
    }
    //绘制实际/计划占比视图
    draw_planed_actual_rect(working_time_aggregate, isTask) {
        const _this = this;
        const _index = this.task._index;
        let minDate = this.task.startDate;
        let maxDate = this.task.endDate;
        const row_width = this.gantt.options.column_width - 1.2;
        const row_height = this.gantt.options.bar_height + this.gantt.options.padding;
        const row_y = (this.gantt.options.header_height + this.gantt.options.padding / 2) + _index * row_height;
        const task_y = this.isTaskMode ? row_y + 1 : isTask ? this.y : row_y + 0.4;
        const task_h = this.isTaskMode ? row_height : isTask ? this.height : row_height;
        for (let [index, task] of new Map(working_time_aggregate.map((task, i) => [i, task]))) {
            let text_left = 0;
            const actual = task['sum_actual_working_hours'];
            const plan = task['sum_working_hours'];
            const isOver = actual > plan;
            let max_len = CRM.util.calculateTimeBetween(task.working_time_start, maxDate, 'Days');
            let min_len = CRM.util.calculateTimeBetween(minDate, task.working_time_end, 'Days');
            const work_hour_labels = _this.slice_work_hour_width([actual, plan]);
            let row_col_width = _this.compute_work_hours_width(task, row_width);
            let min_width = this.gantt.options.column_width * min_len + row_width;
            let max_width = this.gantt.options.column_width * max_len;
            let distance_left = row_col_width - min_width;
            let text_len = `${work_hour_labels[0].label_len}/${work_hour_labels[1].label_len}`;
            if(row_col_width > row_width) {
                if(task.x < 0) {
                    if(task.working_time_end <= maxDate) {
                        text_left = distance_left + min_width/2 - String(text_len).length/2;
                    }else {
                        const distance_middle = CRM.util.calculateTimeBetween(minDate, maxDate, 'Days');
                        const distance_width =  this.gantt.options.column_width * distance_middle;
                        text_left = distance_left + distance_width/2 - String(text_len).length/2;
                    }
                }else if(task.working_time_end > maxDate){
                    text_left = max_width / 2 - String(text_len).length/2;
                }
            }else {
                text_left = 0;
            }
            this[`$bar_compare_${index}`] = createSVG('g', {
                class: 'bar-def-group',
                append_to: this.bar_group
            });
            createSVG('rect', {
                width: row_col_width,
                x: task.x,
                y: task_y,
                fill: isOver ? '#FFF5F0' : '#fff',
                height: task_h - 1,
                class: 'linear-rect',
                append_to: this[`$bar_compare_${index}`]
            });
            this[`$bar_text_${index}`] = createSVG('text', {
                x: task.x + (text_left || row_col_width/2),
                y: task_y + task_h / 2,
                fullText: `${work_hour_labels[0].str}/${work_hour_labels[1].str}`,
                class: 'bar-text-label',
                'text-anchor': true,
                'dominant-baseline': "middle",
                append_to: this[`$bar_compare_${index}`]
            });
            createSVG('tspan', {
                innerHTML: work_hour_labels[0].label_len || 0,
                fill: isOver ? '#FF522A' : '#2A304D',
                append_to: this[`$bar_text_${index}`]
            });
            createSVG('tspan', {
                innerHTML: '/'+ work_hour_labels[1].label_len || 0,
                fill: '#2A304D',
                append_to: this[`$bar_text_${index}`]
            });
        }
    }
    //绘制工时记录
    draw_work_hour_rect() {
        const _index = this.task._index;
        let tick_x = 0;
        let minDate = this.task.dates[0];
        let maxDate = this.task.dates[1];
        const row_height = this.gantt.options.bar_height + this.gantt.options.padding;
        let row_y = (this.gantt.options.header_height + this.gantt.options.padding / 2) + _index * row_height;
        const work_hours = this.calcule_x(this.task['working_time_aggregate']);
        const {Add, Edit} = this.gantt.options.objectPermission || {};
        this.$bar_hour_group = createSVG('g', {
            class: 'bar-hour-group',
            taskId: this.group.getAttribute('data-id'),
            append_to: this.bar_group
        });
        this.$bar_hour_gray_group = createSVG('g', {
            class: 'bar_hour_gray_group',
            taskId: this.group.getAttribute('data-id'),
            append_to: this.bar_group
        });

        for (let [d, date] of new Map(this.gantt.dates.map((date, d) => [d, date]))) {
            let row_width = this.gantt.options.column_width - 0.6;
            let start_date = date.getTime();
            this[`$bar_input_group_${d}`] = createSVG('foreignObject', {
                x: tick_x,
                y: row_y,
                fill: "#fff",
                width: row_width,
                height: row_height,
                startDate: start_date,
                endDate: start_date,
                class: 'bar-work-input',
                append_to: this.$bar_hour_group
            });
            this[`$bar_input_group_${d}`].innerHTML = `<input type="text" class="work-text ${Add ? '' : 'cols-text'}" style="width: ${row_width - (Add ? 2.2 : 3)}px;height: ${row_height - 3}px;text-align:center" value=""/>`;
            this[`$bar_input_group_${d}`].querySelector('.work-text').disabled = Add ? false : true;
            tick_x += this.gantt.options.column_width;
        }
        for (let [index, period] of new Map(work_hours.map((period, i) => [i, period]))) {
            let work_len = period.dataList?.length;
            let row_width = this.gantt.options.column_width - 0.6;
            let input_val = Number(period.workingHours);
            let date_len = CRM.util.calculateTimeBetween(period.startDate, period.endDate, 'Days');
            let max_len = CRM.util.calculateTimeBetween(period.startDate, maxDate, 'Days');
            let min_len = CRM.util.calculateTimeBetween(minDate, period.endDate, 'Days');
            const isflow = period.dataList && (period.dataList[0].life_status === 'under_review' || period.dataList[0].life_status === 'in_change');
            let input_w = (!Edit || isflow) ? 3 : 2;
            if (work_len > 1 || date_len || period.new) {
                let work_width = date_len ? (this.gantt.options.column_width * date_len + row_width) : row_width;
                let max_width = this.gantt.options.column_width * max_len;
                let min_width = this.gantt.options.column_width * min_len + row_width;
                let distance_left = work_width - min_width;
                let text_left = 0;

                if(period.x < 0) {
                    if(period.endDate <= maxDate) {
                        text_left = distance_left + min_width/2 - String(input_val).length/2;
                    }else {
                        const distance_middle = CRM.util.calculateTimeBetween(minDate, maxDate, 'Days');
                        const distance_width =  this.gantt.options.column_width * distance_middle;
                        text_left = distance_left + distance_width/2 - String(input_val).length/2;
                    }
                }else if(period.endDate > maxDate){
                    text_left = max_width / 2 - String(input_val).length/2;
                }
                this[`$bar_hour_group_${index}`] = createSVG('foreignObject', {
                    x: period.x,
                    y: row_y + 0.6,
                    width: work_width - 1,
                    workingHours: input_val,
                    height: row_height - 1,
                    startDate: period.startDate,
                    endDate: period.endDate,
                    class: 'bar-hour-input',
                    append_to: this.$bar_hour_gray_group
                });
                this[`$bar_hour_group_${index}`].innerHTML = `<div class="text-wrapper"><input type="text" class="cols-text work-text" disabled="true" style="width: ${work_width-input_w}px;height: ${row_height-2}px;padding-left:${text_left}px;text-align:${text_left ? text_left : 'center'}" value="${input_val}"/><span class="fx-icon-f-approval flow-icon ${isflow ? 'flow' : ''}"></span></div>`;
            }
            else {
                this[`$bar_hour_group_${index}`] = createSVG('foreignObject', {
                    x: period.x,
                    y: row_y,
                    fill: '#fff',
                    width: row_width,
                    height: row_height,
                    startDate: period.startDate,
                    endDate: period.endDate,
                    class: 'bar-hour-input',
                    append_to: this.$bar_hour_group
                });
                this[`$bar_hour_group_${index}`].innerHTML = `<div><input type="text" class="work-text ${!Edit || isflow ? 'cols-text' : ''}" style="width: ${row_width - input_w}px;height: ${row_height-3}px;text-align:center" value="${input_val}"/><span class="fx-icon-f-approval flow-icon ${isflow ? 'flow' : ''}"></span></div>`;
                input_val && (this[`$bar_hour_group_${index}`].querySelector('.work-text').disabled = (!Edit || isflow) ? true : false);
            }
        };
    }

    //多位数字溢出处理
    slice_work_hour_width(values) {  //[actual, plan]
        if (!Array.isArray(values)) {
            values = [values];
        }

        return values.map(value => {
            const str = (value || 0).toString();
            const label_len = str.length > 3 ? str.slice(0, 3) + '...' : str;
            return {
                str,
                label_len,
            };
        });
    }
    //计算合并工时宽度
    compute_work_hours_width(task,rowWidth) {
        let date_len = CRM.util.calculateTimeBetween(task.working_time_start, task.working_time_end, 'Days');
        let row_col_width = date_len ? rowWidth + date_len*this.gantt.options.column_width: rowWidth;
        return row_col_width;
    }
    calcule_x (hours) {
        const _this = this;
        const {
            step,
            column_width
        } = this.gantt.options;
        const arr = hours.map(item => {
            let start_date = new Date(new Date(item.start).setHours(0, 0, 0));
            const gantt_start = _this.gantt.gantt_start;

            const diff = date_utils.diff(start_date, gantt_start, 'hour');
            item.x = diff / step * column_width;
            return item;
        });
        return arr;
    }

    bind () {
        this.task.isWorkHour && this.setup_click_event();
        !this.isDefault && this.bind_events();
    }

    bind_events() {
        const _this = this;
        this.bar_group.querySelectorAll('.bar-text-label').forEach((item,index) => {
            $.on(item, this.gantt.options.popup_trigger, e => {
                _this.show_work_popup(e);
            });
            $.on(item, this.gantt.options.popup_off, e => {
                _this.gantt.hide_popup('label');
            });
        });
    }
    setup_click_event () {
        const _this = this;
        this.$bar_hour_gray_group.querySelectorAll('.bar-hour-input').forEach((item,index) => {
            $.on(item, this.gantt.options.popup_trigger, e => {
                _this.show_popup(item);
                _this.gantt.unselect_all();
                item.classList.add('active');
            });
            $.on(item, this.gantt.options.popup_off, e => {
                const isPopup = e.toElement.classList.contains('popup-wrapper-time');
                if(isPopup) return;  //鼠标在弹窗上不隐藏提示框
                _this.gantt.hide_popup('time');
                _this.gantt.unselect_all(item);
            });
        })
        $.on(this.bar_group, 'click', e => {
            if(e.target.classList.contains('work-text')) {
                e.target.addEventListener('input', function(event) {
                    event.stopPropagation();
                    _this.changeInput(event);
                    return;
                });
                e.target.addEventListener('blur', function(event) {
                    event.stopPropagation();
                    event.stopPropagation();
                    var totalValue = 0;
                    var currentInput = event.target;
                    var pElementl =  currentInput.parentNode.matches('foreignObject') ? currentInput.parentNode : currentInput.parentNode.parentNode;
                    var gElement = pElementl.parentNode;
                    var start = pElementl.getAttribute('startDate');
                    var current_val = Number(currentInput.value);
                    var current_id = gElement.getAttribute('taskId');
                    // 遍历<g>下的所有foreignObject
                    var foreignObjects = gElement.parentNode.querySelectorAll('foreignObject');
                    foreignObjects.forEach(function(foreignObject) {
                        var inputInForeignObject = foreignObject.querySelector('.work-text');
                        if (inputInForeignObject) {
                            var value = parseFloat(inputInForeignObject.value);
                            if (!isNaN(value)) {
                                totalValue += value;
                            }
                        }
                    });
                    //更新整行的工时记录
                    _this.gantt.options.timeSheetObj.updateTaskWorking(current_id, current_val, totalValue, start);
                });
            };
        });
        $.off(this.bar_group, 'click');
    }

    //获取当前工时单元格
    getCurrentBar(e) {
        const distance = e.x - 361 + 0.6;
        const scroll_left = $(".resource-gantt-container").scrollLeft;
        const works_wrapper = e.target.querySelectorAll('foreignObject');
        const cur_work = ([...works_wrapper].filter(item => {
            const min_x = Number(item.getAttribute('x')) - scroll_left;
            const max_x = Number(item.getAttribute('width')) + min_x;
            return distance >= min_x && distance <= max_x
        }) || [])[0];
        return cur_work;
    }

    changeInput(e) {
        const value = CRM.util.trim(e.target.value);
        const regex = /^(0(\.\d+)?|[1-9](\.\d+)?|1[0-9](\.\d+)?|2[0-3](\.\d+)?|24(\.0+)?)$/;
        if(value && !regex.test(value)) {
            CRM.util.remind(3, $t('enter_value_between'))
            e.target.focus();
            e.target.value = '';
        }
    }
    show_popup (cur_work) {
        const  scroll_left = $(".resource-gantt-container").scrollLeft;
        let start_milli = cur_work.getAttribute('startDate');
        let end_milli = cur_work.getAttribute('endDate');
        let start_date = FS.moment.unix(start_milli / 1000).format("YYYY-MM-DD");
        let end_date = FS.moment.unix(end_milli / 1000).format("YYYY-MM-DD");
        const date_range = start_date + '－' + end_date;
        const work_hours = cur_work.getAttribute('workingHours');
        let top = cur_work.getBoundingClientRect().bottom - 5;
        const min = Number(cur_work.getAttribute('x')) - scroll_left + 361;
        let max =  Number(cur_work.getAttribute('width')) + min;
        let days = CRM.util.calculateTimeBetween(Number(start_milli), this.task.dates[1], 'Days');
        let maxDate = days * this.gantt.options.column_width + min;
        let left = maxDate <= max ? maxDate-30 : max-30;
        this.gantt.show_popup('time', {
            target_element:  cur_work,
            dates: date_range,
            workHours: work_hours || 0,
            position: 'follow',
            position_data: {
                left,
                top: top
            },
            data: {
                task: this.task
            }
        });
        
    }
    show_work_popup(e) {
        const cur_label = e.target;
        const text = cur_label.getAttribute('fullText') || '';
        const top = cur_label.getBoundingClientRect().bottom - 10;
        const left = cur_label.getBoundingClientRect().right - 5;
        if(text.length > 2) {
            this.gantt.show_popup('label', {
                target_element: cur_label,
                position: 'follow',
                label: text,
                timeSheet: true,
                position_data: {
                    left,
                    top: top
                }
            });
        }

    }
    remove_status_handles (isChangeLeft) {
        if (!isChangeLeft && this.$bar_label) {
            this.$bar_label.remove();
            this.draw_label();
        };
    }
    date_changed (isHasFill, dragObj) {
        let changed = false;
        const {
            new_start_date,
            new_end_date
        } = this.compute_start_end_date();
        this.task.isDragProgress = (this.$bar.classList.contains('bar-fill') && this.$bar.classList.contains('light-progress')) ? false : true;
        if (Number(this.task._start) !== Number(new_start_date)) {
            changed = true;
            this.task._start = new_start_date;
        }

        if (Number(this.task._end) !== Number(new_end_date)) {
            changed = true;
            this.task._end = new_end_date;
        }
        CRM.util.waiting(false);
    }
    progress_changed () {
        const new_progress = this.compute_progress();
        this.task.progress = new_progress;
        this.gantt.trigger_event('progress_change', [this.task, new_progress]);
    }

    compute_start_end_date () {
        const bar = this.$bar;
        const x_in_units = bar.getX() / this.gantt.options.column_width;
        const new_start_date = date_utils.add(
            this.gantt.gantt_start,
            x_in_units * this.gantt.options.step,
            'hour'
        );
        let width_in_units = bar.getWidth() / this.gantt.options.column_width;
        let new_end_date = date_utils.add(
            new_start_date,
            width_in_units * this.gantt.options.step,
            'hour'
        );
        return {
            new_start_date,
            new_end_date
        };
    }

    compute_progress () {
        const progress =
            this.$bar_progress.getWidth() / this.$bar.getWidth() * 100;
        return parseInt(progress, 10);
    }

    compute_x () {
        const {
            step,
            column_width
        } = this.gantt.options;
        const task_start = this.task._start;
        const gantt_start = this.gantt.gantt_start;

        const diff = date_utils.diff(task_start, gantt_start, 'hour');
        let x = diff / step * column_width;

        if (this.gantt.view_is('Month')) {
            const diff = date_utils.diff(task_start, gantt_start, 'day');
            x = diff * column_width / 30;
        }
        return x;
    }

    compute_y () {
        return (
            this.gantt.options.header_height +
            this.gantt.options.padding +
            this.task._index * (this.height + this.gantt.options.padding)
        );
    }

    update_attr (element, attr, value) {
        value = +value;
        if (!isNaN(value)) {
            element.setAttribute(attr, value);
        }
        return element;
    }
}