import date_utils from './date_utils';
import {
    $,
    createSVG,
    setInlineStyles,
    addScrollbarStyles
} from './svg_utils';
import Bar from './bar';
import Popup from './popup';

import './resource.scss';

const VIEW_MODE = {
    QUARTER_DAY: 'Quarter Day',
    HALF_DAY: 'Half Day',
    DAY: 'Day',
    WEEK: 'Week',
    MONTH: 'Month',
    YEAR: 'Year'
};

export default class Resource {
    constructor(wrapper, tasks, options) {
        this.tasks = tasks;
        this.options = options;
        this.container = wrapper?.length && wrapper[0];
        this.resource = options.projectresourceobj || {};
        this.clear();
        this.prepare_values();
        this.setup_wrapper();
        this.setup_options();
        this.setup_tasks(this.tasks);
        this.setup_layers();
        this.change_view_mode();
    }

    prepare_values () {
        this.popup_wrapper = {};
        this.popup = {};
        this.isDefault = this.resource.options?.isDialog || this.options.isWorkHour;
    }

    setup_wrapper () {
        this.$svg = createSVG('svg', {
            append_to: this.container,
            class: 'project-resource-gantt'
        });
        this.container.appendChild(this.$svg);
        this.setElementStyle(this.container);
    }

    //添加wrapper样式
    setElementStyle(element) {
        setInlineStyles(element, {
            position: 'relative',
            overflow: 'hidden',
            overflowX: 'auto',
            fontSize: '12px',
            height: '100%'
        });
        //滚动条的样式
        addScrollbarStyles(element);
    }
    
    setup_options () {
        const options = this.options;
        const default_options = {
            header_height: 53,
            column_width: options.column_width || 66,
            bar_height: 20,
            bar_corner_radius: 3,
            bar_work_corner: 8,
            padding: 18,
            step: 24,
            view_mode: "Day",
            date_format: "YYYY-MM-DD",
            popup_trigger: "mouseenter",
            popup_off: "mouseleave",
            stageBarHeight: 6,
            language: "zh",
            on_click: function (task) {
                return;
            },
            setZindex: function (sourceElement, type) {
                sourceElement.classList[type === "top" ? "add" : "remove"](
                    "arrow-hover"
                );
            }
        };
        this.options = { ...default_options, ...options };
    }

    setup_tasks (tasks) {
        let cacheIndex = {};
        let index = 0;

        // prepare tasks
        this.tasks = tasks.map((task, i) => {
            let _index = index;

            if (cacheIndex[task.id]) {
                _index = cacheIndex[task.id];
            } else {
                cacheIndex[task.id] = _index;
                index++;
            }

            // convert to Date objects
            task._start = date_utils.parse(task.start);
            task._end = date_utils.parse(task.end);

            // make task invalid if duration too large
            if (date_utils.diff(task._end, task._start, 'year') > 10) {
                task.end = null;
            }

            // cache index
            task._index = _index;

            // invalid dates
            if (!task.start && !task.end) {
                const today = date_utils.today();
                task._start = today;
                task._end = date_utils.add(today, 2, 'day');
            }

            if (!task.start && task.end) {
                task._start = date_utils.add(task._end, -2, 'day');
            }

            if (task.start && !task.end) {
                task._end = date_utils.add(task._start, 2, 'day');
            }

            // if hours is not set, assume the last day is full day
            // e.g: 2018-09-09 becomes 2018-09-09 23:59:59
            const task_end_values = date_utils.get_date_values(task._end);
            if (task_end_values.slice(3).every(d => d === 0)) {
                task._end = date_utils.add(task._end, 24, 'hour');
            }

            // invalid flag
            if (!task.start || !task.end) {
                task.invalid = true;
            }

            // dependencies
            if (typeof task.dependencies === 'string' || !task.dependencies) {
                let deps = [];
                if (task.dependencies) {
                    deps = task.dependencies
                        .split(',')
                        .map(d => d.trim())
                        .filter(d => d);
                }
                task.dependencies = deps;
            }

            // uids
            if (!task.id) {
                task.id = generate_id(task);
            }

            return task;
        });
    }

    change_view_mode (mode = this.options.view_mode) {
        this.tasks.length && this.setup_dates();
        this.render();
    }

    setup_dates () {
        this.setup_gantt_dates();
        this.setup_date_values();
    }

    setup_gantt_dates () {
        const dates = this.options.dates;
        const start = this.tasks.length ? this.tasks[0].start : FS.moment.unix(dates[0] / 1000).format("YYYY-MM-DD");
        const end = this.tasks.length ? this.tasks[0].end : FS.moment.unix(dates[1] / 1000).format("YYYY-MM-DD");
        this.gantt_start = this.gantt_end = null;
        this.gantt_start = date_utils.start_of(new Date(start), 'day');
        this.gantt_end = date_utils.start_of(new Date(end), 'day');
    }
    setup_date_values () {
        this.dates = [];
        let cur_date = null;

        while (cur_date === null || cur_date < this.gantt_end) {
            if (!cur_date) {
                cur_date = date_utils.clone(this.gantt_start);
            } else {
                if (this.view_is(VIEW_MODE.YEAR)) {
                    cur_date = date_utils.add(cur_date, 1, 'year');
                } else if (this.view_is(VIEW_MODE.MONTH)) {
                    cur_date = date_utils.add(cur_date, 1, 'month');
                } else {
                    cur_date = date_utils.add(
                        cur_date,
                        this.options.step,
                        'hour'
                    );
                }
            }
            this.dates.push(cur_date);
        }
    }

    render () {
        this.make_grid();
        this.make_bars();
        this.set_width();
    }

    setup_layers () {
        this.layers = {};
        const layers = ['grid', 'date', 'arrow', 'progress', 'bar', 'noTime'];
        // make group layers
        for (let layer of layers) {
            this.layers[layer] = createSVG('g', {
                class: layer,
                append_to: this.$svg
            });
            // $('.gantt').append(this.layers[layer])
        }

    }

    make_grid () {
        this.make_grid_background();
        this.make_grid_rows();
        this.make_grid_header();
        this.make_grid_ticks();
    }

    make_grid_background () {
        const grid_width = this.dates.length * this.options.column_width;
        const grid_height =
            this.options.header_height +
            this.options.padding +
            (this.options.bar_height + this.options.padding) *
            (this.get_tasks().length ? this.get_tasks().length : this.dates.length);

        createSVG('rect', {
            x: 0,
            y: 0,
            width: grid_width,
            height: grid_height,
            class: 'grid-background',
            append_to: this.layers.grid
        });

        $.attr(this.$svg, {
            height: grid_height,
            width: '100%'
        });
    }

    make_grid_rows () {
        let tasks = this.get_tasks() || [];
        tasks = tasks.length ? this.get_tasks() : this.dates;
        const rows_layer = createSVG('g', {
            append_to: this.layers.grid
        });
        const row_width = this.dates.length * this.options.column_width;
        const row_height = this.options.bar_height + this.options.padding;
        let row_y = this.options.header_height + this.options.padding / 2;
        for (let [index, task] of tasks.entries()) {
            createSVG('rect', {
                x: 0,
                y: row_y,
                width: row_width,
                height: row_height,
                id: task.id,
                class: `grid-row ${!index ? 'no': ''}`,
                append_to: rows_layer
            });
            row_y += this.options.bar_height + this.options.padding;
        };
        createSVG('line', {
            x1: 0,
            y1: row_y,
            x2: row_width,
            y2: row_y,
            class: 'row-line',
            append_to: rows_layer
        });
    }
    create_grid_rows (row_width, row_y, row_height, rows_layer, lines_layer) {
        for (let [index, task] of this.dates) {
            createSVG('rect', {
                x: 0,
                y: row_y,
                width: row_width,
                height: row_height,
                id: task.id || index,
                class: 'grid-row',
                append_to: rows_layer
            });

            createSVG('line', {
                x1: 0,
                y1: row_y + row_height,
                x2: row_width,
                y2: row_y + row_height,
                class: 'row-line',
                append_to: lines_layer
            });

            row_y += this.options.bar_height + this.options.padding;
        }
    }

    make_grid_header () {
        const header_width = this.dates.length * this.options.column_width;
        const header_height = this.options.header_height + this.options.padding / 2;
        createSVG('rect', {
            x: 0,
            y: 0,
            width: header_width,
            height: header_height,
            class: `grid-resource-header ${this.resource}`,
            append_to: this.layers.grid
        });
        this.make_dates();
    }

    make_grid_ticks () {
        let tick_x = 0;
        let tick_y = this.options.header_height + this.options.padding / 2;
        let tick_height =
            (this.options.bar_height + this.options.padding) *
            (this.get_tasks().length ? this.get_tasks().length : this.dates.length);

        for (let date of this.dates) {
            let tick_class = 'tick';
            // thick tick for monday
            if (this.view_is(VIEW_MODE.DAY) && date.getDate() === 1) {
                tick_class += ' thick';
            }
            // thick tick for first week
            if (
                this.view_is(VIEW_MODE.WEEK) &&
                date.getDate() >= 1 &&
                date.getDate() < 8
            ) {
                tick_class += ' thick';
            }
            // thick ticks for quarters
            if (this.view_is(VIEW_MODE.MONTH) && (date.getMonth() + 1) % 3 === 0) {
                tick_class += ' thick';
            }

            createSVG('path', {
                d: `M ${tick_x} ${tick_y} v ${tick_height}`,
                class: tick_class,
                append_to: this.layers.grid
            });


            //非弹窗形式
            !this.isDefault && this.resource.selectDimension === 'personal_mode' && createSVG('text', {
                x: tick_x + this.options.column_width/2,
                y: tick_y + this.options.header_height / 2,
                innerHTML: '-',
                class: 'no-time-text',
                append_to: this.layers.noTime,
            });
            
            tick_x += this.options.column_width;
        }
    }
    make_dates () {
        let get_dates_to_draw = this.get_dates_to_draw();
        let _this = this;
        const x = date_utils.diff(date_utils.today(), this.gantt_start, 'hour') / this.options.step * this.options.column_width + this.options.column_width / 2;
        get_dates_to_draw.forEach((item, index) => {
            let date = item; let is_show_month = false;
            //日
            if (get_dates_to_draw[index - 1] && date.month !== get_dates_to_draw[index - 1].month) {
                is_show_month = true;
            }
            createSVG('text', {
                x: +date.lower_x,
                y: _this.options.view_mode === 'Day' ? (date.lower_y - 10) : date.lower_y - 1,
                innerHTML: ((_this.options.view_mode === 'Week' && is_show_month) ? date.month + $t('月') : '') + date.lower_text + `${this.options.view_mode === 'Week' ? $t('日') : ''}`,
                class: `lower-text ${x == date.lower_x && _this.isDefault ? 'lower-text-active' : ''}`,
                append_to: this.layers.date
            });
            //周
            if (_this.options.view_mode === 'Day') {
                createSVG('text', {
                    x: +date.lower_x,
                    y: date.lower_y + 4,
                    innerHTML: date_utils.format_week(date.week, 'zh'),
                    class: `lower-text ${x == date.lower_x && _this.isDefault ? 'lower-text-active' : ''}`,
                    append_to: this.layers.date
                });
            }

            //upper的日期区间
            if (Number(date.lower_text) === 15) {
                createSVG('text', {
                    x: date.lower_x,
                    y: date.lower_y - 30,
                    innerHTML: date.year + $t('年') + date.month + $t('月'),
                    class: 'upper-text',
                    append_to: this.layers.date,
                });
            }
        })

    }

    get_dates_to_draw () {
        let last_date = null;
        const dates = this.dates.map((date, i) => {
            last_date = date;
            const d = this.get_date_info(date, last_date, i);
            //暂时特殊处理以后扩展
            d['time'] = date.getTime();
            d['year'] = date.getFullYear();
            d['month'] = +date.getMonth() + 1;
            d['week'] = date.getDay();
            return d;
        });
        return dates;
    }

    get_date_info (date, last_date, i) {
        last_date = date_utils.add(date, 1, 'year');
        const date_text = {
            'Quarter Day_lower': date_utils.format(
                date,
                'HH',
                this.options.language
            ),
            'Half Day_lower': date_utils.format(
                date,
                'HH',
                this.options.language
            ),
            Day_lower: date_utils.format(date, 'D', this.options.language),
            Week_lower: date.getMonth() !== last_date.getMonth() ?
                date_utils.format(date, 'D MMM', this.options.language) : date_utils.format(date, 'D', this.options.language),
            Month_lower: date_utils.format(date, 'MMMM', this.options.language),
            Year_lower: date_utils.format(date, 'YYYY', this.options.language),
            'Quarter Day_upper': date.getDate() !== last_date.getDate() ?
                date_utils.format(date, 'D MMM', this.options.language) : '',
            'Half Day_upper': date.getDate() !== last_date.getDate() ?
                date.getMonth() !== last_date.getMonth() ?
                    date_utils.format(date, 'D MMM', this.options.language) :
                    date_utils.format(date, 'D', this.options.language) : '',
            Day_upper: date.getMonth() !== last_date.getMonth() ?
                date_utils.format(date, 'MMMM', this.options.language) : '',
            Week_upper: date.getMonth() !== last_date.getMonth() ?
                date_utils.format(date, 'MMMM', this.options.language) : '',
            Month_upper: date.getFullYear() !== last_date.getFullYear() ?
                date_utils.format(date, 'YYYY', this.options.language) : '',
            Year_upper: date.getFullYear() !== last_date.getFullYear() ?
                date_utils.format(date, 'YYYY', this.options.language) : ''
        };

        const base_pos = {
            x: i * this.options.column_width,
            lower_y: this.options.header_height,
            upper_y: this.options.header_height - 25
        };

        const x_pos = {
            'Quarter Day_lower': this.options.column_width * 4 / 2,
            'Quarter Day_upper': 0,
            'Half Day_lower': this.options.column_width * 2 / 2,
            'Half Day_upper': 0,
            Day_lower: this.options.column_width / 2,
            Day_upper: this.options.column_width * 30 / 2,
            Week_lower: 0,
            Week_upper: this.options.column_width * 4 / 2,
            Month_lower: this.options.column_width / 2,
            Month_upper: this.options.column_width * 12 / 2,
            Year_lower: this.options.column_width / 2,
            Year_upper: this.options.column_width * 30 / 2
        };

        return {
            upper_text: date_text[`${this.options.view_mode}_upper`],
            lower_text: date_text[`${this.options.view_mode}_lower`],
            upper_x: base_pos.x + x_pos[`${this.options.view_mode}_upper`],
            upper_y: base_pos.upper_y,
            lower_x: base_pos.x + x_pos[`${this.options.view_mode}_lower`],
            lower_y: base_pos.lower_y
        };
    }

    make_bars () {
        this.bars = this.tasks.map(task => {
            const bar = new Bar(this, task);
            this.layers.bar.appendChild(bar.group);
            return bar;
        });
    }


    compute_x (startDate) {
        const {
            step,
            column_width
        } = this.options;
        const gantt_start = this.gantt_start;

        const diff = date_utils.diff(startDate, gantt_start, 'hour');
        let x = diff / step * column_width;

        if (this.view_is('Month')) {
            const diff = date_utils.diff(startDate, gantt_start, 'day');
            x = diff * column_width / 30;
        }
        return x;
    }
    set_width () {
        const cur_width = this.$svg.getBoundingClientRect().width;
        const actual_width = this.$svg
            .querySelector('.grid .grid-row')
            .getAttribute('width');
        if (cur_width < actual_width) {
            this.$svg.setAttribute('width', actual_width);
        }
    }

    add_grid_row_class (className, index, trId) {
        const id_str = trId + '';
        let row = this.$svg.querySelectorAll('.grid .grid-row');
        row.forEach((item, key) => {
            let trd = (typeof id_str == 'string' && id_str.constructor == String) ? id_str : id_str.toString();
            item.classList[item.id === trd ? 'add' : 'remove'](className);
        });
        this.bars.length && this.bars.forEach(item => {
            const current = item.group;
            const group_id = current.getAttribute('data-id');
            const rects = current?.querySelectorAll('.linear-rect');
            rects.forEach(rt => {
                const old_color = rt.getAttribute('fill');
                const isWhite = old_color === '#fff';
                rt.classList[group_id === id_str ? 'add' : 'remove'](className);
                if (rt.classList.contains('linear-rect') && rt.classList.contains(className)) {
                    rt.setAttribute('fill', isWhite ? '#e6f4ff' : old_color);
                } else {
                    rt.setAttribute('fill', old_color === '#e6f4ff' ? '#fff' : old_color);
                }
            });
        });
    }


    remove_grid_row_class (className, index) {
        let row = this.$svg.querySelector('.grid .grid-row')

        row.forEach((item, key) => {
            if (typeof index !== 'undefined') {
                if (index === key) {
                    item.classList.remove(className);
                }
            } else {
                item.classList.remove(className);
            }
        })
    }

    unselect_all () {
        [...this.$svg.querySelectorAll('.bar-wrapper')].forEach(el => {
            el.classList.remove('active');
        });
    }

    view_is (modes) {
        if (typeof modes === 'string') {
            return this.options.view_mode === modes;
        }

        if (Array.isArray(modes)) {
            return modes.some(mode => this.options.view_mode === mode);
        }

        return false;
    }
    get_tasks () {
        let taskObj = {};

        return (this.tasks || []).filter(item => {
            if (!taskObj[item.id]) {
                taskObj[item.id] = true;
                return item;
            }
        });
    }

    get_task (id, notActual) {
        return this.tasks.find(task => {
            return notActual ? task.id === id && !task._is_actual_bar : task.id === id;
        });
    }

    get_bar (id, notActual) {
        return this.bars.find(bar => {
            return notActual ? bar.task.id === id && !bar.task._is_actual_bar : (bar.group.classList.contains('active') && bar.task.id === id);
        });
    }

    get_popup_wrapper (type) {
        if (this.popup_wrapper[type]) {
            return this.popup_wrapper[type];
        }

        this.popup_wrapper[type] = document.createElement('div');
        this.popup_wrapper[type].classList.add('popup-wrapper');
        this.popup_wrapper[type].classList.add(`popup-wrapper-${type}`);
        document.body.appendChild(this.popup_wrapper[type]);

        return this.popup_wrapper[type];
    }
    show_popup (type, options) {
        clearTimeout(this.hide_popup_timer);
        this.hide_popup();
        if (!this.popup[type]) {
            let wrapper = this.get_popup_wrapper(type);

            this.popup[type] = new Popup(wrapper);

            $.on(wrapper, 'mouseenter', e => {
                clearTimeout(this.hide_popup_timer);
            })

            $.on(wrapper, 'mouseleave', e => {
                this.hide_popup();
            })

            $.on(wrapper, 'click', e => {
                if(e.target.classList.contains('arrow-icon')) {
                    e.target.classList.toggle('on')
                    if(!e.target.classList.contains('on')) {
                        e.target.style.transform = 'rotate(0deg)';
                        wrapper.querySelector('.work-time-info').style.display = 'none';
                    }else {
                        e.target.style.transform = 'rotate(180deg)';
                        wrapper.querySelector('.work-time-info').style.display = 'block';
                    }
                }
            })
        }
        !options.timeSheet && this.popup[type].show(options);
        options.timeSheet && this.popup[type].show_label(options);
    }

    hide_popup (type) {
        if (type) {
            this.popup[type] && this.popup[type].hide();
        } else {
            Object.keys(this.popup).forEach(item => {
                this.popup[item].hide();
            })
        }
    }

    /**
     * Gets the oldest starting date from the list of tasks
     *
     * @returns Date
     * @memberof Gantt
     */
    get_oldest_starting_date () {
        return this.tasks
            .map(task => task._start)
            .reduce(
                (prev_date, cur_date) =>
                    cur_date <= prev_date ? cur_date : prev_date
            );
    }

    clear () {
        this.container && (this.container.innerHTML = '');
    }

    /**
     * Set z-index
     * @param {Object} sourceElement
     * @param {String} type
     * @param {String} current
     * @param {String} target
     */
    setZindex (sourceElement, type, current, target) {
        let curLayer = this.layers[current];
        let targetLayer = this.layers[target];

        switch (type) {
            case 'top':
                if (curLayer) {
                    this.$svg.appendChild(curLayer);
                    this.options.setZindex && this.options.setZindex(sourceElement, type);
                }
                break;
            case 'before':
                if (curLayer && targetLayer) {
                    this.$svg.insertBefore(curLayer, targetLayer);
                    this.options.setZindex && this.options.setZindex(sourceElement, type);
                }
                break;
        }
    }
}

Resource.VIEW_MODE = VIEW_MODE;

function generate_id (task) {
    return (
        task.name +
        '_' +
        Math.random()
            .toString(36)
            .slice(2, 12)
    );
};

