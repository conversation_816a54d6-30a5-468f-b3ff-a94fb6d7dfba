export default class Popup {
    constructor(parent) {
        this.parent = parent;
    }
    show (options) {
        if (!options.target_element) {
            throw new Error('target_element is required to show popup');
        }
        let date_range = options.dates;
        let work_hours = options.workHours;
        this.parent.innerHTML = `<div class="work-time-popup">
            <p>${$t('report_dates_range', {range: date_range, hours: work_hours})}<i class="fx-icon-arrow-up2 arrow-icon"></i></p>
            <ul class="work-time-info">
                <li>${$t('report_across_dates.description')}</li>
                <li>${$t('report_multiple_dates.description')}</li>
            </ul>
        </div>`
        // set position
        this.setPosition(options);
    }
    show_label(options) {
        if (!options.target_element) {
            throw new Error('target_element is required to show popup');
        }
        const label = options.label || '';
        this.parent.innerHTML = `<div class="work-time-popup"><p>${label}</p></div>`;
        this.setPosition(options);
    }
    setPosition(options) {
        const element = options.target_element;
        let position_meta = element.getBoundingClientRect();
        if (options.position === 'left') {
            let h = document.querySelectorAll('.grid-background')[0].getAttribute('height');
            this.parent.style.position = "fixed";
            this.parent.style.left = position_meta.left + 'px';
            this.parent.style.top = position_meta.bottom + 'px';
        }

        if (options.position === 'follow' && options.position_data) {
            this.parent.style.position = "fixed";
            this.parent.style.left = options.position_data.left + 'px';
            this.parent.style.top = options.position_data.top + 'px';
        }

        // show
        this.parent.style.display = 'block';
        this.parent.style.zIndex = CRM.util.getzIndex() + 5;
        this.updatePositoin(element);
    }
    updatePositoin (target_element) {
        let cw = document.documentElement.clientWidth;
        let ch = document.documentElement.clientHeight;
        let rect = this.parent.getBoundingClientRect();

        if (rect.right > cw) {
            this.parent.style.left = rect.left - rect.width + 'px';
        }

        if (rect.bottom > ch) {
            this.parent.style.top = rect.top - rect.height - target_element.getBoundingClientRect().height + 'px';
        }
    }

    hide () {
        this.parent.style.display = 'none';
        //里程碑隐藏线条
        let milepostLines = document.querySelector('.milepost-line');
        (milepostLines && milepostLines.length) ? milepostLines.style.opacity = 0 : null
    }
}
