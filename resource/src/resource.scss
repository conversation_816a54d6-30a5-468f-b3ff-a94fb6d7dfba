$bar-color: #eaf7ed !default;
$bar-work-color: #98d9a4 !default;
$bar-light-color: #fbbe7f !default;
$bar-stroke: #8d99a6 !default;
$bar-red-color: #ffeeea !default;
$bar-hour-red-color: #fda795 !default;
$bar-hour-dark-red-color:#FF522A !default;
$border-color: #e5e9f2 !default;
$light-border-color: #ebeff2 !default;
$light-yellow: #ff8000 !default;
$text-muted: #c1c5ce !default;
$text-light: #545861 !default;
$text-color: #545861 !default;
$bar-pro: #f97f0e;
@mixin common-stroke-style {
  stroke: $border-color;
  stroke-width: 0.7px;
  stroke-linecap: butt;
  stroke-linejoin: miter;
  vector-effect: non-scaling-stroke;
  shape-rendering: crispedges;
}
.project-resource-gantt {
  .grid-background {
    fill: none;
  }

  .grid-resource-header {
    fill: #f0f2f5;
    stroke: $border-color;
    stroke-width: 0.5;
  }

  .grid-row {
    fill: #fff;
    @include common-stroke-style;
    &.no {
      stroke: none;
    }
    &.gantt-row-hover {
      fill: var(--color-info01, #e6f4ff) !important;
    }
  }

  .row-line {
    @include common-stroke-style;
    stroke-width: 1.6px;
  }

  .tick {
    stroke: $border-color;
    stroke-width: 0.6px;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    vector-effect: non-scaling-stroke;
    shape-rendering: crispedges;

    &.thick {
      stroke-width: 0.4;
    }
  }

  .bar-resource {
    stroke-width: 0;
    transition: stroke-width 0.3s ease;
    user-select: none;
  }
  .bar-hour {
    fill: $bar-work-color;
  }
  .bar-text-label {
    text-anchor: middle;
    dominant-baseline: middle;
  }
  .bar-fill {
    opacity: 8;

    &.light-progress {
      fill: $bar-light-color;
      stroke: $bar-light-color;
    }
  }

  .bar-line {
    fill: #eaf7ed;
    transition: stroke-width 0.3s ease;
    user-select: none;
  }
  .bar-def-red-group {
    .bar-hour {
      fill: $bar-hour-red-color;
      &.dark-red {
        fill: $bar-hour-dark-red-color;
      }
    }
  }
  .bar-wrapper {
    cursor: pointer;
    outline: none;
  }

  .lower-text,
  .upper-text {
    font-size: 12px;
    fill: $text-light;
    text-anchor: middle;
  }

  .lower-text-active {
    fill: $bar-pro;
  }
  .work-hour-rect .cols {
    stroke: $light-border-color;
    stroke-width: 0.3;
  }
  .bar-hour-input {
    .text-wrapper {
      position: relative;
    }
    .text-wrapper::after {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      width: 10px;
      height: 10px;
      background-color: #737c8c;
      clip-path: polygon(0 0, 100% 0, 0 100%);
      transform: rotateY(180deg);
    }
  }
  .work-text {
    display: inline-block;
    border: 1px solid transparent;
    background: #fff;
    &:focus {
      border-color: #368dff;
    }
    &.cols-text {
      background: #f7f7f8;
      color: #91959E;
    }
  }
  .flow-icon {
    display: none;
    position: absolute;
    right: 0;
    bottom: 0;
    font-size: 12px;
    &.flow {
      display: block;
    }
  }
  
}
.popup-wrapper-time, .popup-wrapper-label{
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99999;
  padding: 6px 8px;
  width: fit-content;
  width: 314px;
  background: #fff;
  color: #959da5;
  border-radius: 4px;
  border: none;
  box-shadow: 0px 8px 12px -4px rgba(9, 30, 66, 0.25),
    0px 0px 1px 0px rgba(9, 30, 66, 0.18);

  .work-time-popup {
    width: 100%;
    color: #545861;
    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: 400;
    overflow: hidden;
    .arrow-icon {
      float: right;
      position: relative;
      top: 3px;
      transform: rotate(0deg);
      cursor: pointer;
    }
    .work-time-info {
      display: none;
      li {
        color: #545861;
        font-size: 12px;
      }
    }
  }

  .pointer {
    position: absolute;
    height: 5px;
    margin: 0 0 0 -5px;
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    display: none;
  }
}
.popup-wrapper-label {
  width: auto;
}
