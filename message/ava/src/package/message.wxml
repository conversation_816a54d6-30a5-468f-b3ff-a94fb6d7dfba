<wxs src="./message.wxs" module="message" />

<scroll-view class="feed-session-message" scroll-into-view="{{cellsCheckboxBottom}}" bindscroll="fnHandleScroll" enable-passive scroll-y enhanced>
  <view class="feed-session-message_wrapper">
    <view id="{{item.id === scrollIntoDataId ? 'cells_checkbox_target' : (index === data.length - 1 ? 'cells_checkbox_bottom' : '')}}" class="cells_checkbox" wx:for="{{data}}" wx:key="id">
          <view class="msg-item-wrapper ">
              <view class="{{message.getLeftOrRight(item.user.id, userId)? 'msg-item-wrapper-mine' : 'msg-item-wrapper-other'}}">
                  <view wx:if="{{message.getLeftOrRight(item.user.id, userId)}}">
                        <text class="msg-item-time">{{item.create_time}}</text>
                        <text class="msg-item-name">{{item.user.name || '--'}}</text>
                    </view>
                    <view wx:else>
                        <text class="msg-item-name">{{item.user.name || '--'}}</text>
                        <text class="msg-item-time">{{item.create_time}}</text>
                    </view>

                  <image-msg wx:if="{{item.type === 'image'}}" data="{{message.filterImgData(data)}}" index="{{message.filterImgIndex(data, item.id)}}"/>

                  <file-attach-msg wx:elif="{{item.type === 'file'}}" data="{{item.content}}" f-class="{{message.getLeftOrRight(item.user.id, userId)? 'file-attach-msg_mine' : 'file-attach-msg_other'}}"/>

                    <mini-program-msg wx:elif="{{item.type === 'miniProgram'}}" data="{{item.content}}"/>

                    <video-msg wx:elif="{{item.type === 'video'}}" data="{{item.content}}"/>

                    <view wx:elif="{{item.type === 'audio'}}" class="msg-item {{message.getLeftOrRight(item.user.id, userId) ? 'msg-item-name_mine':'msg-item-name_other'}}">
                        <voice-msg data="{{item.content}}" i-duration="voice-msg-duration" />
                    </view>

                  <view class="msg-item {{message.getLeftOrRight(item.user.id, userId) ? 'msg-item-name_mine':'msg-item-name_other'}}" wx:else>
                      <view class="msg-content"> 
                        
                        <block wx:for="{{message.parseLink(item.content)}}" wx:for-item="text">
                            <text class="link" wx:if="{{text.isLink}}" bind:tap="openLink" data-link="{{text.value}}">{{ text.value }}</text>
                            <block wx:else>
                                <block wx:for="{{message.highlight(text.value, item.highlight_words)}}" wx:for-item="t">
                                    <text class="highlight" wx:if="{{t.isHighlight}}">{{ t.value }}</text>
                                    <block wx:else>{{ t.value }}</block>
                                </block>
                            </block>
                        </block>
                          
                      </view>
                  </view>

                  <block wx:if="{{item.tags}}">
                        <view class="msg-item-tag" wx:for="{{item.tags}}" wx:for-item="tag">
                            <text class="msg-item-tag-text">{{ tag }}</text>
                        </view>
                    </block>

              </view>
          </view>
      </view>  
  </view>
</scroll-view>