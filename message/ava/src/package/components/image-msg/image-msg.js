// objformpkgbiz/pages/session/components/image_msg/image_msg.js
import fsApi from 'fs-hera-api';
const {
  jsapi,
} = fsApi;
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    data:{
      type:Array,
      value: []
    },
    index: {
        type: Number,
        value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    _preview () {
      var images = this.properties.data.map(item => item.path + '.' + item.ext);
      jsapi.imgPreview({
        imgUrls: images,
        index: this.properties.index,
        onSuccess: function(res) {
            console.log('success', res);
        }
      })
    }
  }
})
