// src/package/components/video-msg/video-msg.js
import fsApi from 'fs-hera-api';
const {
  jsapi,
} = fsApi;
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    data:{
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    preview() {
      jsapi.previewAttach({
        attach: {
          path: this.properties.data.path + '.' + this.properties.data.ext,
          filename: this.properties.data.name,
          size: this.properties.data.size,
        },
        onSuccess: function(res) {
          console.log('success', res);
        },
        onFail: function(res) {
          console.log('fail', res);
        }
      })
    }
  }
})
