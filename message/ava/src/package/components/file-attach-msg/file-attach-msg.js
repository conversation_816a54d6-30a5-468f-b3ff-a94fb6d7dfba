// objformpkgbiz/pages/session/components/file-attach-msg/file-attach-msg.js
import fsApi from 'fs-hera-api';
const {
  jsapi,
} = fsApi;
Component({
  externalClasses: ['f-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    data:{
      type:Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
  },

  /**
   * 组件的方法列表
   */
  methods: {
    preview() {
      jsapi.previewFile({
        fileNPath: this.properties.data.path + '.' + this.properties.data.ext,
        fileName: this.properties.data.name,
        onSuccess: function(res) {
          console.log('success', res);
        },
        onFail: function(res) {
          console.log('fail', res);
        }
      })
    },
  }
})
