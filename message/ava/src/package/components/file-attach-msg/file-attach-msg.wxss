/* objformpkgbiz/pages/session/components/file-attach-msg/file-attach-msg.wxss */
.feed-session-message-attachMsg{
  width: 100%;
}
.file-upload-item {
  position: relative;
  display: flex;
  height: 130rpx;
  line-height: 128rpx;
  width: 80%;
  box-sizing: border-box;
  transition: all 0.3s ease-in;
}
.file-item-icon {
  height: 60rpx;
  width: 60rpx;
  margin: 0 24rpx;
  align-self: center;
  flex-basis: 60rpx;
  background-color: #eee;
  flex-shrink: 0;
  background-repeat: center center no-repeat;
  background-size: 60rpx 60rpx;
}
.file-item-wrap {
  width: 0;
  position: relative;
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  align-items: center;
  border-bottom: 2rpx solid #eaeff3;
  z-index: 10;
  background-color: #f2f3f5;
  overflow: hidden;
}
.file-item-name {
  font-size: 32rpx;
  flex-grow: 4;
  flex-shrink: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.file-item-size {
  color: #67717f;
  flex-basis: 60rpx;
  flex-shrink: 0;
  margin: 0 40rpx;
}
.file-item-status {
  flex-shrink: 0;
  flex-basis: 80rpx;
  font-size: 24rpx;
  text-align: center;
  margin-right: 24rpx;
}
.file-item-fail {
  color: #f35959;
}
.file-item-ready {
  color: #0385e1;
}
.file-item-success {
  color: #16263c;
}
.file-item-progress {
  display: flex;
  align-items: center;
  justify-content: center;
}
.file-item-delete {
  height: 64rpx;
  line-height: 64rpx;
  flex-basis: 96rpx;
  flex-shrink: 0;
  text-align: center;
  font-size: 30rpx;
  color: #fff;
  background-color: #f35959;
  transition: width 0.3s ease-in;
  z-index: 5;
}
.file-item-content {
  margin-left: -180rpx;
}
.file-upload-item:last-child .file-item-wrap {
  border-bottom: none;
}