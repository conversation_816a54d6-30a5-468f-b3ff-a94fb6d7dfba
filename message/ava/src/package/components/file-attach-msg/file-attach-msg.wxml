<wxs src="./file-attach-msg.wxs" module="fileAttachMsg" />

<view class="feed-session-message-attachMsg f-class">
      <view class="file-upload-item">
        <view class="file-item-wrap" bindtap="preview">
            <image class="file-item-icon" src="{{fileAttachMsg.iconCls(data.ext)}}" />
            <view class="file-item-name">{{data.name}}</view>
            <view class="file-item-size">{{fileAttachMsg.formatFileSize(data.size)}}</view>
        </view>
    </view>
</view>