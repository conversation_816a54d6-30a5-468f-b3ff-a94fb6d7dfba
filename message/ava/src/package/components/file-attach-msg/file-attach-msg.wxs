function getFileType(fileName, fullTest, moreType) {
    var fileType = "common";
    fileName = fileName.toLowerCase();

    if (['tif', 'eps', 'png', 'gif', 'jpeg', 'jpg', 'dwg', 'ai', 'cdr', 'bmp', 'webp'].indexOf(fileName) > -1) {
        fileType = "jpg";
    } else if (['doc', 'docx'].indexOf(fileName) > -1) {
        fileType = "doc";
    } else if (['pdf'].indexOf(fileName) > -1) {
        fileType = "pdf";
    } else if (['rar'].indexOf(fileName) > -1) {
        fileType = "rar";
    } else if (['xls', 'xlsx', 'csv'].indexOf(fileName) > -1) {
        fileType = "xls";
    } else if (['zip'].indexOf(fileName) > -1) {
        fileType = "zip";
    } else if (['7z'].indexOf(fileName) > -1) {
        fileType = "zip";
    } else if (['ppt', 'pptx'].indexOf(fileName) > -1) {
        fileType = "ppt";
    } else if (['txt'].indexOf(fileName) > -1) {
        fileType = "txt";
    } else if (['rm', 'rmvb', 'swf', 'avi', 'mov', 'wmv', 'mp4', 'mpg', 'mpeg', 'flv'].indexOf(fileName) > -1) {
        fileType = "mov";
    } else if (['wav', 'mp3', 'acm', 'aif', 'aifc', 'aiff', 'au', 'amr'].indexOf(fileName) > -1) {
        fileType = "mp3";
    }
    if (fullTest) {
        if (['png'].indexOf(fileName) > -1) {
            fileType = "png";
        } else if (['gif'].indexOf(fileName) > -1) {
            fileType = "gif";
        }
    }
    // 文件上传 统计扩展名
    if (moreType) {
        if (['bmp'].indexOf(fileName) > -1) {
            fileType = "bmp";
        } else if (['html'].indexOf(fileName) > -1) {
            fileType = "html";
        } else if (['exe'].indexOf(fileName) > -1) {
            fileType = "exe";
        } else if (['apk'].indexOf(fileName) > -1) {
            fileType = "apk";
        } else if (['dmg'].indexOf(fileName) > -1) {
            fileType = "dmg";
        } else if (['app'].indexOf(fileName) > -1) {
            fileType = "app";
        } else if (['msi'].indexOf(fileName) > -1) {
            fileType = "msi";
        } else if (['psd'].indexOf(fileName) > -1) {
            fileType = "psd";
        } else if (['docx'].indexOf(fileName) > -1) {
            fileType = "docx";
        } else if (['xlsx'].indexOf(fileName) > -1) {
            fileType = "xlsx";
        } else if (['pptx'].indexOf(fileName) > -1) {
            fileType = "pptx";
        } else if (['mp3'].indexOf(fileName) > -1) {
            fileType = "mp3";
        } else if (['mp4'].indexOf(fileName) > -1) {
            fileType = "mp4";
        }
    }
    return fileType;
}

function iconCls(name) {
    var map = {
        common: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/common.svg',
        txt: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/txt.svg',
        pdf: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/pdf.svg',
        doc: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/doc.svg',
        xls: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/xls.svg',
        ppt: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/ppt.svg',
        mp3: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/mp3.svg',
        zip: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/zip.svg',
        jpg: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/jpg.svg',
        mov: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/mov.svg',
        mp4: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/mov.svg',
        amr: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/mov.svg'
      };
      return map[getFileType('.'+name,true,true)];
}

function formatFileSize(size) {
    var unit = "B";
    if (size > 1024) {
        unit = "K";
        size = size / 1024;
    }
    if (size > 1024) {
        unit = "M";
        size = size / 1024;
    }
    if (size > 1024) {
        unit = "G";
        size = size / 1024;
    }
    size = Math.round(size);
    return size + unit;
}


module.exports = {
    formatFileSize: formatFileSize,
    iconCls: iconCls
};