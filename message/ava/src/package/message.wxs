function getLeftOrRight(val, cUserId) {
    if(!val) {
        return false
    }
    if(cUserId == val) {
        return true
    }
    return false
}

function parseLink(text) {
    var result = [];

    var httpReg = getRegExp("http://([\w-]+\.)+[\w-]+(/[\w-./?%&=]*)?", "ig");

    var linkList = text.match(httpReg);

    (linkList || []).forEach(function(link, index) {
        result.push({value: text.slice(0, text.indexOf(link))}, {isLink: true, value: link});
        text = text.slice(text.indexOf(link) + link.length);
        index === linkList.length - 1 && result.push({value: text});
    });

    return result.length ? result : [{value: text}];
}

function mergeIntervals(intervals) {
    var res = [];
    intervals.sort(function(a, b) { return a.start - b.start });

    var prev = intervals[0];

    for (var i = 1; i < intervals.length; i++) {
        var cur = intervals[i];
        if (prev.end >= cur.start) { // 有重合
        prev.end = Math.max(cur.end, prev.end); 
        } else {       // 不重合，prev推入res数组 
        res.push(prev);
        prev = cur;  // 更新 prev
        }
    }

    prev && res.push(prev);
    return res;
}

function getStrPosition(str, target) {
    var result = [];
    var index = str.indexOf(target);
    while (index !== -1) {
        result.push(index);
        index = str.indexOf(target, index + 1);
    }
    return result;
}

function highlight(text, highlightWords = []) {
    var result = [];
    var highlightIntervals = [];
    highlightWords.forEach(function(highlightWord) {
        var intervals = getStrPosition(text, highlightWord).map(function(index) { return { start: index, end: index + highlightWord.length }});
        highlightIntervals = highlightIntervals.concat(intervals);
    });

    highlightIntervals = mergeIntervals(highlightIntervals);

    var prevEnd;
    highlightIntervals.forEach(function(highlightInterval, index) {
        result.push({value: text.slice(prevEnd || 0, highlightInterval.start)}, {isHighlight: true, value: text.slice(highlightInterval.start, highlightInterval.end)});
        prevEnd = highlightInterval.end;
        index === highlightIntervals.length - 1 && result.push({value: text.slice(prevEnd)});
    });

    return result.length ? result : [{value: text}];
}

function filterImgData(data) {
    return data.filter(function(image) { return image.type === 'image' }).map(function(image) { return image.content });
}

function filterImgIndex(data, id) {
    var result = 0;
    var imageData = data.filter(function(image) { return image.type === 'image' });
    imageData.forEach(function(image, index) {
        image.id === id && (result = index);
    });
    return result;
}

module.exports = {
    getLeftOrRight: getLeftOrRight,
    parseLink: parseLink,
    highlight: highlight,
    filterImgData: filterImgData,
    filterImgIndex: filterImgIndex
};