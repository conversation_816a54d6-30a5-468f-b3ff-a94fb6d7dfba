// objformpkgbiz/pages/session/components/message/message.js
import fsApi from 'fs-hera-api';
const {
  page,
  util,
} = fsApi;

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    data: {
      type: Array,
      value: []
    },
    userId: {
      type: String,
      value: ''
    },
    scrollIntoDataId: {
        type: String,
        value: ''
    }
  },

  observers: {
    data(val) {
      const _this = this;
      const node = _this.createSelectorQuery().select('.feed-session-message');
      node.scrollOffset(function({scrollHeight}){
        node.boundingClientRect(function({height}){
          if (scrollHeight !== 0 && height !== 0 && scrollHeight === height) {
            _this.triggerEvent('scrollHandle', 'top');
            _this.triggerEvent('scrollHandle', 'bottom');
          }
        }).exec();
      }).exec();
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    cellsCheckboxBottom: ''
  },

  attached() {
    const _this = this;

    //设置滚动条
    _this.setData({
      cellsCheckboxBottom: _this.properties.scrollIntoDataId ? 'cells_checkbox_target' : 'cells_checkbox_bottom'
    });
  },

  /**
   * 组件的方法列表
   */
  methods: {
    openLink(e) {
      page.utilOpen({
        name: e.currentTarget.dataset.link,
        onSuccess:(res)=>{
          console.log('success', res);
        },
        onFail:(res)=>{
          console.log('fail', res);
        }
      });
    },
    fnHandleScroll: util.debounce(function(e) {
      const _this = this;

      _this.createSelectorQuery().select('.feed-session-message').boundingClientRect(function(res){
        const {scrollTop, scrollHeight} = e.detail;
        const {height} = res;
        console.log('滚动高',scrollTop, res)
        if(scrollTop === 0) {
            //下拉刷新
            _this.triggerEvent('scrollHandle', 'top');
        }
        if(scrollTop + height === scrollHeight) {
            //上拉刷新
            _this.triggerEvent('scrollHandle', 'bottom');
        }
      }).exec();
    },300),
  }
})
