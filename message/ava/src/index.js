// src/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    messageData: [{
      id: '1',
      type: "text",
      user: {
          id: 1031,
          name: '名字'
      },
      content: '111必须啊不知道22222',
      highlight_words: ["必须啊不知道", "须", "知"],
      tags: ['超时2小时未回复', '含有敏感词'],
      create_time: 1673241566170
  },{
      id: '2',
      type: "image",
      user: {
          id: 4135,
          name: '客户'
      },
      content: {
          name: '图片',
          path: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/dht-empty.svg',
          ext: 'jpg',
          size: 266
      },
      create_time: 1673241566170
  },{
      id: '3',
      type: "video",
      user: {
          id: 1031,
          name: '名字'
      },
      content: {
          path: 'N_202212_07_843a05dae31a4da08ac82ce4017f427a',
          ext: 'mp4',
          size: 15169724,
          play_length: 108,
          thumb_path: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/dht-empty.svg'
      },
      create_time: 1673241566170
  },{
      id: '4',
      type: "file",
      user: {
          id: 4135,
          name: '客户'
      },
      content: {
          name: '文件',
          path: 'N_202209_16_42786f3b43b04544b95378ffa1100aab',
          ext: 'doc',
          size: 276,
      },
      create_time: 1673241566170
  },{
      id: '6',
      type: "miniProgram",
      user: {
          id: 4135,
          name: '客户'
      },
      content: {
          title: '开始聊天前请仔细阅读服务须知事项',
          description: '客户需同意存档聊天记录',
          displayname: '服务须知',
      },
      create_time: 1673241566170
  },{
      id: '7',
      type: "audio",
      user: {
          id: 1031,
          name: '名字'
      },
      content: {
          path: 'N_202211_24_9b1c0d1ed24a4623b4f4fabf7603dea5',
          ext: 'mp3',
          play_length: 10,
      },
      create_time: 1673241566170
  },{
      id: '22',
      type: "image",
      user: {
          id: 1031,
          name: '名字'
      },
      content: {
          name: '图片啊',
          path: 'https://a9.fspage.com/FSR/weex/avatar/object_form/images/dht-empty.svg',
          ext: 'jpg',
          size: 266
      },
      create_time: 1673241566170
  }]
  },

  scrollHandle() {
    console.log('scrollHandle')
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})