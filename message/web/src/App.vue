<template>
    <message :data="messageData" userId="1031" scrollIntoDataId="2" @scrollHandle="scrollHandle" />
</template>
  
  <script>
    import Message from './package/message.vue'

    export default {
        data() {
            return {
                messageData: [{
                        id: '1',
                        type: "text",
                        user: {
                            id: 1031,
                            name: '名字'
                        },
                        content: '111必须啊不知道22222',
                        highlight_words: ["必须啊不知道", "须", "知"],
                        tags: ['超时2小时未回复', '含有敏感词'],
                        create_time: 1673241566170
                    },{
                        id: '2',
                        type: "image",
                        user: {
                            id: 4135,
                            name: '客户'
                        },
                        content: {
                            name: '图片',
                            path: 'N_202209_16_ce5eaf646c324257b4106bc5192ced7f',
                            ext: 'jpg',
                            size: 266
                        },
                        create_time: 1673241566170
                    },{
                        id: '3',
                        type: "video",
                        user: {
                            id: 1031,
                            name: '名字'
                        },
                        content: {
                            path: 'N_202212_07_843a05dae31a4da08ac82ce4017f427a',
                            ext: 'mp4',
                            size: 15169724,
                            play_length: 108,
                            thumb_path: 'N_202212_07_d4a2b33c818447d089009440a05dcca5.thumb.1.webp'
                        },
                        create_time: 1673241566170
                    },{
                        id: '4',
                        type: "file",
                        user: {
                            id: 4135,
                            name: '客户'
                        },
                        content: {
                            name: '文件',
                            path: 'N_202209_16_42786f3b43b04544b95378ffa1100aab',
                            ext: 'doc',
                            size: 276,
                        },
                        create_time: 1673241566170
                    },{
                        id: '5',
                        type: "link",
                        user: {
                            id: 1031,
                            name: '名字'
                        },
                        content: {
                            title: '邀请你加入群聊',
                            description: '技术支持群，进入可查看详情',
                            image_url: 'https://wwcdn.weixin.qq.com/node/wework/images/202201062104.366e5ee28e.png',
                            link_url: 'https://work.weixin.qq.com/wework_admin/external_room/join/exceed?vcode=xxx',
                        },
                        create_time: 1673241566170
                    },{
                        id: '6',
                        type: "miniProgram",
                        user: {
                            id: 4135,
                            name: '客户'
                        },
                        content: {
                            title: '开始聊天前请仔细阅读服务须知事项',
                            description: '客户需同意存档聊天记录',
                            displayname: '服务须知',
                        },
                        create_time: 1673241566170
                    },{
                        id: '7',
                        type: "audio",
                        user: {
                            id: 1031,
                            name: '名字'
                        },
                        content: {
                            path: 'N_202211_24_9b1c0d1ed24a4623b4f4fabf7603dea5',
                            ext: 'mp3',
                            play_length: 10,
                        },
                        create_time: 1673241566170
                    },{
                        id: '22',
                        type: "image",
                        user: {
                            id: 1031,
                            name: '名字'
                        },
                        content: {
                            name: '图片啊',
                            path: 'N_202209_16_ce5eaf646c324257b4106bc5192ced7f',
                            ext: 'jpg',
                            size: 266
                        },
                        create_time: 1673241566170
                    }]
            }
        },
        methods: {
            scrollHandle() {
                console.log('scrollHandle')
            }
        },
        components: {
            'message': Message
        }
    }
  </script>