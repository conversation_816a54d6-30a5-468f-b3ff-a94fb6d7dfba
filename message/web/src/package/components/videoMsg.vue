<template>
    <div class="feed-session-message-videoMsg" >
        <div class="video-poster" @click="preview">
            <img :src="getPosterUrl(data.thumb_path)">
            <div class="duration">{{formatDuration(data.play_length)}}</div>
        </div>
    </div>
</template>

<script>
    import dhtEmpty from '../assets/images/dht-empty.svg';
    export default {
        props: {
            data:{
                type: Object,
                default: {}
            }
        },
        data() {
            return {}
        },
        created() {
            
        },
        mounted() {
        
        },
        methods: {
            getPosterUrl(poster) {
                return FS && poster ? FS.qxUtil.getFscLinkByOpt({
                    id: poster,
                    webp: true
                }) : dhtEmpty;
            },
            formatDuration(duration) {
                return FS && duration ? FS.qxUtil.formatSeconds(duration): duration;
            },
            preview() {
                const _this = this;
                seajs ? seajs.use('qx-modules/video/pop-video', function (Video) {
                    const src = FS ? FS.qxUtil.getFscStreamLink(_this.data.path + '.' + _this.data.ext) : _this.data.path;
                    const poster = _this.getPosterUrl(_this.data.thumb_path);
                    new Video({
                        src,
                        poster
                    });
                }) : alert('当前环境不支持预览');
            }
        }
    }
</script>
<style lang='scss' scoped>
.feed-session-message-videoMsg {
    display: inline-block;
    position: relative;
    .video-poster {
        min-height: 48px;
        min-width: 48px;
        background-color: rgba(0,0,0,.8);
        border-radius: 8px;
        cursor: pointer;
        img {
            display: block;
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
        }
        .duration {
            position: absolute;
            bottom: 0;
            right: 5px;
            color: #FFF;
        }
    }
}
</style>