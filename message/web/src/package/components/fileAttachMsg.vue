<template>
    <div class="feed-session-message-attachMsg">
         <div class="file-upload-item">
            <div class="file-item-wrap" @click="preview">
                <div :class="['file-item-icon', iconCls(data.ext)]"></div>
                <div class="file-item-name">{{data.name}}</div>
                <div class="file-item-size">{{formatFileSize(data.size)}}</div>
            </div>
        </div>
    </div>
</template>

<script type='text/javascript'>
    export default {
        props: {
            data:{
                type: Object,
                default: {}
            }
        },
        data() {
            return {}
        },
        created() {
        },
        mounted() {
        
        },
        methods: {
            preview() {
                FS ? FS.qxUtil.previewDoc({
                    path: this.data.path + '.' + this.data.ext,
                    name: this.data.name
                }) : alert('当前环境不支持预览');
            },
            iconCls(name) {
                return `file-item-${FS ? FS.util.getFileType({name:'.'+name},true,true) : 'common'}`;
            },
            formatFileSize(size) {
                let unit = "B";
                if (size > 1024) {
                    unit = "K";
                    size = size / 1024;
                }
                if (size > 1024) {
                    unit = "M";
                    size = size / 1024;
                }
                if (size > 1024) {
                    unit = "G";
                    size = size / 1024;
                }
                size = Math.round(size);
                return `${size}${unit}`;
            },
        }
    }
</script>
<style lang='scss' scoped>
    .feed-session-message-attachMsg{
        width: 100%;
        .file-upload-item {
            position: relative;
            display: flex;
            height: 65px;
            line-height: 64px;
            width: 300px;
            box-sizing: border-box;
            transition: all 0.3s ease-in;
            .file-item-icon {
                height: 30px;
                width: 30px;
                margin: 0 12px;
                align-self: center;
                flex-basis: 30px;
                background-color: #eee;
                flex-shrink: 0;
                background-repeat: center center no-repeat;
                background-size: 30px 30px;
            }
            .file-item-wrap {
                width: 0;
                position: relative;
                display: flex;
                flex-grow: 1;
                flex-shrink: 1;
                align-items: center;
                border-bottom: 1px solid #eaeff3;
                z-index: 10;
                background-color: #f2f3f5;
                overflow: hidden;
                cursor: pointer;
            }
            .file-item-name {
                font-size: 16px;
                flex-grow: 4;
                flex-shrink: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .file-item-size {
                // padding: 0 10px;
                color: #67717f;
                flex-basis: 30px;
                flex-shrink: 0;
                margin: 0 20px;
            }
            .file-item-status {
                flex-shrink: 0;
                flex-basis: 40px;
                font-size: 12px;
                text-align: center;
                margin-right: 12px;
            }
            .file-item-fail {
                color: #f35959;
            }
            .file-item-ready {
                color: #0385e1;
            }
            .file-item-success {
                color: #16263c;
            }
            .file-item-progress {
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .file-item-delete {
                height: 32px;
                line-height: 32px;
                flex-basis: 48px;
                flex-shrink: 0;
                text-align: center;
                font-size: 15px;
                color: #fff;
                background-color: #f35959;
                transition: width 0.3s ease-in;
                z-index: 5;
            }
            &.file-upload-item-delete {
                .file-item-content {
                    margin-left: -90px;
                }
            }
            .file-item-common {
                background-image: url('../assets/images/common.svg');
            }
            .file-item-txt {
                background-image: url('../assets/images/txt.svg');
            }
            .file-item-pdf {
                background-image: url('../assets/images/pdf.svg');
            }
            .file-item-doc {
                background-image: url('../assets/images/doc.svg');
            }
            .file-item-xls {
                background-image: url('../assets/images/xls.svg');
            }
            .file-item-ppt {
                background-image: url('../assets/images/ppt.svg');
            }
            .file-item-mp3 {
                background-image: url('../assets/images/mp3.svg');
            }
            .file-item-zip {
                background-image: url('../assets/images/zip.svg');
            }
            .file-item-jpg {
                background-image: url('../assets/images/jpg.svg');
            }
            .file-item-mov {
                background-image: url('../assets/images/mov.svg');
            }
            .file-item-mp4 {
                background-image: url('../assets/images/mov.svg');
            }
            .file-item-amr {
                background-image: url('../assets/images/mov.svg');
            }

            &:last-child {
                .file-item-wrap {
                    border-bottom: none;
                }
            }
        }
    }
</style>