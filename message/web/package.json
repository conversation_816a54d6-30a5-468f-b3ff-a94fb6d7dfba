{"name": "web-message", "version": "1.0.0", "description": "An ui project base on Vue.js ", "author": "lipr9703", "license": "ISC", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --open --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules"}, "repository": {"type": "git", "url": "http://git.firstshare.cn/bigfe/plugins.git"}, "keywords": ["message"], "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "devDependencies": {"fx-ui": "^1.1.9", "vue": "^2.5.11", "babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-preset-env": "^1.6.0", "babel-preset-stage-3": "^6.24.1", "cross-env": "^5.0.5", "css-loader": "^0.28.7", "node-sass": "^4.5.3", "sass-loader": "^6.0.6", "url-loader": "^0.5.8", "file-loader": "^1.1.4", "vue-loader": "^13.0.5", "vue-template-compiler": "^2.4.4", "webpack": "^3.6.0", "webpack-dev-server": "^2.9.1"}}