/**
 * @desc: 插件公共Class
 * @author: wangshaoh
 * @date: 12/29/21
 */

export default class Plugin_Base {

    options() {
        return {
            defMasterFields: {}, // 主对象默认字段映射
            defMdFields: {},     // 从对象默认字段映射
            masterFields: {},    // 主对象实际字段映射
            mdFields: {},        // 从对象实际字段映射
        }
    }

    constructor(pluginService, pluginParam) {
        this.childCom = [];
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
        this.request = pluginService.api.request;
        this._initOptions(pluginParam);
        this._cacheMdApiName(pluginParam);
        this._uploadLog(pluginService, pluginParam);
    }

    // 缓存所有子组件;
    cacheChildren(children) {
        let c = Array.isArray(children) ? children : [children];
        this.childCom = this.childCom.concat(c);
    }

    // 缓存所有支持当前插件的从对象apiname
    _cacheMdApiName(pluginParam) {
        this._allPluginMdApiName = [];
        if (pluginParam.params && pluginParam.params.details) {
            this._allPluginMdApiName = pluginParam.params.details.map(item => item.objectApiName);
        }
    }

    // 获取开关状态
    getConfig(key){
        return this.pluginParam.bizStateConfig[key]
    }

    // 获取所有支持当前插件的从对象apiname
    getAllMdApiName() {
        return this._allPluginMdApiName;
    }

    // 缓存所有映射字段；
    _initOptions(pluginParam) {
        let opt = this.options() || {};
        opt.masterFields = opt.masterFields || {};
        opt.mdFields = opt.mdFields || {};
        opt.masterFields = Object.assign({}, opt.defMasterFields, pluginParam.params.fieldMapping);
        if (pluginParam.params.details) {
            pluginParam.params.details.forEach(item => {
                opt.mdFields[item.objectApiName] = Object.assign({}, opt.defMdFields, item.fieldMapping);
            })
        }
        this._options = opt;
    }

    // 获取某个字段
    getFields(key = '', mdApiName = '') {
        let opt = this._options || {masterFields: {}, mdFields: {}};
        return opt.masterFields[key] || (opt.mdFields[mdApiName] && opt.mdFields[mdApiName][key]);
    }

    // 获取所有主对象字段
    getMasterFields(){
       return this._options.masterFields;
    }

    // 获取所有从对象字段
    getMdFields(mdApiName = ''){
        let opt = this._options;
        let mdFields = opt.mdFields[mdApiName] || Object.values(opt.mdFields)[0] || {};
        return Object.assign({}, mdFields);
    }

    // 获取所有映射字段
    getAllFields(mdApiName = '') {
        let opt = this._options;
        let mdFields = opt.mdFields[mdApiName] || Object.values(opt.mdFields)[0] || {};
        return Object.assign({}, opt.masterFields || {}, mdFields);
    }

    /**
     * @desc 获取某个插件的字段映射
     * @param pluginName 插件名称
     * @param apiName 从对象apiname
     * @returns {{}}
     */
    getPluginFields(pluginName = '', apiName = ''){
        let plugin = this.pluginService.api.getPlugins().find(item => item.pluginApiName === pluginName);
        if(!plugin) return;
        let masterFields = plugin.params.fieldMapping || {};
        let details = plugin.params.details || [];
        let md = apiName ? details.find(item => item.objectApiName === apiName) : details[0];
        let mdFields = md?.fieldMapping || {};
        return {...masterFields, ...mdFields};
    }

    // 有某个插件
    hasPlugin(pluginName, mdApiName){
        let plugin = this.pluginService.api.getPlugins().find(item => item.pluginApiName === pluginName);
        if(!plugin) return false;
        let details = plugin.params.details || [];
        let md = details.find(item => item.objectApiName === mdApiName);
        return !!md
    }

    // 获取多个插件的映射字段
    getSomePluginFields(pluginList = [], mdApiName = ''){
       let r = this.getAllFields(mdApiName);
       pluginList.forEach(pn => {
           let f = this.getPluginFields(pn, mdApiName) || {};
           r = Object.assign(r,f);
       });
       return r;
    }

    // 多语翻译
    i18n(){
        return this.pluginService.api.i18n(...arguments)
    }

    alert(msg){
        this.pluginService.api.alert(msg);
    }

    getHook() {
        return [];
    }

    showLoading(){
        this.pluginService.api.showLoading();
    }

    hideLoading(){
        this.pluginService.api.hideLoading();
    }

    async runPlugin(name, param = {}) {
        if(param) param._plugin = this.pluginService;
        let r = await this.pluginService.run(name, param);
        if (r && r.StatusCode === 0) return r.Value;
    }

    runPluginSync(name, param = {}) {
        if(param) param._plugin = this.pluginService;
        let r = this.pluginService.runSync(name, param);
        if (r && r.StatusCode === 0) return r.Value;
    }

    // 收集所有子组件和自己的注册事件；子组件事件在前；
    apply(pluginService, pluginParam) {
        let args = arguments;
        let hooks = [];
        this.childCom.forEach(com => {
            if (com.apply) {
                hooks = hooks.concat(com.apply(...args));
            } else if (com.getHook) hooks = hooks.concat(com.getHook(pluginService, pluginParam));
        });
        let selfHook = this.getHook(pluginService, pluginParam);
        hooks = hooks.concat(selfHook);
        return hooks;
    }

    /**
     * 上报日志
     *
     * @param {Object} pluginService 
     * @param {Object} pluginParam 
     */
    _uploadLog(pluginService, pluginParam) {
        // 插件使用日志
        // https://wiki.firstshare.cn/pages/viewpage.action?pageId=225932438

        try {
            pluginService.api.bizLog.log({
                eventId: 'fs-crm-sfa-plugin-used',
                eventType: 'PROD',
                eventName: 'pv',
                // 对象ApiName
                apiName: pluginParam.describe.objectApiName,
                // 插件名api_name
                module: pluginParam.describe.pluginApiName,
            });
        } catch(e) {}
    }

    /**
     * @desc 发送日志
     * @param param: {
                eventId: 'fs-crm-sfa-plugin-used',
                eventType: 'PROD',
                eventName: 'pv',
                // 对象ApiName
                apiName: pluginParam.describe.objectApiName,
                // 插件名api_name
                module: pluginParam.describe.pluginApiName,
            }
     */
    sendLog(param = {}){
        try {
            this.pluginService.api.bizLog.log(
                param
            );
        } catch(e) {}
    }

    destroyChildren(){
        this.childCom.forEach(com => {
            com.destroy && com.destroy();
        });
    }

    // 销毁钩子
    destroyHook(){
        
    }

    destroy(){
        this.destroyHook && this.destroyHook();
        this.destroyChildren();
    }


}
