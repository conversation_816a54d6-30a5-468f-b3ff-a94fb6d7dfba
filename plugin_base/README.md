# 插件的一些通用功能，可用可不用

**_base 使用_**

    

    `
       import {Base} from '@bizcomponents/plugins_base/plugins_base'
       import Children from '../Children'
       
       class A extends Base{
            constructor(pluginService, pluginParam) {
                   super(...arguments);
                   // 模拟有子组件
                   this.Children = new Children(pluginService, pluginParam, this);
                   // 缓存子组件；
                   this.cacheChildren(this.Children);
            },
            
            // 固定函数，收集注册事件勾子；
            // 如果子组件也有注册事件，在子组件中也这么写；Base统一收集
            getHook(){
                return [
                    {
                        event: 'form.render.before',
                        functional: this._beforeRender.bind(this)
                    },
                ];
            }
               
       }
       
       
    `
