const info = {
    "ava.object_form.onsale.change_field_tip_2": "更换{0}将清空已选产品，确认更换？",
    "ava.object_form.order.overwrite_delivery_address_tip": "是否用联系人的地址覆盖当前收货地址？",
    "ava.object_form.order.account_balance_insufficient": "客户账户余额不足",
    "ava.object_form.order.account_balance_insufficient_tip": "该客户的可用账户余额不足，确认继续提交订单吗？",
    "ava.object_form.order.order_product.collapse_up": "向上收起",
    "ava.object_form.order.order_product.expand_products": "展开共{0}件产品",
    "ava.object_list.form_pop.total_str": "共{0}件",
    "ava.object_form.order.order_product.required_field_missing_tip": "该产品有必填字段未填写",
    "ava.objform.errorandmodify": "“{0}”填写有误，请点击修改",
    "ava.object_form.onsale.close_tip_copy": "已关闭的订单产品不允许复制",
    "ava.object_form.onsale.close_tip_delete": "已关闭的订单产品不允许删除",
    "ava.object_form.onsale.pricebook_not_avaliable_tip_info": "当前价目表不可用已清空，请知悉！",
    "ava.object_form.onsale.edit_sale_contract_tip_info":"更换{0}，将删除原合同明细行对应的产品，是否继续？"
}

//固定格式，fssub依赖 -start
module.exports = {
    info
}
//固定格式，fssub依赖 -end