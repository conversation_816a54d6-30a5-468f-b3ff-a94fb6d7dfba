export class MDRender {

    constructor(context) {
        let {bizStateConfig} = context;
        this.bizStateConfig = bizStateConfig;
    }

    mdRenderBefore(pluginExecResult, options) {
        let self = this;
        let preData = pluginExecResult && pluginExecResult.preData;
        let preHandleOutFields = preData && preData.handleOutFields || [];
        if(preData && preData.mdBottomComs){
            // 去掉通用paas_mdcount组件
            const mdCountIndex = preData.mdBottomComs.findIndex(com => com.name == "paas_mdcount");

            mdCountIndex > -1 && preData.mdBottomComs.splice(mdCountIndex, 1);
            // 展示结算组件兜底样式
            preData.mdBottomComs.push({name: "order_settlement"})
        }
        let describeLayout = options.dataGetter.getDescribeLayout();
        let component = this.getSalesOrderProductCmpt(describeLayout);
        let renderType = component && component.render_type;
        let mdChildRenderTypes = {};
        if (renderType === 'compact') {
            Object.assign(mdChildRenderTypes, {
                'all': 'compact'
            })
        }
        let mdChildRenderTypesResult = Object.assign({}, preData.mdChildRenderTypes, mdChildRenderTypes);
        return Object.assign({}, preData, {
            mdChildRenderTypes: mdChildRenderTypesResult,

            handleOutFields: [...preHandleOutFields, (opt) => {
                let openMultiUnit = self.bizStateConfig.isOpenMultipleUnit();
                let unitFieldName = openMultiUnit ? 'actual_unit' : 'unit';
                let blackFields = ['product_price', unitFieldName, 'quantity'];
                opt = opt || {};
                opt.fields = (opt.fields || []).filter(it => !blackFields.includes(it.field_name));
                return opt;
            }]
        })
    }

    itemRenderBeforeSync(pluginExecResult, options) {
        let preData = pluginExecResult && pluginExecResult.preData && pluginExecResult.preData;
        let preFooterComs = preData && preData.footerComs || [];
        return Object.assign({}, preData, {
            footerComs: [{name: "price-unit-cmpt"}, ...preFooterComs],
        })
    }

    getSalesOrderProductCmpt(describeLayoutResult) {
        let components = describeLayoutResult && describeLayoutResult.layout && describeLayoutResult.layout.components;
        return components && components.length && components.find(it => it.ref_object_api_name === 'SalesOrderProductObj');
    }
}