import {cloneDeep, equals, i18n, isEmpty, uuid} from "../../pluginbase-ava/package/pluginutils";
import {BackCalculation} from './BackCalculation';

export class MasterFieldEdit {

    constructor(context) {
        let {pluginApi, requestApi} = context;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
        this.backCalculation = new BackCalculation(context);
    }

    masterFieldEditAfter(pluginExecResult, options) {
        let {fieldName} = options;
        if (fieldName === 'account_id') {
            return this.accountEditAfter(options);
        } else if (fieldName === 'order_amount') {
            return this.backCalculation.orderAmountEditAfter(options);
        } else if (fieldName === 'sale_contract_id') {
            return this.saleContractEditAfter(pluginExecResult, options);
        } else if (fieldName === 'ship_to_id') {
            return this.shipToIdEditAfter(options);
        } else if (fieldName === 'ship_to_party') {
            return this.shipToPartyEditAfter(options);
        }
    }

    masterFieldEditEnd(pluginExecResult, options) {

    }

    accountEditAfter(options) {
        let {dataGetter, changeData, masterObjApiName} = options;
        let masterData = dataGetter.getMasterData();
        let masterDescribe = dataGetter.getDescribe && dataGetter.getDescribe(masterObjApiName);
        let { ship_to_party: shipToParty } = masterDescribe && masterDescribe.fields || {};
        let isNeedGetAccountAddress = shipToParty ? shipToParty.default_value === "$account_id$" : true;//是否需要获取地址信息
        let { account_id: accountId, account_id__r: accountName } = changeData;
        let tempMasterData = Object.assign({}, masterData, changeData, (shipToParty && isNeedGetAccountAddress) && { ship_to_party: accountId, ship_to_party__r: accountName });
        let defaultAccountAddress = isNeedGetAccountAddress ? this.getDefaultAccountAddress(accountId, tempMasterData, options) : Promise.resolve();
        let defaultWareHouse = this.getDefaultWarehouse(accountId, options);
        let pageId = dataGetter.getPageId();
        let token = 'sales_order_' + uuid();
        this.pluginApi.showSingletonLoading(token, {}, pageId);
        return Promise.all([defaultAccountAddress, defaultWareHouse]).then(resultList => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            let updateData = {};
            resultList && resultList.length && resultList.forEach(result => {
                Object.assign(updateData, result || {});
            });
            Object.assign(changeData, updateData);
        }).catch(err => {
            this.pluginApi.hideSingletonLoading(token, pageId);
            this.pluginApi.showToast(err);
        })
    }

    saleContractEditAfter(pluginExecResult, options) {
        let detailObjApiName = 'SalesOrderProductObj'
        let {dataGetter, changeData, masterObjApiName, dataUpdater, formApis, lookupData} = options
        if (isEmpty(changeData)) {
            return Promise.resolve();
        }
        let saleContractChanged = !(typeof changeData.sale_contract_id === 'undefined');
        let detailDataList = dataGetter && dataGetter.getDetail(detailObjApiName)
        if (!saleContractChanged || !detailDataList || !detailDataList.length) {
            return Promise.resolve();
        }
        let masterDescribe = dataGetter.getDescribe && dataGetter.getDescribe(masterObjApiName);
        let saleContractField = masterDescribe && masterDescribe.fields && masterDescribe.fields['sale_contract_id'];
        let fieldLabel = saleContractField && saleContractField.label || '';
        let message = i18n('ava.object_form.onsale.edit_sale_contract_tip_info', [fieldLabel])/*更换{0}，将删除原合同明细行对应的产品，是否继续？*/;
        return this.pluginApi.confirmPromise('', message).then(async confirm => {
            if (confirm) {//【订单产品.销售合同产品】有值的行清掉，并且要触发一次计算
                let deleteDataIndexList = detailDataList.filter(it => (!isEmpty(it.sale_contract_line_id))).map(it => it.dataIndex);
                let delDatas = dataUpdater && dataUpdater.del(detailObjApiName, deleteDataIndexList);
                await formApis.triggerCalAndUIEvent({objApiName: detailObjApiName, delDatas});
                return true;//校验价目表
            } else {
                delete changeData['sale_contract_id']
                delete changeData['sale_contract_id__r']
            }
        }).then(checkPriceBook => {
            let isRealPriceMode = lookupData && lookupData.constraint_mode === 'real_price';//是否是取价模式
            let masterData = dataGetter && dataGetter.getMasterData();
            let tempMasterData = Object.assign({}, masterData, changeData)
            let {account_id, price_book_id, partner_id} = tempMasterData;
            if (!checkPriceBook || !isRealPriceMode || isEmpty(price_book_id)) {
                return true;//不校验价目表，则认为价目表可用
            }
            return this.requestApi.validateAccountPriceBook({
                account_id,
                partner_id,
                price_book_id,
                object_data: tempMasterData
            })
        }).then(available => {
            if (!available) {
                Object.assign(changeData, {price_book_id: null, price_book_id__r: null})
                this.pluginApi.alert(i18n("ava.object_form.onsale.pricebook_not_avaliable_tip_info")/*当前价目表不可用已清空，请知悉！*/)
            }
        }).catch(error => {
            this.pluginApi.showToast(error)
        })
    }

    shipToIdEditAfter(options) {
        let defaultIsExpression = function (field) {
            return field && field.default_is_expression;
        };
        let {masterObjApiName, dataGetter, changeData, lookupData} = options;
        let layoutFields = dataGetter.getLayoutFields && dataGetter.getLayoutFields(masterObjApiName);
        let {ship_to_add: shipToAddField, ship_to_tel: shipToTelField, dht_order_delivery_address__c: deliveryAddressComp} = layoutFields || {};
        let objectDescribe = dataGetter.getDescribe(masterObjApiName);
        let {ship_to_add: shipToAddDesc, ship_to_tel: shipToTelDesc} = objectDescribe && objectDescribe.fields || {};
        if (lookupData) {//修改的收货人，则更新收货人电话、收货人地址
            let {add = null, mobile = null} = lookupData;
            let masterData = dataGetter.getMasterData();
            let shipToAdd = masterData && masterData.ship_to_add;
            if ((shipToTelField || deliveryAddressComp) && !defaultIsExpression(shipToTelDesc)) {
                changeData.ship_to_tel = mobile;//更新收货人电话
            }
            if ((shipToAddField || deliveryAddressComp) && !defaultIsExpression(shipToAddDesc)) {
                let updateData = {
                    ship_to_add: add,
                    key_selected_account_address_id: null,
                    key_is_ship_to_add: false
                };
                if (!shipToAdd) {//没有收货人地址，更新收货人地址
                    Object.assign(changeData, updateData);
                } else {//弹窗提示是否用联系人的地址覆盖当前收货地址
                    let tip = i18n('ava.object_form.order.overwrite_delivery_address_tip'/*是否用联系人的地址覆盖当前收货地址？*/);
                    return this.pluginApi.confirmPromise(null, tip).then(result => {
                        if (result) {
                            Object.assign(changeData, updateData);
                        }
                    })
                }
            }
        } else {
            if ((shipToAddField || deliveryAddressComp) && !defaultIsExpression(shipToAddDesc)) {
                changeData.ship_to_add = null;
            }
            if ((shipToTelField || deliveryAddressComp) && !defaultIsExpression(shipToTelDesc)) {
                changeData.ship_to_tel = null;
            }
        }
    }

    shipToPartyEditAfter(options) {
        let { dataGetter, changeData } = options;
        let masterData = dataGetter.getMasterData();
        let tempMasterData = Object.assign({}, masterData, changeData);
        let accountId = tempMasterData['ship_to_party'];
        return this.getDefaultAccountAddress(accountId, tempMasterData, options).then(defaultAddr => {
            Object.assign(changeData, (defaultAddr || {}));
        });
    }

    getDefaultAccountAddress(accountId, masterData, options) {
        let {masterObjApiName, dataGetter} = options;
        let masterDescribe = dataGetter.getDescribe && dataGetter.getDescribe(masterObjApiName);
        let layoutFields = dataGetter.getLayoutFields && dataGetter.getLayoutFields(masterObjApiName);
        let {ship_to_id: shipToIdField, ship_to_add: shipToAddField, ship_to_tel: shipToTelField, dht_order_delivery_address__c: deliveryAddressComp} = layoutFields || {};
        if (!shipToIdField && !shipToAddField && !shipToTelField && !deliveryAddressComp) {
            return Promise.resolve();
        }
        let defaultIsExpression = function (field) {
            return field && field.default_is_expression;
        };
        let {ship_to_add_control: ship2AddControlField, ship_to_add: shipToAddDesc, ship_to_tel: shipToTelDesc} = masterDescribe && masterDescribe.fields || {}
        let wheres = ship2AddControlField && ship2AddControlField.wheres;
        let { ship_to_add, ship_to_tel, ship_to_id } = masterData || {};
        return this.requestApi.getDefaultAccountAddr(accountId, null, cloneDeep(wheres), masterData)
            .then(defaultAddr => {
                let {_id = null, address = null, contact_way = null, contact_id = null, contact_id__r = null, is_ship_to_add} = defaultAddr || {};
                let updateData = {
                    key_selected_account_address_id: _id,
                    dht_order_delivery_address__c: _id,
                    key_is_ship_to_add: is_ship_to_add
                };
                let addressEquals = equals(ship_to_add, address);
                if (!addressEquals && (shipToAddField || deliveryAddressComp) && !defaultIsExpression(shipToAddDesc)) {
                    updateData['ship_to_add'] = address;
                }
                let shipToTelEquals = equals(ship_to_tel, contact_way);
                if (!shipToTelEquals && (shipToTelField || deliveryAddressComp) && !defaultIsExpression(shipToTelDesc)) {
                    updateData['ship_to_tel'] = contact_way;
                }
                let shipToIdEquals = equals(ship_to_id, contact_id);
                if (!shipToIdEquals && (shipToIdField || deliveryAddressComp)) {
                    updateData['ship_to_id'] = contact_id;
                    updateData['ship_to_id__r'] = contact_id__r;
                }
                return updateData;
            }).catch(err => {
                this.pluginApi.showToast(err);
            });

    }

    getDefaultWarehouse(account_id, options) {
        let {masterObjApiName, dataGetter} = options;
        let layoutFields = dataGetter.getLayoutFields && dataGetter.getLayoutFields(masterObjApiName);
        let shippingWareHouseField = layoutFields && layoutFields['shipping_warehouse_id'];
        //布局中有收货仓库字段才拉取默认仓库
        return shippingWareHouseField ? this.requestApi.queryUpValid(account_id).then(res => {
            let {id, name} = res ? res : {};
            return {
                shipping_warehouse_id: id,
                shipping_warehouse_id__r: name
            };
        }).catch(err => {
            this.pluginApi.showToast(err);
        }) : Promise.resolve();
    }
}