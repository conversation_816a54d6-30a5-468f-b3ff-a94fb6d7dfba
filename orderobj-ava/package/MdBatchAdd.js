import requireUtils from 'fs-hera-api/api/utils/requireUtils'
import {i18n} from "../../pluginbase-ava/package/pluginutils";

export default class MdBatchAdd {

    constructor(options) {
        let { bizStateConfig, pluginApi, requestApi, pluginParam } = options || {}
        this.pluginParam = pluginParam
        this.bizStateConfig = bizStateConfig
        this.pluginApi = pluginApi
        this.requestApi = requestApi
        this.initializeHandler(options)
    }

    async initializeHandler(options) {
        let obj = await requireUtils.requireAsync("../../otc-base-ability/proxy/index")
        let proxy = obj && obj.default
        if (!proxy) return
        let { bizStateConfig, pluginApi, requestApi, pluginParam } = options || {}
        let mapper = proxy.common.mapper.objectFieldsMapperFromPlugin(pluginParam)
        this.handler = {
            ...proxy.object.batch_add_handler, 
            mapper,
            plugin<PERSON>pi, 
            requestApi,
            pluginParam, 
            bizStateConfig,
            lookup_field_configs: [{
                // before
                lookup_field_api_name: 'sale_contract_line_id',
                pre_main_field_api_name: 'sale_contract_id',



                // select_form_params

                select_associated_pre_fill_fields: ['account_id', 'mc_currency', 'mc_exchange_rate', 'mc_exchange_rate_version', 'mc_functional_currency'],
                select_detail_main_field_api_name: 'sale_contract_id',
                select_source_main_object_api_name: 'SaleContractObj',
                select_source_detail_object_api_name: 'SaleContractLineObj',
                select_from_source: { 'SalesOrderObj': 'SelectSaleCotnractLines' },



                // select_filter
                filter_config: {
                    manual_gift: {
                        filter_bom_sub: true,
                        filter_policy_gift: true,
                        filter_select_detail: true,
                        filter_rebate_product: true,
                    },
                    normal: {
                        filter_bom_sub: true,
                        filter_policy_gift: true,
                        filter_select_detail: true,
                        filter_rebate_product: true,
                    }
                },


                // after
                detail_forbid_calc_fields: ['product_price', 'discount', 'quantity', 'extra_discount'],
                valid_price_book_show_prompt: false,
            }, {
                // before
                lookup_field_api_name: 'quote_line_id',
                pre_main_field_api_name: 'quote_id',



                // select_form_params
                select_associated_pre_fill_fields: ['account_id', 'mc_currency', 'mc_exchange_rate', 'mc_exchange_rate_version', 'mc_functional_currency'],
                select_detail_main_field_api_name: 'quote_line_id',
                select_source_main_object_api_name: 'QuoteObj',
                select_source_detail_object_api_name: 'QuoteLineObj',
                select_from_source: { 'SaleContractObj': 'SaleCotnractSelectQuoteLines' },




                // select_filter
                filter_config: {
                    manual_gift: {
                        filter_bom_sub: true,
                        filter_policy_gift: true,
                        filter_select_detail: true,
                        filter_rebate_product: true,
                        filter_select_detail_follow_record_type: true,
                    },
                    normal: {
                        filter_bom_sub: true,
                        filter_policy_gift: true,
                        filter_select_detail: true,
                        filter_rebate_product: true,
                        filter_select_detail_follow_record_type: true,
                    }
                },



                // after
                valid_price_book_show_prompt: true,
            }]
        }
    }


    async beforeBatchAddDetailAction(pluginExecResult, options) { 
        if (this.handler) return await this.handler.beforeBatchAddDetailAction(pluginExecResult, options)
        return pluginExecResult && pluginExecResult.preData
    }


    async afterBatchAddDetailAction(pluginExecResult, options) { 
        if (this.handler) return await this.handler.afterBatchAddDetailAction(pluginExecResult, options)
        return pluginExecResult && pluginExecResult.preData
    }

    mdCloneAfter(pluginExecResult, options) {
        this.filterClosedData(options.newDatas, 'copy');
    }

    mdDeleteAfter(pluginExecResult, options) {
        this.filterClosedData(options.delDatas, 'delete');
    }

    filterClosedData(dataList, type) {
        if (!dataList || !dataList.length) {
            return;
        }
        let hasClosedData = dataList.some(it => it.close_status === true);
        if (hasClosedData) {//过滤掉已关闭的明细行
            let i = dataList.length;
            while (i--) {
                let objectData = dataList[i];
                let {close_status} = objectData;
                if (close_status === true) {
                    dataList.splice(i, 1);
                    hasClosedData = true;
                }
            }
            if (type === 'copy') {
                this.pluginApi.showToast(i18n('ava.object_form.onsale.close_tip_copy')/*已关闭的订单产品不允许复制*/);
            } else if (type === 'delete') {
                this.pluginApi.showToast(i18n('ava.object_form.onsale.close_tip_delete')/*'已关闭的订单产品不允许删除'*/);
            }
        }
    }
}