class SalesOrderConfig {

    initContext(context) {
        let {pluginApi} = context;
        this.pluginApi = pluginApi;
        this.staticPropertiesResult = {
            imagePlaceholder: undefined,//订单产品组件中图片默认占位图：FsImage组件中，placeholder属性需要的值
        };
    }

    getImagePlaceholder() {
        return this.staticPropertiesResult.imagePlaceholder;
    }

    async formRenderBefore(pluginExecResult, options) {
        let result = await this.pluginApi.runPlugin('SalesOrderObj.initStaticProperties.after', options);
        Object.assign(this.staticPropertiesResult, result);
    }
}

export default new SalesOrderConfig();