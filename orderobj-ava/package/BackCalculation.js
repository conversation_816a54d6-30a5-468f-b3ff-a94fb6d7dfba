import {
    divide,
    formatValueDecimalPlaces,
    getPercentileFieldDecimalPlacesFromDescribe,
    getPriceFieldApiName,
    getSalesPriceFieldApiName,
    isEmpty,
    multiply
} from "../../pluginbase-ava/package/pluginutils";

export class BackCalculation {

    constructor(context) {
        let {bizStateConfig} = context;
        this.bizStateConfig = bizStateConfig;
    }

    orderAmountEditAfter(options) {
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        if (isOpenManualChangePrice) {
            return;
        }
        let isReverseOrderDiscount = this.bizStateConfig.isReverseOrderDiscount();
        if (!isReverseOrderDiscount) {
            return;
        }
        let dynamicAllowAmortize = this.bizStateConfig.dynamicAllowAmortize();
        if (dynamicAllowAmortize) {
            return;
        }
        let {masterObjApiName, dataGetter, changeData} = options;
        let objectDescribe = dataGetter.getDescribe(masterObjApiName);
        let orderAmountField = objectDescribe && objectDescribe.fields && objectDescribe.fields['order_amount'];
        let defaultValue = orderAmountField && orderAmountField.default_value;
        let isMatch = this.isFieldFormulaMatchedDefault('product_amount', 'discount', defaultValue);
        if (!isMatch) {
            return;
        }
        let masterData = dataGetter.getMasterData();
        let tempMasterData = Object.assign({}, masterData, changeData);
        let {order_amount, product_amount} = tempMasterData || {};
        if (isEmpty(order_amount)) {
            order_amount = '0';
        }
        let orderAmount = parseFloat(order_amount);
        if (isEmpty(product_amount)) {
            return;
        }
        let productAmount = parseFloat(product_amount);
        if (productAmount === 0) {//产品合计为0，不反算折扣
            return;
        }
        let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe('discount', objectDescribe);
        let discount = multiply(divide(orderAmount, productAmount), 100);
        discount = formatValueDecimalPlaces(discount, decimalPlaces);
        Object.assign(changeData, {
            discount: "" + discount
        });
    }

    salesPriceEditAfter(options) {
        let {masterObjApiName, dataGetter, changeData, dataIndex, objApiName, fieldName: salesPriceFieldName} = options;
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        if (isOpenManualChangePrice && (objApiName === 'SalesOrderProductObj')) {
            return;
        }
        let enablePeriodicProductPlugin = this.bizStateConfig.enablePeriodicProductPlugin(masterObjApiName);
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let priceFieldName = enablePeriodicProductPlugin ? 'price_per_set' : getPriceFieldApiName(objApiName);
        let salesPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields[salesPriceFieldName];
        let defaultValue = salesPriceField && salesPriceField.default_value;
        let matched = this.isFieldFormulaMatchedDefault(priceFieldName, 'discount', defaultValue);
        if (!matched) {
            return;
        }
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let salesPrice = tempData[salesPriceFieldName];
        if (isEmpty(salesPrice)) {
            salesPrice = '0';
        }
        salesPrice = parseFloat(salesPrice);
        let price = tempData[priceFieldName];
        if (isEmpty(price)) {
            return;
        }
        price = parseFloat(price);
        if (price === 0) {//价格为0，不反算折扣
            return;
        }
        let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe("discount", objectDescribe);
        let discount = multiply(divide(salesPrice, price), 100);
        discount = discount ? formatValueDecimalPlaces(discount, decimalPlaces) : 0;
        Object.assign(changeData, {
            discount: "" + discount
        });
    }

    subtotalEditAfter(options) {
        let {masterObjApiName, dataGetter, changeData, dataIndex, objApiName, fieldName: subtotalFieldName} = options;
        let isOpenManualChangePrice = this.bizStateConfig.isOpenManualChangePrice();
        if (isOpenManualChangePrice && (objApiName === 'SalesOrderProductObj')) {
            return;
        }
        if (this.bizStateConfig.isOpenStratifiedPricing()) {//开启分层，走分层反算逻辑
            return this.subtotalEditAfterByStratifiedPricing(options);
        }
        let enablePeriodicProductPlugin = this.bizStateConfig.enablePeriodicProductPlugin(masterObjApiName);
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let priceFieldName = enablePeriodicProductPlugin ? 'price_per_set' : getPriceFieldApiName(objApiName);
        let salesPriceFieldName = getSalesPriceFieldApiName(objApiName);
        let subtotalField = objectDescribe && objectDescribe.fields && objectDescribe.fields[subtotalFieldName];
        let defaultValue = subtotalField && subtotalField.default_value;
        let matched = this.isFieldFormulaMatchedDefault(salesPriceFieldName, 'quantity', defaultValue);
        if (!matched) {
            return;
        }
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let quantity = tempData['quantity'];
        if (isEmpty(quantity)) {
            return;
        }
        quantity = parseFloat(quantity);
        if (quantity === 0) {
            return;
        }
        let salesPriceField = objectDescribe && objectDescribe.fields && objectDescribe.fields[salesPriceFieldName];
        let {decimal_places, default_value} = salesPriceField || {};
        let subtotal = tempData[subtotalFieldName];
        if (isEmpty(subtotal)) {
            subtotal = '0';
        }
        subtotal = parseFloat(subtotal);
        let salesPrice = divide(subtotal, quantity);
        salesPrice = salesPrice ? formatValueDecimalPlaces(salesPrice, decimal_places) : 0;
        let isSalesPriceFormulaMatchedDefault = this.isFieldFormulaMatchedDefault(priceFieldName, 'discount', default_value);
        let price = tempData[priceFieldName];
        if (isEmpty(price)) {
            price = 0;
        }
        price = parseFloat(price);
        if (price === 0 || !isSalesPriceFormulaMatchedDefault) {//销售单价字段的默认值公式是否是默认的
            Object.assign(changeData, {
                sales_price: "" + salesPrice
            });
        } else {
            let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe("discount", objectDescribe);
            let discount = multiply(divide(salesPrice, price), 100);
            discount = discount ? formatValueDecimalPlaces(discount, decimalPlaces) : 0;
            Object.assign(changeData, {
                discount: "" + discount
            });
        }
    }

    subtotalEditAfterByStratifiedPricing(options) {
        let { dataGetter, changeData, dataIndex, objApiName, fieldName: subtotalFieldName } = options;
        let objectDescribe = dataGetter && dataGetter.getDescribe && dataGetter.getDescribe(objApiName);
        let subtotalField = objectDescribe && objectDescribe.fields && objectDescribe.fields[subtotalFieldName];
        let defaultValue = subtotalField && subtotalField.default_value;
        let matched = defaultValue === "$price_book_subtotal$/$price_book_discount$*$discount$";
        if (!matched) {
            return;
        }
        let objectData = dataGetter && dataGetter.getData(objApiName, dataIndex);
        let tempData = Object.assign({}, objectData, changeData);
        let { price_book_subtotal: priceBookSubtotal, price_book_discount: priceBookDiscount, [subtotalFieldName]: subtotal } = tempData;
        if (isEmpty(priceBookSubtotal)) {
            return;
        }
        priceBookSubtotal = parseFloat(priceBookSubtotal);
        if (priceBookSubtotal === 0) {
            return;
        }
        if (isEmpty(priceBookDiscount)) {
            priceBookDiscount = '0';
        }
        if (isEmpty(subtotal)) {
            subtotal = '0';
        }
        subtotal = parseFloat(subtotal);
        let discount = multiply(multiply(divide(subtotal, priceBookSubtotal), divide(priceBookDiscount, 100)), 100);
        let decimalPlaces = getPercentileFieldDecimalPlacesFromDescribe("discount", objectDescribe);
        discount = discount ? formatValueDecimalPlaces(discount, decimalPlaces) : 0;
        Object.assign(changeData, {
            discount: "" + discount
        });
    }

    isFieldFormulaMatchedDefault(variable1, variable2, defaultFormula, operator = '*') {
        let symbol_$ = '$';
        let case1 = `${symbol_$}${variable1}${symbol_$}${operator}${symbol_$}${variable2}${symbol_$}`;
        let case2 = `${symbol_$}${variable2}${symbol_$}${operator}${symbol_$}${variable1}${symbol_$}`;
        return defaultFormula === case1 || defaultFormula === case2;
    }
}