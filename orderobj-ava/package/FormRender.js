import {
    cloneDeep,
    equals,
    isClone,
    isConvert,
    isDraft,
    isEdit,
    isEmpty,
    uuid
} from "../../pluginbase-ava/package/pluginutils";

export class FormRender {

    constructor(context) {
        let {bizStateConfig, pluginApi, requestApi} = context;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.requestApi = requestApi;
    }

    formFetchDescribeLayoutAfter(pluginExecResult, options){
        let {describeLayout} = options;
        let components = describeLayout && describeLayout.layout && describeLayout.layout.components;
        let addressComp = components && components.find((a) => {
            return a.api_name === 'dht_order_delivery_address'
        })
        addressComp && (addressComp.type = 'form');
        this._handleAccountAndOrderSettleLayout(describeLayout)
        this.describeLayout = describeLayout;
        return {describeLayout};
    }

    formRenderBefore(pluginExecResult, options){
        let returnData=  {};
        let describeLayout = this.describeLayout;

        let orderSettleComponents = this.orderSettleComponents;
        if(orderSettleComponents){
            describeLayout && describeLayout.layout && describeLayout.layout.components.push(orderSettleComponents);
        }
        // 是否配置底部展示合计字段
        let isOrderMobileEditPageSummarySetting = this.bizStateConfig.isOrderMobileEditPageSummarySetting();

        // 底部展示合计 || 底部展示结算组件布局 = 走订单底部按钮组件
        if (isOrderMobileEditPageSummarySetting || (orderSettleComponents && orderSettleComponents.widget_style == "stickToBottom")) {
            returnData.actionBarRenderType = "orderButtonPanel"; //指定按钮组件类型
        }
        let preResult = pluginExecResult && pluginExecResult.preData;
        return Object.assign({}, preResult, returnData);
    }

    formRenderAfter(pluginExecResult, options) {
        let {dataGetter, dataUpdater, triggerCalFields, masterObjApiName} = options;
        let {getMasterData} = dataGetter || {};
        let masterData = getMasterData && getMasterData();
        let promiseList = [];
        let orderTime = this.getOrderTime(masterData);
        orderTime && promiseList.push(orderTime);
        let {account_id, isLieGongJiTuan} = masterData || {};
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        let masterDescribe = dataGetter.getDescribe && dataGetter.getDescribe(masterObjApiName);
        let masterLayout = dataGetter.getLayoutFields && dataGetter.getLayoutFields(masterObjApiName);
        let isEditMode = isEdit(sourceAction);
        let isDraftMode = isDraft(sourceAction);
        if (!isEditMode && !isDraftMode && account_id && !isLieGongJiTuan) {
            let defaultAccountAddress = this.getDefaultAccountAddress(sourceAction, masterData, masterLayout, masterDescribe);
            let defaultWarehouse = this.getDefaultWarehouse(sourceAction, masterData, masterLayout);
            defaultAccountAddress && promiseList.push(defaultAccountAddress);
            defaultWarehouse && promiseList.push(defaultWarehouse);
        }
        if (promiseList && promiseList.length) {
            let pageId = dataGetter.getPageId();
            let token = 'sales_order_' + uuid();
            this.pluginApi.showSingletonLoading(token, {}, pageId);
            return Promise.all(promiseList).then(resultList => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                let updateData = {};
                resultList && resultList.length && resultList.forEach(result => {
                    Object.assign(updateData, result || {});
                });
                dataUpdater && dataUpdater.updateMaster && dataUpdater.updateMaster(updateData);
                triggerCalFields && triggerCalFields.push(...Object.keys(updateData));
            }).catch(err => {
                this.pluginApi.hideSingletonLoading(token, pageId);
                this.pluginApi.showToast(err);
            })
        }
        return Promise.resolve();

    }

    getDefaultAccountAddress(sourceAction, masterData, masterLayout, masterDescribe) {
        let needGetAccountAddress;
        if (isConvert(sourceAction)) {
            let isOrder2QuoteDefaultValueCover = this.bizStateConfig.isOrder2QuoteDefaultValueCover();
            needGetAccountAddress = !!isOrder2QuoteDefaultValueCover;
        } else {
            let isCloneMode = isClone(sourceAction);
            needGetAccountAddress = !isCloneMode;
        }
        if (!needGetAccountAddress) {
            return Promise.resolve();
        }
        let {ship_to_id: shipToIdField, ship_to_add: shipToAddField, ship_to_tel: shipToTelField, dht_order_delivery_address__c: deliveryAddressComp} = masterLayout || {};
        if (!shipToIdField && !shipToAddField && !shipToTelField && !deliveryAddressComp) {
            return Promise.resolve();
        }
        let defaultIsExpression = function (field) {
            return field && field.default_is_expression;
        };
        let { ship_to_add_control: ship2AddControlField, ship_to_add: shipToAddDesc, ship_to_tel: shipToTelDesc, ship_to_party: shipToParty } = masterDescribe && masterDescribe.fields || {}
        let wheres = ship2AddControlField && ship2AddControlField.wheres;
        let { account_id, ship_to_add, ship_to_tel, ship_to_id, ship_to_party } = masterData || {};
        let accountId = shipToParty ? ship_to_party : account_id;
        return this.requestApi.getDefaultAccountAddr(accountId, null, cloneDeep(wheres), masterData)
            .then(defaultAddr => {
                let {_id = null, address = null, contact_way = null, contact_id = null, contact_id__r = null, is_ship_to_add} = defaultAddr || {};
                let updateData = {
                    key_selected_account_address_id: _id,
                    dht_order_delivery_address__c: _id,
                    key_is_ship_to_add: is_ship_to_add
                };
                let addressEquals = equals(ship_to_add, address);
                if (!addressEquals && (shipToAddField || deliveryAddressComp) && !defaultIsExpression(shipToAddDesc)) {
                    updateData['ship_to_add'] = address;
                }
                let shipToTelEquals = equals(ship_to_tel, contact_way);
                if (!shipToTelEquals && (shipToTelField || deliveryAddressComp) && !defaultIsExpression(shipToTelDesc)) {
                    updateData['ship_to_tel'] = contact_way;
                }
                let shipToIdEquals = equals(ship_to_id, contact_id);
                if (!shipToIdEquals && (shipToIdField || deliveryAddressComp)) {
                    updateData['ship_to_id'] = contact_id;
                    updateData['ship_to_id__r'] = contact_id__r;
                }
                return updateData;
            }).catch(err => {
                this.pluginApi.showToast(err);
            });
    }

    getDefaultWarehouse(sourceAction, masterData, masterLayout) {
        let needQueryUpValid = false;
        let {account_id, isFormFieldworkOrderGoods} = masterData || {};
        let shippingWareHouseField = masterLayout && masterLayout.shipping_warehouse_id;//布局中有收货仓库字段才拉取默认仓库
        if (shippingWareHouseField) {
            if (isConvert(sourceAction)) {
                let isOrder2QuoteDefaultValueCover = this.bizStateConfig.isOrder2QuoteDefaultValueCover();
                needQueryUpValid = !!isOrder2QuoteDefaultValueCover;
            } else {
                needQueryUpValid = !isFormFieldworkOrderGoods;
            }
        }
        return needQueryUpValid ? this.requestApi.queryUpValid(account_id)
            .then(res => {
                let {id, name} = res || {};
                return {
                    shipping_warehouse_id: id,
                    shipping_warehouse_id__r: name
                };
            }).catch(err => {
                this.pluginApi.showToast(err);
            }) : Promise.resolve();
    }

    getOrderTime(masterData) {
        let order_time = masterData.order_time;
        if (isEmpty(order_time)) {//如果下单日期没有值，设置为当前日期
            let date = new Date();
            date.setHours(0, 0, 0, 0);
            let time = date.getTime();
            return Promise.resolve({
                order_time: time
            });
        }
        return Promise.resolve();
    }

    /**
     * 处理客户账户组件和订单结算组件的关系，若是两者都存在，
     * 需要结合两者一起显示，客户账户组件不单独显示
     * @param describeAndLayout
     * @private
     */
    _handleAccountAndOrderSettleLayout(describeAndLayout) {
        let orderSettleComponents; // 订单结算组件
        let accountComponents; // 客户账户组件
        let accountIndex = -1; // 客户账户的在components中的位置，用于删除
        let orderSettleIndex = -1; // 订单结算组件的在components中的位置，用于删除
        const components = describeAndLayout && describeAndLayout.layout && describeAndLayout.layout.components;
        components && components.forEach((component, index) => {
            if (component.api_name === 'order_settlement') {
                orderSettleComponents = component;
                orderSettleIndex = index;
                if(!component.widget_style){
                    component.widget_style = "followMd";
                }
            } else if (component.api_name === 'dht_order_customer_account') {
                accountComponents = component;
                accountIndex = index;
            }
        });

        // 只有订单结算组件在订单产品下方时，才合并两个组件
        // 删除客户账户组件并移动到结算组件内部
        if (orderSettleComponents && accountComponents) {
            orderSettleComponents.dht_order_customer_account = accountComponents;
            if(orderSettleComponents.widget_style == "followMd"){
                components && components.splice(accountIndex, 1);
            }
        }
        this.orderSettleComponents = orderSettleComponents;
        // 订单产品下不展示结算组件，先从布局中删除结算组件
        if(orderSettleComponents && orderSettleComponents.widget_style == "stickToBottom"){
            components && components.splice(orderSettleIndex, 1);
        }
    }

    formRenderEnd(pluginExecResult, options) {
        let {dataGetter, dataUpdater} = options;
        let {getDetail, getDescribe} = dataGetter || {};
        let objectDescribe = getDescribe && getDescribe('SalesOrderProductObj');
        let detailDataList = getDetail && getDetail('SalesOrderProductObj');
        let fields = objectDescribe && objectDescribe.fields || {};
        let closeStatusFields = fields && fields.close_status;
        let fieldApiNames = Object.keys(fields);
        if (!isEmpty(closeStatusFields) && fieldApiNames && fieldApiNames.length) {
            detailDataList && detailDataList.length && detailDataList.forEach(detailData => {
                let {close_status} = detailData;
                if (close_status === true) {//关闭的明细行所有字段不允许编辑
                    dataUpdater && dataUpdater.setReadOnly && dataUpdater.setReadOnly({
                        objApiName: "SalesOrderProductObj",
                        dataIndex: detailData.dataIndex,
                        fieldName: fieldApiNames,
                        status: true,
                        biz: 'SalesOrderObj',
                        priority: 11
                    });
                }
            });
        }
    }
}