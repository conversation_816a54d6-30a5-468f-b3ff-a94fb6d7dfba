<view class="root">
    <!--头部组件统一通过dHeaderComs控制渲染-->
    <block wx:for="{{dHeaderComs}}" wx:key="name">
        <!-- 展示价格政策header -->
        <policy-detail-header wx:if="{{item.name === 'policy-detail-header'}}" render="{{refreshUI}}" bind:getDataDetail="getItemInfo"/>
    </block>
    <view class="content-container">
        <fs-image wx:if="{{dItemObj.showImage}}" i-class="product-img" src='{{dItemObj.imagePath}}'
                  imgStyle="width: 64px;height: 64px;"
                  placeholder="{{dItemObj.imagePlaceholder}}">
        </fs-image>
        <view class="field-container">
            <view style="display: flex; flex-direction: row; align-items: center; padding: 20rpx 24rpx; box-sizing: border-box;">
                <view wx:if="{{dTitleLeftBtns&&dTitleLeftBtns.length}}">
                    <view wx:for="{{dTitleLeftBtns}}" wx:key="action" class="{{item.icon?('fxui_all '+item.icon):''}}"
                          style="{{item.style}}"
                          data-index="{{index}}" catchtap="onTitleLeftBtnClick">{{item.icon?'':item.label}}
                    </view>
                </view>
                <view class="product-name-container" bindtap="editFields">
                    <text class="product-name">{{dItemObj.productName}}</text>
                </view>
            </view>
            <block wx:for="{{dBellowTitleComs}}" wx:key="name">
                <!-- 更换组合政策按钮 -->
                <change_combine_policy_btn wx:if="{{item.name === 'change_combine_policy_btn'}}" render="{{refreshUI}}" bind:getDataDetail="getItemInfo"/>
            </block>
            <view style="display: flex; flex-direction: column; margin-left: 24rpx; margin-right: 24rpx; margin-bottom: 24rpx">
                <view wx:if="dDisplayFields" class="display-field-container">
                    <text wx:for="{{dDisplayFields}}" style="color: #91959E;font-size: 24rpx;" >{{item?item:'--'}};</text>
                </view>
                <view class="preset-field-container">
                    <view style="display: flex; flex-direction: row; align-items: center">
                        <rich-text nodes="{{dItemObj.salesPrice}}" style="height: 48rpx; padding: 2rpx 0 0 0"></rich-text>
                        <text wx:if="{{dItemObj.isShowPriceBookPrice}}" class="price-book-price">{{dItemObj.priceBookPrice}}</text>
                        <text class="preset-field-value">/{{dItemObj.unitLabel}}</text>
                        <text class="preset-field-value" style="margin: 0 10rpx;">x</text>
                        <text class="preset-field-value">{{dItemObj.quantity}}</text>
                    </view>
                    <text class="preset-field-value">{{dItemObj.subtotal}}</text>
                </view>
                <view wx:if="{{dErrorInfo&&dErrorInfo.msg}}" class="error-container" bindtap="_onErrorClick">
                    <view class="error-label">{{dErrorInfo.msg}}</view>
                    <view class="fxui_all youjiantou" style="color:white;"></view>
                </view>
            </view>
        </view>
    </view>
    <!--底部组件统一通过dFooterComs控制渲染-->
    <block wx:for="{{dFooterComs}}" wx:key="name">
        <!-- Bom树组件 -->
        <form-bom-tree wx:if="{{item.name === 'bom-tree-cmpt'}}" refresh="{{refreshUI}}" bind:getDataDetail="getItemInfo"/>
        <!-- 展示一般明细赠品 -->
        <detail-policy-gift wx:if="{{item.name === 'detail-policy-gift-detail'}}" type="detail" render="{{refreshUI}}" bind:getDataDetail="getItemInfo"/>
        <!-- 展示组合赠品 -->
        <detail-policy-gift wx:elif="{{item.name === 'detail-policy-gift-group'}}" type="group" render="{{refreshUI}}" bind:getDataDetail="getItemInfo"/>
    </block>
    <view class="line" wx:if="{{showLine}}"></view>
</view>