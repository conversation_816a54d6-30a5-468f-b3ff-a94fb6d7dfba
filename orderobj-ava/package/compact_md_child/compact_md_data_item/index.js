import listcdnres from "../../../../business-cmpt-paas-main/utils/listcdnres";
import {convertOneFieldData} from "ava-metadata-lib/formatDefObj";
import {i18n, isEmpty, requireAsync} from "../../../../pluginbase-ava/package/pluginutils";
import dialogset from "../../../../objformmain/dialogset/index";
import salesOrderConfig from "../../hook/SalesOrderConfig";

Component({

    options: {
        addGlobalClass: true
    },

    properties: {
        item: {
            type: Object,
            observer() {
                this.refreshUi()
            }
        },

        showLine: {
            type: Boolean,
            value: true
        }
    },

    data: {
        dErrorInfo: undefined,
        dItemObj: undefined,
        dDisplayFields: undefined,
        refreshUI: {},
    },

    methods: {
        refreshUi() {
            this.triggerEvent("getDataDetail", rst => {
                this._updateComponentData(rst);
            });
        },

        getItemInfo(event) {
            let {detail} = event
            if (detail && typeof detail == 'function') detail(this.data.itemInfo)
        },

        async _updateComponentData(itemInfo) {
            this.data.itemInfo = itemInfo;
            this.fieldChangedDatas = [];
            this.detachFormDelegate();
            let {objectData, objectDescribe, headerComs, footerComs, bellowTitleComs, titleLeftBtns, formContext} = itemInfo || {};
            let {include_fields: showFields, showImage} = this.data.item;
            let displayFields = showFields && showFields.length && showFields.filter(it => {
                let fieldName = it.field_name;
                let includes = (['product_id', 'quantity', 'unit'].includes(fieldName));
                let empty = isEmpty(objectData[fieldName]);
                return !includes && !empty;
            }).map(it => {
                return this.convertValue(objectData, objectDescribe, it.field_name)
            }) || [];
            let {[showImage]: images, product_id__r: productName, dataIndex} = objectData;
            let {signedUrl, path} = images && images[0] || {};
            let imagePath = signedUrl || path;
            let unitLabel = this.convertValue(objectData, objectDescribe, 'unit');
            let actualUnitLabel = this.convertValue(objectData, objectDescribe, 'actual_unit');
            let salesPrice = this.convertValue(objectData, objectDescribe, 'sales_price');
            let quantity = this.convertValue(objectData, objectDescribe, 'quantity');
            let subtotal = this.convertValue(objectData, objectDescribe, 'subtotal');
            let priceBookPrice = this.convertValue(objectData, objectDescribe, 'price_book_price');
            let showUnitLabel = actualUnitLabel || unitLabel || '--';
            let errorInfo = this._getErrorInfo();

            let openMultiCurrency = formContext && formContext.catchRunPluginHookSync('mccurrency.isOpenMultiCurrency.sync', {});
            let mcCurrency = openMultiCurrency ? '' : '¥';
            let salesPriceRichData = this.getRichData(salesPrice, mcCurrency);
            let showSubtotal = `${mcCurrency}${subtotal}`;
            let showPriceBookPrice = `${mcCurrency}${priceBookPrice}`;
            //促销优惠额有值且不为0且不是赠品
            let {policy_dynamic_amount, is_giveaway} = objectData;
            let isShowPriceBookPrice = (!isEmpty(policy_dynamic_amount)) && (policy_dynamic_amount != 0) && (is_giveaway != 1);
            let imagePlaceholder = salesOrderConfig.getImagePlaceholder();
            let renderData = {
                productName: productName || '',
                salesPrice: salesPriceRichData,
                subtotal: showSubtotal,
                quantity: quantity,
                unitLabel: showUnitLabel !== '#%$' ? showUnitLabel : '',
                showImage: !isEmpty(showImage),
                imagePath: imagePath,
                imagePlaceholder: imagePlaceholder || listcdnres.getRes('images/list_default_icon.png'),
                priceBookPrice: showPriceBookPrice,
                isShowPriceBookPrice
            }
            if (formContext) {
                await formContext.catchRunPluginHook("SalesOrderObj.compact.item.render.before", {
                    objApiName: 'SalesOrderProductObj',
                    dataIndex,
                    renderData: renderData
                });
            }
            let updateData = {
                dDisplayFields: displayFields,
                dItemObj: renderData,
                dErrorInfo: errorInfo,
                dHeaderComs: headerComs || null,
                dBellowTitleComs: bellowTitleComs || null,
                dFooterComs: footerComs || null,
                dTitleLeftBtns: titleLeftBtns || null,
                refreshUI: {},
            }
            let formDelegate = await this.getFormDelegate();
            Object.assign(updateData, formDelegate.refreshFormAttr())
            this.setData(updateData);
        },

        editFields() {
            this._showFormPopup({});
        },

        _onErrorClick() {
            if (this.data.dErrorInfo) {
                this._showFormPopup({api_name: this.data.dErrorInfo.api_name})
            }
        },

        async getFormDelegate() {
            if (this.formDelegate == null) {
                let self = this;
                let formAttr = self._getFormAttr();
                let QuickFormDelegate = await requireAsync('../../../../objformpkgbase/base/fields/md/new_md_child/md_data_item/QuickFormDelegate');
                this.formDelegate = new QuickFormDelegate(Object.assign(formAttr, {
                    pageId: self.getPageId(),
                    setData: self.setData.bind(self),
                    getFieldCom: () => {
                    },
                    showFormPopup: self._showFormPopup.bind(self)
                }))
            }
            return this.formDelegate
        },

        detachFormDelegate() {
            if (this.formDelegate) {
                this.formDelegate.detach()
            }
            this.formDelegate = null;//重新创建
        },

        _getFormAttr() {
            let {objectData, getFormAttr, errorMsgs} = this.data.itemInfo;
            return getFormAttr(objectData, errorMsgs)
        },

        _showFormPopup(opt = {}) {
            let self = this;
            let formAttr = Object.assign(this._getFormAttr(), {})
            let {objectDataList, getAllErrorMsgs, onFieldChanged, refreshDataItemUi} = this.data.itemInfo;
            dialogset.formpopup.show({
                objectDataList,//当前业务类型的所有数据引用
                getAllErrorMsgs,//所有数据错误提示
                focusField: opt.api_name,//自动定位到指定字段
                formAttr,
                onClose() {
                    if (!isEmpty(self.fieldChangedDatas)) {
                        refreshDataItemUi && refreshDataItemUi(self.fieldChangedDatas)
                    }
                },

                getTitle(objData) {
                    return self._getTitle(objData)
                },

                getTitlePlaceHolder(objData) {
                    let field = self._getTitleField(objData)
                    return self._getTitleFieldLabel(field)
                },

                onFieldChanged(opt) {
                    if (!isEmpty(opt.dataIndex) && !self.fieldChangedDatas.includes(opt.dataIndex)) {
                        self.fieldChangedDatas.push(opt.dataIndex)
                    }
                    if (onFieldChanged) {//临时处理依赖字段改变刷新ui的场景
                        onFieldChanged && onFieldChanged(opt)
                    }
                }
            })
        },

        _getTitleField() {
            let {objectDescribe, title_field, layoutFields} = this.data.itemInfo;
            let titleFieldApiName = layoutFields[title_field] ? title_field : "name";
            return objectDescribe.fields[titleFieldApiName];
        },

        _getTitle(objectData) {
            let field = this._getTitleField(objectData);
            if (!field) {
                return ''
            }
            let {layoutFields, objectDescribe} = this.data.itemInfo;
            if (layoutFields[field.api_name]) {
                field = layoutFields[field.api_name]
            }
            return this.convertValue(objectData, objectDescribe, field.api_name);
        },

        _getTitleFieldLabel(field) {
            if (field) {
                let {layoutFields} = this.data.itemInfo;
                return (layoutFields[field.api_name] && layoutFields[field.api_name].label) || field.label
            }
        },

        _getErrorInfo() {
            let {errorMsgs, layoutFields} = this.data.itemInfo;
            if (!isEmpty(errorMsgs)) {
                let rst = null;
                for (let k in errorMsgs) {
                    if (errorMsgs[k]) {
                        let f = layoutFields[k]
                        if (f) {
                            rst = {api_name: k, msg: i18n("ava.objform.errorandmodify", [f.label])}
                            break
                        }
                    }
                }
                return rst;
            }
            return null;
        },

        convertValue(objectData, objectDescribe, fieldName, defaultValue = '') {
            let fieldDesc = objectDescribe.fields[fieldName];
            return convertOneFieldData(objectData, fieldDesc, defaultValue);
        },

        getRichData(fieldValue, mcCurrency) {
            let richData = [];
            if (!isEmpty(fieldValue)) {
                let integerValue;
                let decimalValue;
                let values = `${fieldValue}`.split('.');
                if (values && values.length > 1) {
                    integerValue = values[0];
                    decimalValue = values[1];
                } else {
                    integerValue = fieldValue;
                }
                if (!isEmpty(mcCurrency)) {
                    richData.push(this.newSpan({text: `${mcCurrency}`, fontSize: 12, fontWeight: 700}));
                }
                if (!isEmpty(integerValue)) {
                    richData.push(this.newSpan({text: `${integerValue}`, fontSize: 14, fontWeight: 700}));
                }
                if (!isEmpty(decimalValue)) {
                    richData.push(this.newSpan({text: `.${decimalValue}`, fontSize: 12, fontWeight: 700}));
                }
            }
            return richData;
        },

        newSpan(param) {
            let {text = '', fontSize = 16, color = '#FF522A', fontWeight = 'normal', textDecoration = 'none'} = param;
            return {
                name: "span",
                attrs: {style: `font-size: ${fontSize}px; color: ${color}; font-weight: ${fontWeight}; text-decoration:${textDecoration}`},
                children: [{text: `${text}`, type: 'text'}]
            }
        },

        onTitleLeftBtnClick(e) {
            let {index} = e.currentTarget.dataset;
            let titleLeftBtns = this.data.dTitleLeftBtns;
            let btn = titleLeftBtns && titleLeftBtns[index]
            btn && btn.onClick && btn.onClick()
        },
    }
});