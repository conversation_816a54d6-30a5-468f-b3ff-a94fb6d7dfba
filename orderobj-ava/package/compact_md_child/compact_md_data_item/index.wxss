.root {
  display: flex;
  flex-direction: column;
}

.content-container {
  display: flex;
  flex-direction: row;
}

.product-img {
  width: 64px;
  height: 64px;
  margin-top: 12px;
  margin-bottom: 10px;
  margin-left: 12px;
  border-radius: 3px;
  overflow: auto;
}

.field-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-name-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  color: #181c25;
  max-width: 480rpx;
}

.preset-field-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.preset-field-value{
  font-size: 28rpx;
  color: #181C25;
}

.arrow-down {
  width: 10rpx;
  height: 10rpx;
  border-bottom: 2rpx solid #91959e;
  border-right: 2rpx solid #91959e;
  transform: rotate(45deg);
  margin-right: 10rpx;
}

.error-container {
  background-color: var(--error);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-radius: 2px;
}

.error-label{
  word-break: break-word;
  font-size: 24rpx;
  line-height: 44rpx;
  color: white;
}

.display-field-container{
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.line{
  margin-left: 12px;
  height: 1px;
  background-color: var(--line);
}

.price-book-price{
  color: #91959E;
  font-size: 28rpx;
  font-weight: 400;
  text-decoration: line-through;
  margin-left: 8rpx;
}