<wxs module="utils" src="../../../objformmain/base/fields/md/new_md_child.wxs">
</wxs>
<mdchildcontainer hidden="{{dHidden}}" type="{{headerType}}" title="{{recordTypeLabel}}"
                  buttons="{{dBtns}}" collapse="{{dCollapseState}}"
                  hideButtons="{{utils.hideBarBtns({dSimplePageList,dBtns})}}">
    <!-- 数据区域 -->
    <block wx:if="{{dShowList&&dShowList.length}}">
        <block wx:if="{{dHorizontal}}">
            <view style="display: flex; flex-direction: row; align-items: center; padding: 8rpx 24rpx;">
                <scroll-view scroll-x enhanced show-scrollbar="{{false}}" scroll-with-animation="{{true}}" class="scroll">
                    <fs-image wx:for="{{dImageDataList}}" class="product-img" src='{{item}}'
                              imgStyle="width: 64px;height: 64px;" placeholder="{{dPlaceholderImage}}"></fs-image>
                </scroll-view>
                <view style="display: flex; flex-direction: row; margin-left: 24rpx; align-items: center" catchtap="onExpandClick">
                    <text style="font-size: 28rpx; color: #181c25">{{dHorizontalCount}}</text>
                    <view class="fxui_all xialasanjiao" style="margin-top: 6rpx"></view>
                </view>
            </view>
        </block>
        <block wx:else>
            <view style="display: flex; flex-direction: column">
                <block wx:for="{{dShowList}}" wx:key="dataIndex">
                    <slideview hidden="{{dHidden}}" disable="{{dBatchOpMode||dReadOnlyDetail}}" buttons="{{utils.dataBtns({dSlideBtns,dHideDataBtn,item})}}" data-index="{{index}}" bindbuttontap="_onSlideBtnClick">
                        <view class="item-container">
                            <view wx:if="{{dBatchOpMode}}"
                                  class="fxui_all check-icon{{dCheckedDataIndexs[item.dataIndex]?'-checked xuanzhong':' weixuanzhong'}}"
                                  data-index="{{index}}"
                                  bindtap="_onCheckBoxClick"></view>
                            <view style="flex:1;">
                                <!-- 数据视图 -->
                                <object-item id="{{'data'+item.dataIndex}}" item="{{item}}" showLine="{{index!=dShowList.length-1}}" data-index="{{index}}" bind:getDataDetail="getMdDataItemDetail">
                                </object-item>
                            </view>
                        </view>
                    </slideview>
                </block>
                <view wx:if="{{dEnableExpandAction}}" class="expand-container" catchtap="onExpandClick">
                    <text class="expand-text">{{dExpandText}}</text>
                    <view class="{{dIsExpand?'arrow-up':'arrow-down'}}"></view>
                </view>
            </view>
        </block>
    </block>
    <!-- 无数据视图 -->
    <view wx:else>
        <view wx:if="{{!dReadOnlyDetail&&dShowAddBtn}}" bindtap="_onAddBtnClick" class="btn-wrap">
            <view class="r-btn"><view class="fxui_all jiaqian add-ic"></view>{{displayName}}</view>
        </view>
        <imageholder wx:elif="{{!dSimplePageList||!dSimplePageList.length}}" type="nodata"/>
    </view>
</mdchildcontainer>