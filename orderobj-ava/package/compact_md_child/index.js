import new_md_child_b from "../../../objformmain/base/fields/md/new_md_child_b";
import listcdnres from "../../../business-cmpt-paas-main/utils/listcdnres";
import {i18n, isEmpty} from "../../../pluginbase-ava/package/pluginutils";
import salesOrderConfig from "../hook/SalesOrderConfig";

Component({

    options: {
        addGlobalClass: true
    },

    behaviors: [new_md_child_b],

    data: {
        pageSize: 1000,
        dEnableExpandAction: false,//是否支持展开收起操作
        dExpandText: undefined,//展开收起文案
        dIsExpand: false,//是否是展开状态
        dShowList: undefined,//展示的数据列表
        dHorizontal: false,//是否水平展示，收起并且展示图片时水平展示
        dHorizontalCount: undefined,
        dPlaceholderImage: listcdnres.getRes('images/list_default_icon.png'),
        dImageDataList: undefined,
    },

    methods: {

        _getExtraResetData(simplePageList) {
            return this.getRenderData(this.data.dIsExpand);
        },

        onExpandClick() {
            let {dIsExpand: isExpand, formContext, apiName} = this.data;
            formContext && formContext.selectNavTab && formContext.selectNavTab(apiName);
            let showData = this.getRenderData(!isExpand);
            this.setData(Object.assign({dIsExpand: !isExpand}, showData))
        },

        _onBatchBtnClick() {
            let {dIsExpand: isExpand, dBatchOpMode} = this.data;
            (!isExpand) && this.onExpandClick();//如果是收起态，则展开
            this._switchBatchOpMode && this._switchBatchOpMode(!dBatchOpMode)
        },

        getRenderData(isExpand) {
            let showObjDataList = this.showObjDataList || [];
            let count = showObjDataList.length || 0;
            let showDataList = (isExpand || (showObjDataList.length <= 3)) ? showObjDataList : showObjDataList.slice(0, 3);
            let {component} = this.data.dConfig || {};
            let {include_fields, show_image} = component || {};
            showDataList && showDataList.length && showDataList.forEach(item => {
                Object.assign(item, {showImage: show_image, include_fields});
            });
            let imagePlaceholder = salesOrderConfig.getImagePlaceholder();
            let imageDataList = show_image ? showDataList.map(it => {
                let {[show_image]: images} = it;
                let {signedUrl, path} = images && images[0] || {};
                return signedUrl || path;
            }) : [];
            let horizontal = !isExpand && show_image && (count !== 1);
            return Object.assign({
                dEnableExpandAction: count > 3,//超过三条添加展开收起操作
                dExpandText: isExpand ? i18n('ava.object_form.order.order_product.collapse_up')/*向上收起*/
                    : i18n('ava.object_form.order.order_product.expand_products', [count])/*展开共{0}件产品*/,
                dShowList: showDataList || [],
                dHorizontal: horizontal,
                dHorizontalCount: i18n('ava.object_list.form_pop.total_str', [count])/*共{0}件*/,
            }, horizontal && {
                dImageDataList: imageDataList
            }, (!isEmpty(imagePlaceholder)) && {
                dPlaceholderImage: imagePlaceholder
            })
        },
    }
});