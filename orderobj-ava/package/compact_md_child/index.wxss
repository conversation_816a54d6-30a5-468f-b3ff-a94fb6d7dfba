@import "../../../objformmain/base/fields/md/new_md_child.wxss";

.expand-container{
    display: flex;
    flex-direction: row;
    padding-top: 20rpx;
    padding-bottom: 10rpx;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.expand-text{
    font-size: 28rpx;
    color: #91959e;
    margin-right: 10rpx
}

.arrow-down {
    width: 10rpx;
    height: 10rpx;
    border-bottom: 2rpx solid #91959e;
    border-right: 2rpx solid #91959e;
    transform: rotate(45deg);
}

.arrow-up{
    width: 10rpx;
    height: 10rpx;
    border-top: 2rpx solid #91959e;
    border-left: 2rpx solid #91959e;
    transform: rotate(45deg);
    margin-top: 10rpx;
}

.scroll {
    display: inline-block;
    overflow-x: auto;
    white-space: nowrap;
    flex: 1;
}

.product-img{
    border-radius: 3px;
    width:64px;
    height:64px;
    overflow: hidden;
    margin-right: 20rpx;
}