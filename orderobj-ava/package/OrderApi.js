import {request} from "../../pluginbase-ava/package/pluginutils";

export default class OrderApi {
    constructor(http) {
        this.http = http;
    }

    getDefaultAccountAddr(associateDataId, associateApiName, wheres, objectData) {
        if (!associateDataId) {
            return Promise.resolve();
        }
        let associatedApiName = 'AccountAddrObj';
        let associatedFieldListName = 'account_accountaddr_list';
        return request(this.http, {
            url: `FHE/EM1ANCRM/API/v1/object/${associatedApiName}/controller/RelatedList`,
            data: {
                associated_object_describe_api_name: associatedApiName,
                associated_object_field_related_list_name: associatedFieldListName,
                include_associated: true,
                search_query_info: JSON.stringify(Object.assign({
                    "limit": 1000,
                    "offset": 0,
                    filters: [{
                        field_name: "is_ship_to_add",
                        field_values: [true],
                        operator: 'EQ'

                    }],
                    wheres: (wheres && wheres.length) ? wheres : [{
                        filters: [{
                            field_name: 'account_id',
                            field_values: [associateDataId],
                            operator: 'EQ'
                        }]
                    }]
                })),
                object_data: objectData
            },
            cacheRule: {type: "app"}
        }).then(res => {
            if (res && res.dataList) {
                return res.dataList.find(item => {
                    return item && item.is_ship_to_add;
                })
            }
            return undefined;
        });
    }

    queryUpValid(accountId) {
        if (!accountId) {
            return Promise.resolve();
        }
        return request(this.http, {
            url: `FHE/EM1ANCRM/API/v1/object/warehouse/service/query_up_valid`,
            data: {accountId},
            cacheRule: {type: "app"}
        }).then(res => {
            let id = res && res.result && res.result.defaultId;
            let name = res && res.result && res.result.defaultName;
            return {id, name};
        });
    }

    validateAccountPriceBook(params) {
        return request(this.http, {
            url: 'FHE/EM1ANCRM/API/v1/object/pricebook/service/validate_account_pricebook',
            data: params
        }).then(res => {
            return res && res.result;
        })
    }
}