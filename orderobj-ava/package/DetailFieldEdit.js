import {BackCalculation} from "./BackCalculation";

export class DetailFieldEdit {
    constructor(context) {
        this.backCalculation = new BackCalculation(context);
    }

    detailFieldEditAfter(pluginExecResult, options) {
        let {fieldName} = options;
        if (fieldName === 'sales_price') {
            return this.backCalculation.salesPriceEditAfter(options);
        } else if (fieldName === 'subtotal') {
            return this.backCalculation.subtotalEditAfter(options);
        }
    }
}