import pass_api from "../../business-cmpt-paas-main/paasapi";
import {i18n, isEdit} from "../../pluginbase-ava/package/pluginutils";

export class OrderCommiter {

    constructor(context) {
        let {bizStateConfig, pluginApi} = context;
        this.bizStateConfig = bizStateConfig;
        this.pluginApi = pluginApi;
        this.customerAccountInfo = null;//客户账户信息
        this.addObjectData = null;//新建时，入参中的object_data
    }

    formSubmitBefore(pluginExecResult, options) {
        let {skipAllValidate, optionInfo, object_data, dataGetter} = options;
        if (optionInfo) {
            Object.assign(optionInfo, {useParamsIdempotent: !skipAllValidate});
        }
        let sourceAction = dataGetter.getSourceAction && dataGetter.getSourceAction();
        if (!isEdit(sourceAction)) {
            this.addObjectData = object_data;
            this.customerAccountInfo = object_data && object_data.key_customer_account_info;
        }
    }

    formSubmitPostAfter(pluginExecResult, options) {
        return this.checkParamsIdempotent(pluginExecResult, options)
            .then(rst => {
                return rst && rst.doParamsIdempotent ? rst : this.handleCustomerAccountLogic(pluginExecResult, options)
            });
    }

    formSubmitActionBefore(pluginExecResult, options) {
        let {account_id, account_id__r, settle_type} = this.addObjectData || {};
        let openCustomerAccount = this.bizStateConfig.isOpenCustomerAccount();
        let isPrepay = settle_type == '1';
        let needCreatePayment = this.customerAccountInfo && this.customerAccountInfo.needCreatePayment;
        let createPayment = openCustomerAccount && isPrepay && needCreatePayment;//新建订单后是否新建回款
        if (createPayment) {
            let {_id, name} = options.result && options.result.objectData || {};
            pass_api.openObjectForm({
                action: "Add",
                objectApiName: 'PaymentObj',
                backFill: {
                    account_id: {readOnly: true, value: {objectData: {_id: account_id, name: account_id__r}}},
                    order_id: {readOnly: true, value: {objectData: {_id: _id, name: name}}}
                },
                kPushDetailAfterCreateSuccess: true,
                kShowCreateObjetAfterPost: false,
                onSuccess(rst) {

                }
            });
            return {consumed: true}
        }
    }

    checkParamsIdempotent(pluginExecResult, options) {
        let data = options.postResult && options.postResult.Value;
        let {match, nonBlockMessages} = data && data.paramsIdempotentMessage || {};
        if (!match) {
            return Promise.resolve();
        }
        if (!nonBlockMessages || !nonBlockMessages.length) {
            return Promise.resolve();
        }
        return this.pluginApi.confirmPromise(null, nonBlockMessages.join("; "))
            .then(rst => {
                if (rst) {
                    Object.assign(options.postParams.data.optionInfo, {useParamsIdempotent: false});
                    return {submitAgain: true, doParamsIdempotent: true};
                } else {
                    return {consumed: true, doParamsIdempotent: true};
                }
            })
    }

    handleCustomerAccountLogic(pluginExecResult, options) {
        let {postParams, postResult} = options;
        let failureCode = postResult.Result && postResult.Result.FailureCode;
        let FailureMessage = postResult.Result && postResult.Result.FailureMessage;
        if (failureCode == ********) {//客户账户余额不足
            let createOrderLimitConfig = this.customerAccountInfo && this.customerAccountInfo.createOrderLimitConfig;
            if (createOrderLimitConfig == 1) {//终止操作
                this.pluginApi.showToast(i18n('ava.object_form.order.account_balance_insufficient')/*客户账户余额不足*/);
                return Promise.resolve({consumed: true})
            } else if (createOrderLimitConfig == 2) {//预警提示
                return this.pluginApi.confirmPromise(null, FailureMessage || i18n('ava.object_form.order.account_balance_insufficient_tip')/*'该客户的可用账户余额不足，确认继续提交订单吗？'*/)
                    .then(rst => {
                        if (rst) {
                            postParams.data && postParams.data.object_data && (postParams.data.object_data.IsForceCommit = true);//强制提交订单，添加的参数key
                            return {submitAgain: true};
                        } else {
                            return {consumed: true};
                        }
                    });
            }
        }
        return Promise.resolve();
    }
}