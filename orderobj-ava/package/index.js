import BizStateConfig from "../../pluginbase-ava/package/BizStateConfig";
import PluginApi from "../../pluginbase-ava/package/PluginApi";
import OrderApi from "./OrderApi";
import {OrderCommiter} from "./OrderCommiter";
import {FormRender} from "./FormRender";
import {MasterFieldEdit} from "./MasterFieldEdit";
import {DetailFieldEdit} from "./DetailFieldEdit";
import {MDRender} from "./MDRender";
import MdBatchAdd from "./MdBatchAdd"
import log from "../../pluginbase-ava/package/log";
import objPluginChecker from "../../pluginbase-ava/package/objPluginChecker";
import salesOrderConfig from "./hook/SalesOrderConfig";

export default class OrderObj {

    constructor(pluginService, pluginParam) {
        let {bizStateConfig, describe} = pluginParam || {};
        log.tickPluginUsed(describe);
        this.bizStateConfig = new BizStateConfig(bizStateConfig, pluginService.api.getPlugins());
        let context = {
            bizStateConfig: this.bizStateConfig,
            pluginApi: new PluginApi(pluginService),
            requestApi: new OrderApi(pluginService.api.request),
        }
        this.orderCommiter = new OrderCommiter(context);
        this.formRender = new FormRender(context);
        this.masterFieldEdit = new MasterFieldEdit(context);
        this.detailFieldEdit = new DetailFieldEdit(context);
        this.mdRender = new MDRender(context);
        this.mdBatchAdd = new MdBatchAdd({...context, pluginParam });
        salesOrderConfig.initContext(context);
    }

    pluginServiceUseAfter(pluginExecResult, options) {
        let {plugins, ignore} = options;
        // TODO: mdBatchAdd初始化proxy
        let result = objPluginChecker.check('SalesOrderObj', plugins, ignore);
        if (!result) {//检查不通过，则拦截页面加载
            return false;
        }
    }
    
    formFetchDescribeLayoutAfter(pluginExecResult, options) {
        return this.formRender.formFetchDescribeLayoutAfter(pluginExecResult, options);
    }

    async formRenderBefore(pluginExecResult, options) {
        await salesOrderConfig.formRenderBefore(pluginExecResult, options);
        return this.formRender.formRenderBefore(pluginExecResult, options);

    }

    formRenderAfter(pluginExecResult, options) {
        return this.formRender.formRenderAfter(pluginExecResult, options);
    }

    formRenderEnd(pluginExecResult, options) {
        return this.formRender.formRenderEnd(pluginExecResult, options);
    }

    formSubmitBefore(pluginExecResult, options) {
        return this.orderCommiter.formSubmitBefore(pluginExecResult, options);
    }

    formSubmitPostAfter(pluginExecResult, options) {
        return this.orderCommiter.formSubmitPostAfter(pluginExecResult, options);
    }

    formSubmitActionBefore(pluginExecResult, options) {
        return this.orderCommiter.formSubmitActionBefore(pluginExecResult, options)
    }

    fieldEditAfter(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditAfter(pluginExecResult, options);
        } else {
            return this.detailFieldEdit.detailFieldEditAfter(pluginExecResult, options);
        }
    }

    fieldEditEnd(pluginExecResult, options) {
        let {masterObjApiName, objApiName} = options;
        if (objApiName === masterObjApiName) {
            return this.masterFieldEdit.masterFieldEditEnd(pluginExecResult, options);
        }
    }

    mdRenderBefore(pluginExecResult, options) {
        return this.mdRender.mdRenderBefore(pluginExecResult, options);
    }

    mdItemRenderBeforeSync(pluginExecResult, options) {
        return this.mdRender.itemRenderBeforeSync(pluginExecResult, options);
    }

    mdBatchAddBefore(pluginExecResult, options) {
        return this.mdBatchAdd.beforeBatchAddDetailAction(pluginExecResult, options)
    }

    mdBatchAddAfter(pluginExecResult, options) {
        return this.mdBatchAdd.afterBatchAddDetailAction(pluginExecResult, options)
    }

    mdCloneAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdCloneAfter(pluginExecResult, options);
    }

    mdDeleteAfter(pluginExecResult, options) {
        return this.mdBatchAdd.mdDeleteAfter(pluginExecResult, options);
    }

    customlizeRealPriceParams(pluginExecResult, options) {
        let { preData } = pluginExecResult || {}
        let { type, objApiName, detailData } = options || {}
        if (!objApiName || objApiName !== 'SalesOrderProductObj') return preData
        if (['copy', 'clone', 'mapping', 'convert'].indexOf((type || '').toLowerCase()) < 0) return preData
        if (!this.formRender.bizStateConfig.isOpenSaleContractConstraint()) return preData
        // 开启销售合同约束(isOpenSaleContractConstraint)，并且是复制或转换时，将sale_contract_line_id传入取价接口入参
        return Object.assign({}, preData, { saleContractLineId: detailData && detailData.sale_contract_line_id })
    }

    getBizStateConfig() {
        return this.bizStateConfig;
    }

    apply() {
        return [
            {
                event: 'pluginService.use.after',
                functional: this.pluginServiceUseAfter.bind(this)
            }, {
                event: "form.fetchDescribeLayout.after",
                functional: this.formFetchDescribeLayoutAfter.bind(this)
            }, {
                event: "form.render.before",
                functional: this.formRenderBefore.bind(this)
            }, {
                event: "form.render.after",
                functional: this.formRenderAfter.bind(this)
            }, {
                event: 'form.render.end',
                functional: this.formRenderEnd.bind(this)
            },{
                event: "form.submit.before",
                functional: this.formSubmitBefore.bind(this)
            }, {
                event: "form.submit.post.after",
                functional: this.formSubmitPostAfter.bind(this)
            }, {
                event: "form.submit.action.before",
                functional: this.formSubmitActionBefore.bind(this)
            }, {
                event: "field.edit.after",
                functional: this.fieldEditAfter.bind(this)
            }, {
                event: "field.edit.end",
                functional: this.fieldEditEnd.bind(this)
            }, {
                event: "md.batchAdd.before",
                functional: this.mdBatchAddBefore.bind(this)
            }, {
                event: "md.batchAdd.after",
                functional: this.mdBatchAddAfter.bind(this)
            }, {
                event: "md.clone.after",
                functional: this.mdCloneAfter.bind(this)
            }, {
                event: "md.del.after",
                functional: this.mdDeleteAfter.bind(this)
            },{
                event: "md.render.before",
                functional: this.mdRenderBefore.bind(this)
            }, {
                event: "md.item.render.before.sync",
                functional: this.mdItemRenderBeforeSync.bind(this)
            }, {
                event: "price-service.form.parseFullProduct.sync",
                functional: this.customlizeRealPriceParams.bind(this)
            }, {
                event: "SalesOrderObj.getBizStateConfig",
                functional: this.getBizStateConfig.bind(this)
            }
        ]
    }
}